module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        extensions: [
          '.ios.js',
          '.android.js',
          '.ios.jsx',
          '.android.jsx',
          '.js',
          '.jsx',
          '.json',
          '.ts',
          '.tsx',
        ],
        alias: {
          '@atoms': './src/atoms',
          '@analytics': './src/analytics',
          '@api': './src/api/',
          '@assets': './src/assets',
          '@components': './src/components',
          '@graphQL': './src/graphQL',
          '@hooks': './src/hooks',
          '@lib': './src/lib',
          '@performance': './src/performance',
          '@screen': './src/screens',
          '@store': './src/store',
          '@graphType': './src/types/graphQLTypes',
          '@utils': './src/utils',
          '@theme': './src/theme',
          '@apolloClient': './src/apollo',
          '@dev': './src/dev',
          '@typesense': './src/typesense',
          '@modals': './src/modals',
          '@config': './src/environmentConfig',
          '@': './src',
        },
      },
    ],
    'react-native-reanimated/plugin',
    'react-native-paper/babel',
  ],
};
