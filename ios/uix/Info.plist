<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
	 	<key>CodePushDeploymentKey</key>
		<string>wTgQcbgbs95hLW0nNZOBLroV4Vp7N1yqQSHezl</string>
		<key>CodePushServerURL</key>
		<string>https://codepush.appsonair.com</string>
		<key>MoEngageAppDelegateProxyEnabled</key>
		<false />
		<key>ITSAppUsesNonExemptEncryption</key>
		<false />
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>itms-apps</string>
		</array>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>UIX</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>1.3.5</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>uix</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>uix</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>75</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<false />
			<key>NSAllowsLocalNetworking</key>
			<true />
		</dict>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string></string>
		<key>UIAppFonts</key>
		<array>
			<string>BPdotsUnicaseSquare-Bold.otf</string>
			<string>BPdotsUnicaseSquare-Light.otf</string>
			<string>BPdotsUnicaseSquare.otf</string>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>BootSplash</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>UIFileSharingEnabled</key>
		<true />
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true />
		<key>NSDocumentsFolderUsageDescription</key>
		<string>We need access to your documents to upload your resume.</string>
	</dict>
</plist>