<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController modalTransitionStyle="crossDissolve" id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" autoresizesSubviews="NO" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" image="SplashBG" translatesAutoresizingMaskIntoConstraints="NO" id="RKB-pF-3Tn">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Logo" translatesAutoresizingMaskIntoConstraints="NO" id="GPY-py-CNH">
                                <rect key="frame" x="155.5" y="321.5" width="64" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="64" id="2ZB-kK-4Ny"/>
                                    <constraint firstAttribute="height" constant="24" id="xQX-o7-6sG"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" name="BootSplashBackground-a34dab"/>
                        <constraints>
                            <constraint firstItem="RKB-pF-3Tn" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="CJH-LK-YQ3"/>
                            <constraint firstItem="GPY-py-CNH" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="HOh-nx-RWD"/>
                            <constraint firstItem="GPY-py-CNH" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="TZX-tv-KPu"/>
                            <constraint firstAttribute="trailing" secondItem="RKB-pF-3Tn" secondAttribute="trailing" id="qbA-py-d6U"/>
                            <constraint firstItem="RKB-pF-3Tn" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="sne-Qq-I5k"/>
                            <constraint firstAttribute="bottom" secondItem="RKB-pF-3Tn" secondAttribute="bottom" id="wEK-fK-MDV"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-0.80000000000000004" y="-0.44977511244377816"/>
        </scene>
    </scenes>
    <resources>
        <image name="Logo" width="99.5" height="40"/>
        <image name="SplashBG" width="780" height="1688"/>
        <namedColor name="BootSplashBackground-a34dab">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
