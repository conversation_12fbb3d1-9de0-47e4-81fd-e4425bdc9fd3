{"extends": "@react-native/typescript-config/tsconfig.json", "exclude": ["node_modules"], "include": ["**/*.ts", "**/*.tsx"], "files": ["./node_modules/redux-persist/types/integration/react.d.ts"], "compilerOptions": {"allowJs": true, "baseUrl": ".", "paths": {"@analytics/*": ["src/analytics/*"], "@api/*": ["src/api/*"], "@assets/*": ["src/assets/*"], "@atoms/*": ["src/atoms/*"], "@components/*": ["src/components/*"], "@graphQL/*": ["src/graphQL/*"], "@hooks/*": ["src/app/hooks/*"], "@lib/*": ["src/lib/*"], "@performance/*": ["src/performance/*"], "@screen/*": ["src/screens/*"], "@store/*": ["src/store/*"], "@utils/*": ["src/utils/*"], "@dev/*": ["src/dev/*"], "@theme/*": ["src/theme/*"], "@apolloClient/*": ["src/app/apollo/*"], "@typesense/*": ["src/typesense/*"], "@modals/*": ["src/modals/*"], "@/*": ["src/*"], "@config": ["src/environmentConfig"]}}}