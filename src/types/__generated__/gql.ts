/* eslint-disable */
import * as types from './graphql';



/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  query AssignedBadgeQuery($id: uuid!) {\n    members_member_badges(where: {badge_id: {_eq: $id}}) {\n      created_at\n      member_detail {\n        alias\n      }\n    }\n  }\n": typeof types.AssignedBadgeQueryDocument,
    "\n  query GetBadgeDetails {\n    badges_badge_details {\n      badge_description\n      badge_media\n      badge_name\n      id\n    }\n  }\n": typeof types.GetBadgeDetailsDocument,
    "\n  query DropsList($limit: Int!, $offset: Int!) {\n    drops_drop_details(\n      order_by: {expires_at: desc}\n      limit: $limit\n      offset: $offset\n    ) {\n      id\n      type\n      drop_summary\n      expires_at\n      title\n      media_url\n      created_by\n      created_at\n      surge\n      engagement_type\n      drop_members {\n        member_id\n      }\n    }\n  }\n": typeof types.DropsListDocument,
    "\n  query DropById($dropId: uuid!) {\n    drops_drop_details(where: {id: {_eq: $dropId}}) {\n      id\n      type\n      drop_summary\n      expires_at\n      title\n      media_url\n      created_by\n      created_at\n      surge\n      engagement_type\n      drop_members {\n        member_id\n      }\n    }\n  }\n": typeof types.DropByIdDocument,
    "\n  mutation AddDropInterest($drop_id: uuid!, $member_id: uuid!) {\n    insert_drops_drop_members_one(\n      object: {drop_id: $drop_id, interest_type: 1, member_id: $member_id}\n    ) {\n      id\n    }\n  }\n": typeof types.AddDropInterestDocument,
    "\n  query DropInterestQuery($drop_id: uuid!, $member_id: uuid!) {\n    drops_drop_members(\n      where: {drop_id: {_eq: $drop_id}, member_id: {_eq: $member_id}}\n    ) {\n      drop_id\n      member_id\n      interest_type\n    }\n  }\n": typeof types.DropInterestQueryDocument,
    "\n  query HomeQuery(\n    $joiningStartTime: date!\n    $member_id: uuid\n    $dropDetailcurrentDate: timestamptz!\n  ) {\n    members_member_details(where: {_and: [{id: {_eq: $member_id}}]}) {\n      id\n      alias\n      bio\n      member_photo\n      member_badges {\n        badge_id\n        badge_detail {\n          badge_name\n          badge_media\n        }\n      }\n      member_status\n      type {\n        id\n        name\n      }\n    }\n\n    recently_joined_members: members_member_details(\n      where: {\n        _and: [\n          {joining_date: {_gte: $joiningStartTime}}\n          {onboarded: {_eq: true}}\n        ]\n      }\n    ) {\n      id\n      alias\n    }\n\n    drops_drop_details_aggregate(\n      where: {_and: [{expires_at: {_gt: $dropDetailcurrentDate}}]}\n    ) {\n      aggregate {\n        count\n      }\n    }\n\n    app_ui_homepage_banners {\n      id\n      banner_color\n      banner_description\n      banner_order\n      banner_title\n      action_type\n      action_url\n    }\n\n    stories_story_groups {\n      id\n      title\n      story_details(order_by: [{story_order: asc}]) {\n        id\n        cover_media\n        created_at\n        link\n        title\n        media_type\n        duration\n        isReadMore\n        isSeen\n        story_group_id\n        showOverlay\n        story_group {\n          id\n          title\n        }\n        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {\n          aggregate {\n            count\n          }\n        }\n      }\n    }\n  }\n": typeof types.HomeQueryDocument,
    "\n  query HomeStoriesQuery($member_id: uuid!) {\n    stories_story_groups {\n      id\n      title\n      story_details(order_by: [{story_order: asc}]) {\n        id\n        cover_media\n        created_at\n        link\n        title\n        media_type\n        duration\n        isReadMore\n        isSeen\n        story_group_id\n        showOverlay\n        story_group {\n          id\n          title\n        }\n        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {\n          aggregate {\n            count\n          }\n        }\n      }\n    }\n  }\n": typeof types.HomeStoriesQueryDocument,
    "\n  mutation markSeen($member_id: uuid!, $story_id: uuid!) {\n    insert_members_member_stories_one(\n      object: {member_id: $member_id, story_id: $story_id}\n    ) {\n      id\n    }\n  }\n": typeof types.MarkSeenDocument,
    "\n  query LinkedinSearch($search: String!) {\n    LinkedinSearch(arg1: {search: $search}) {\n      status\n      message\n      data {\n        name\n        photo\n        headline\n        profile_url\n        location\n      }\n    }\n  }\n": typeof types.LinkedinSearchDocument,
    "\n  query MemberDetails($id: uuid!) {\n    GetRevenueGraphDataForApp(arg1: {member_id: $id}) {\n      status\n      message\n      data {\n        slot\n        label\n        hours\n        revenue\n      }\n    }\n    members_member_details_by_pk(id: $id) {\n      alias\n      bio\n      department\n      designation\n      created_at\n      first_name\n      id\n      last_name\n      level\n      linkedin_profile\n      member_badges {\n        badge_detail {\n          badge_description\n          badge_name\n          badge_media\n          id\n        }\n      }\n      member_bank_details {\n        account_number\n        bank_account_name\n        ifsc_code\n      }\n      member_engagement\n      member_photo\n      member_projects {\n        project_detail {\n          cover_image\n          project_name\n          project_logo\n          project_description\n          id\n        }\n      }\n      member_role\n      member_skills {\n        id\n        rating\n        skill_detail {\n          title\n          type\n          id\n        }\n      }\n      member_retainers {\n        id\n        hours\n        days\n        retainer_type\n        member_retainer_type {\n          name\n        }\n      }\n      member_status\n      member_type\n      pan_card_link\n      personal_email\n      phone_number\n      referred_by\n      resume_link\n      salary\n      uix_email\n      updated_at\n      joining_date\n      post_details {\n        post_content\n        post_subject\n        posted_by\n        type\n        id\n      }\n      member_projects_aggregate(where: {member_id: {_eq: $id}}) {\n        aggregate {\n          count\n        }\n      }\n      member_projects {\n        project_detail {\n          cover_image\n          project_name\n          project_logo\n          project_description\n          id\n        }\n      }\n      lead_dealmaker_hunters_aggregate(where: {member_id: {_eq: $id}}) {\n        aggregate {\n          count\n        }\n      }\n    }\n  }\n": typeof types.MemberDetailsDocument,
    "\n  query MemberProject($id: uuid!) {\n    members_member_projects(where: {member_id: {_eq: $id}}) {\n      project_detail {\n        project_name\n        id\n        project_logo\n      }\n    }\n  }\n": typeof types.MemberProjectDocument,
    "\n  mutation MemberAvailabilityStatusChange($id: uuid!, $member_status: Int) {\n    update_members_member_details_by_pk(\n      pk_columns: {id: $id}\n      _set: {member_status: $member_status}\n    ) {\n      id\n    }\n  }\n": typeof types.MemberAvailabilityStatusChangeDocument,
    "\n  query getTotalRevenue($member_id: uuid!, $project_id: uuid) {\n    get_total_revenue(args: {member_id: $member_id, project_id: $project_id}) {\n      hours\n      rate\n    }\n  }\n": typeof types.GetTotalRevenueDocument,
    "\n  mutation deleteMember($id: uuid!) {\n    update_members_member_details_by_pk(\n      pk_columns: {id: $id}\n      _set: {onboarded: false}\n    ) {\n      id\n    }\n  }\n": typeof types.DeleteMemberDocument,
    "\n  mutation UpdateMemberRetainer(\n    $member_id: uuid!\n    $hours: Int!\n    $days: [String!]!\n    $retainer_type: Int!\n  ) {\n    insert_members_member_retainer_one(\n      object: {\n        member_id: $member_id\n        hours: $hours\n        days: $days\n        retainer_type: $retainer_type\n      }\n      on_conflict: {\n        constraint: member_retainer_member_id_key\n        update_columns: [hours, days, retainer_type]\n      }\n    ) {\n      id\n      hours\n      days\n      member_retainer_type {\n        name\n      }\n    }\n  }\n": typeof types.UpdateMemberRetainerDocument,
    "\n  query GetMemberRetainer($member_id: uuid!) {\n    members_member_retainer(where: {member_id: {_eq: $member_id}}) {\n      id\n      hours\n      days\n      retainer_type\n      member_retainer_type {\n        name\n      }\n    }\n  }\n": typeof types.GetMemberRetainerDocument,
    "\n  query MembersList {\n    members_member_details(\n      where: {_and: [{alias: {_neq: \"\"}}, {onboarded: {_eq: true}}]}\n    ) {\n      career_starting_year\n      alias\n      id\n      created_at\n      joining_date\n      type {\n        name\n      }\n      designation\n      member_status\n      member_photo\n      member_skills {\n        id\n        rating\n        skill_detail {\n          title\n          type\n          id\n        }\n      }\n      member_photo\n    }\n    members_member_details_aggregate(\n      where: {_and: [{alias: {_neq: \"\"}}, {onboarded: {_eq: true}}]}\n    ) {\n      aggregate {\n        count\n      }\n    }\n  }\n": typeof types.MembersListDocument,
    "\n  query NewBies($startTime: date!) {\n    members_member_details(\n      where: {\n        _and: [{joining_date: {_gte: $startTime}}, {onboarded: {_eq: true}}]\n      }\n    ) {\n      id\n      alias\n    }\n  }\n": typeof types.NewBiesDocument,
    "\n  mutation GenerateOTP(\n    $phone: String!\n    $purpose: String = \"login\"\n    $country_code: String = \"91\"\n  ) {\n    generateOTP(\n      arg1: {\n        phone_number: $phone\n        purpose: $purpose\n        country_code: $country_code\n      }\n    ) {\n      message\n      token\n      status\n      data {\n        phone_number\n        purpose\n        user_id\n      }\n    }\n  }\n": typeof types.GenerateOtpDocument,
    "\n  mutation VerifyOTP(\n    $phone_number: String!\n    $otp: Int!\n    $country_code: String = \"91\"\n  ) {\n    verifyOTP(\n      arg1: {\n        otp: $otp\n        phone_number: $phone_number\n        country_code: $country_code\n      }\n    ) {\n      data {\n        user_id\n        member_details {\n          alias\n          bio\n          department\n          first_name\n          id\n          last_name\n          level\n          type {\n            name\n          }\n          linkedin_profile\n          member_engagement\n          member_photo\n          member_role\n          member_status\n          phone_number\n          uix_email\n          login_user_infos {\n            id\n            last_seen\n            member_id\n            mobile\n          }\n          member_badges {\n            badge_detail {\n              badge_media\n              badge_name\n              id\n            }\n          }\n          member_projects {\n            project_detail {\n              id\n              project_logo\n              project_name\n            }\n          }\n          member_skills {\n            skill_detail {\n              id\n              title\n              type\n            }\n          }\n        }\n      }\n      message\n    }\n  }\n": typeof types.VerifyOtpDocument,
    "\n  mutation CreateMemberReferral(\n    $first_name: String\n    $last_name: String\n    $linkedin_url: String\n    $description: String\n    $referred_by: uuid\n    $phone_number: String\n    $email: String\n    $resume_link: String\n    $status: members_member_referral_statuses_enum = RECEIVED\n  ) {\n    insert_members_member_referrals_one(\n      object: {\n        first_name: $first_name\n        last_name: $last_name\n        linkedin_url: $linkedin_url\n        description: $description\n        referred_by: $referred_by\n        phone_number: $phone_number\n        status: $status\n        email: $email\n        resume_link: $resume_link\n        source: APP\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.CreateMemberReferralDocument,
    "\n  mutation CreateProjectReferral(\n    $first_name: String\n    $last_name: String\n    $description: String\n    $linkedin_url: String\n    $phone_number: String\n    $member_id: uuid!\n  ) {\n    insert_leads_lead_details_one(\n      object: {\n        first_name: $first_name\n        last_name: $last_name\n        description: $description\n        linkedin_url: $linkedin_url\n        phone_number: $phone_number\n        lead_dealmaker_hunters: {\n          data: {member_id: $member_id, member_role: Hunter}\n        }\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.CreateProjectReferralDocument,
    "\n  mutation DropReferMember(\n    $drop_id: uuid\n    $first_name: String\n    $last_name: String\n    $description: String\n    $department: members_member_department_enum\n    $phone_number: String\n    $linkedin_url: String\n    $referred_by: uuid\n    $resume_link: String = \"\"\n    $email: String\n  ) {\n    insert_drops_drop_members_one(\n      object: {\n        drop_id: $drop_id\n        interest_type: 1\n        referral: {\n          data: {\n            first_name: $first_name\n            last_name: $last_name\n            department: $department\n            description: $description\n            linkedin_url: $linkedin_url\n            phone_number: $phone_number\n            referred_by: $referred_by\n            resume_link: $resume_link\n            status: RECEIVED\n            email: $email\n          }\n        }\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.DropReferMemberDocument,
    "\n  mutation RegisterUser($user_id: String!, $profile: String) {\n    login(arg1: {user_id: $user_id, profile: $profile}) {\n      data {\n        token\n        user_id\n      }\n      message\n    }\n  }\n": typeof types.RegisterUserDocument,
    "\n  mutation AddMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {\n    insert_members_member_skills_one(\n      object: {member_id: $id, rating: $rating, skill_id: $skillId}\n    ) {\n      id\n      skill_id\n      member_id\n    }\n  }\n": typeof types.AddMemberSkillDocument,
    "\n  mutation DeleteMemberSkill($id: uuid!, $skillId: uuid!) {\n    delete_members_member_skills(\n      where: {skill_id: {_eq: $skillId}, member_id: {_eq: $id}}\n    ) {\n      returning {\n        id\n        skill_id\n      }\n    }\n  }\n": typeof types.DeleteMemberSkillDocument,
    "\n  mutation UpdateMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {\n    update_members_member_skills(\n      _set: {rating: $rating}\n      where: {member_id: {_eq: $id}, skill_id: {_eq: $skillId}}\n    ) {\n      returning {\n        id\n        skill_id\n      }\n    }\n  }\n": typeof types.UpdateMemberSkillDocument,
    "\n  query GetSkillsQuery($search: String) {\n    skills_skill_details(\n      limit: 100\n      where: {\n        _or: [{title: {_ilike: $search}}, {search_code: {_ilike: $search}}]\n      }\n    ) {\n      id\n      title\n      search_code\n      type\n    }\n  }\n": typeof types.GetSkillsQueryDocument,
    "\n  mutation CreateSkill($title: String!, $searchCode: String) {\n    insert_skills_skill_details_one(\n      object: {title: $title, search_code: $searchCode, type: \"custom\"}\n    ) {\n      id\n      title\n      search_code\n      type\n    }\n  }\n": typeof types.CreateSkillDocument,
    "\n  mutation AddTimeLogs($data: [members_member_timelogs_insert_input!]!) {\n    insert_members_member_timelogs(objects: $data) {\n      returning {\n        id\n      }\n    }\n  }\n": typeof types.AddTimeLogsDocument,
    "\n  query GetTimelogs(\n    $id: uuid!\n    $limit: Int\n    $offset: Int\n    $status:members_member_timelog_statuses_enum\n    $projectIds: [uuid!]\n    $startDate: date\n    $endDate: date\n  ) {\n    members_member_timelogs (\n      where: {\n        member_id: { _eq: $id }\n        _and: [\n          { status: { _eq: $status } }\n          { project_id: { _in: $projectIds } }\n          { actual_date: { _gte: $startDate } }\n          { actual_date: { _lte: $endDate } }\n        ]\n      }\n      order_by:[\n        { actual_date: desc_nulls_last }\n        { created_at: desc_nulls_last }\n        { id: desc_nulls_last }\n      ]\n      limit: $limit\n      offset: $offset\n    ) {\n        no_of_hours\n        member_id\n        project_id\n        status\n        work_description\n        created_at\n        actual_date\n        project_detail{\n          id\n          project_logo\n          project_name\n        }\n        id\n    }\n    \n    # Get total count for pagination\n    members_member_timelogs_aggregate (\n      where: {\n        member_id: { _eq: $id }\n        _and: [\n          { status: { _eq: $status } }\n          { project_id: { _in: $projectIds } }\n          { actual_date: { _gte: $startDate } }\n          { actual_date: { _lte: $endDate } }\n        ]\n      }\n    ) {\n      aggregate {\n        count\n      }\n    }\n  }\n": typeof types.GetTimelogsDocument,
    "\n  mutation UpdateTimelogs(\n    $id: uuid!\n    $work_description: String = \"\"\n    $no_of_hours: numeric = \"\"\n  ) {\n    update_members_member_timelogs_by_pk(\n      pk_columns: {id: $id}\n      _set: {work_description: $work_description, no_of_hours: $no_of_hours}\n    ) {\n      id\n    }\n  }\n": typeof types.UpdateTimelogsDocument,
    "\n  mutation DeleteTimelogs($id: uuid!) {\n    delete_members_member_timelogs_by_pk(id: $id) {\n      id\n    }\n  }\n": typeof types.DeleteTimelogsDocument,
};
const documents: Documents = {
    "\n  query AssignedBadgeQuery($id: uuid!) {\n    members_member_badges(where: {badge_id: {_eq: $id}}) {\n      created_at\n      member_detail {\n        alias\n      }\n    }\n  }\n": types.AssignedBadgeQueryDocument,
    "\n  query GetBadgeDetails {\n    badges_badge_details {\n      badge_description\n      badge_media\n      badge_name\n      id\n    }\n  }\n": types.GetBadgeDetailsDocument,
    "\n  query DropsList($limit: Int!, $offset: Int!) {\n    drops_drop_details(\n      order_by: {expires_at: desc}\n      limit: $limit\n      offset: $offset\n    ) {\n      id\n      type\n      drop_summary\n      expires_at\n      title\n      media_url\n      created_by\n      created_at\n      surge\n      engagement_type\n      drop_members {\n        member_id\n      }\n    }\n  }\n": types.DropsListDocument,
    "\n  query DropById($dropId: uuid!) {\n    drops_drop_details(where: {id: {_eq: $dropId}}) {\n      id\n      type\n      drop_summary\n      expires_at\n      title\n      media_url\n      created_by\n      created_at\n      surge\n      engagement_type\n      drop_members {\n        member_id\n      }\n    }\n  }\n": types.DropByIdDocument,
    "\n  mutation AddDropInterest($drop_id: uuid!, $member_id: uuid!) {\n    insert_drops_drop_members_one(\n      object: {drop_id: $drop_id, interest_type: 1, member_id: $member_id}\n    ) {\n      id\n    }\n  }\n": types.AddDropInterestDocument,
    "\n  query DropInterestQuery($drop_id: uuid!, $member_id: uuid!) {\n    drops_drop_members(\n      where: {drop_id: {_eq: $drop_id}, member_id: {_eq: $member_id}}\n    ) {\n      drop_id\n      member_id\n      interest_type\n    }\n  }\n": types.DropInterestQueryDocument,
    "\n  query HomeQuery(\n    $joiningStartTime: date!\n    $member_id: uuid\n    $dropDetailcurrentDate: timestamptz!\n  ) {\n    members_member_details(where: {_and: [{id: {_eq: $member_id}}]}) {\n      id\n      alias\n      bio\n      member_photo\n      member_badges {\n        badge_id\n        badge_detail {\n          badge_name\n          badge_media\n        }\n      }\n      member_status\n      type {\n        id\n        name\n      }\n    }\n\n    recently_joined_members: members_member_details(\n      where: {\n        _and: [\n          {joining_date: {_gte: $joiningStartTime}}\n          {onboarded: {_eq: true}}\n        ]\n      }\n    ) {\n      id\n      alias\n    }\n\n    drops_drop_details_aggregate(\n      where: {_and: [{expires_at: {_gt: $dropDetailcurrentDate}}]}\n    ) {\n      aggregate {\n        count\n      }\n    }\n\n    app_ui_homepage_banners {\n      id\n      banner_color\n      banner_description\n      banner_order\n      banner_title\n      action_type\n      action_url\n    }\n\n    stories_story_groups {\n      id\n      title\n      story_details(order_by: [{story_order: asc}]) {\n        id\n        cover_media\n        created_at\n        link\n        title\n        media_type\n        duration\n        isReadMore\n        isSeen\n        story_group_id\n        showOverlay\n        story_group {\n          id\n          title\n        }\n        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {\n          aggregate {\n            count\n          }\n        }\n      }\n    }\n  }\n": types.HomeQueryDocument,
    "\n  query HomeStoriesQuery($member_id: uuid!) {\n    stories_story_groups {\n      id\n      title\n      story_details(order_by: [{story_order: asc}]) {\n        id\n        cover_media\n        created_at\n        link\n        title\n        media_type\n        duration\n        isReadMore\n        isSeen\n        story_group_id\n        showOverlay\n        story_group {\n          id\n          title\n        }\n        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {\n          aggregate {\n            count\n          }\n        }\n      }\n    }\n  }\n": types.HomeStoriesQueryDocument,
    "\n  mutation markSeen($member_id: uuid!, $story_id: uuid!) {\n    insert_members_member_stories_one(\n      object: {member_id: $member_id, story_id: $story_id}\n    ) {\n      id\n    }\n  }\n": types.MarkSeenDocument,
    "\n  query LinkedinSearch($search: String!) {\n    LinkedinSearch(arg1: {search: $search}) {\n      status\n      message\n      data {\n        name\n        photo\n        headline\n        profile_url\n        location\n      }\n    }\n  }\n": types.LinkedinSearchDocument,
    "\n  query MemberDetails($id: uuid!) {\n    GetRevenueGraphDataForApp(arg1: {member_id: $id}) {\n      status\n      message\n      data {\n        slot\n        label\n        hours\n        revenue\n      }\n    }\n    members_member_details_by_pk(id: $id) {\n      alias\n      bio\n      department\n      designation\n      created_at\n      first_name\n      id\n      last_name\n      level\n      linkedin_profile\n      member_badges {\n        badge_detail {\n          badge_description\n          badge_name\n          badge_media\n          id\n        }\n      }\n      member_bank_details {\n        account_number\n        bank_account_name\n        ifsc_code\n      }\n      member_engagement\n      member_photo\n      member_projects {\n        project_detail {\n          cover_image\n          project_name\n          project_logo\n          project_description\n          id\n        }\n      }\n      member_role\n      member_skills {\n        id\n        rating\n        skill_detail {\n          title\n          type\n          id\n        }\n      }\n      member_retainers {\n        id\n        hours\n        days\n        retainer_type\n        member_retainer_type {\n          name\n        }\n      }\n      member_status\n      member_type\n      pan_card_link\n      personal_email\n      phone_number\n      referred_by\n      resume_link\n      salary\n      uix_email\n      updated_at\n      joining_date\n      post_details {\n        post_content\n        post_subject\n        posted_by\n        type\n        id\n      }\n      member_projects_aggregate(where: {member_id: {_eq: $id}}) {\n        aggregate {\n          count\n        }\n      }\n      member_projects {\n        project_detail {\n          cover_image\n          project_name\n          project_logo\n          project_description\n          id\n        }\n      }\n      lead_dealmaker_hunters_aggregate(where: {member_id: {_eq: $id}}) {\n        aggregate {\n          count\n        }\n      }\n    }\n  }\n": types.MemberDetailsDocument,
    "\n  query MemberProject($id: uuid!) {\n    members_member_projects(where: {member_id: {_eq: $id}}) {\n      project_detail {\n        project_name\n        id\n        project_logo\n      }\n    }\n  }\n": types.MemberProjectDocument,
    "\n  mutation MemberAvailabilityStatusChange($id: uuid!, $member_status: Int) {\n    update_members_member_details_by_pk(\n      pk_columns: {id: $id}\n      _set: {member_status: $member_status}\n    ) {\n      id\n    }\n  }\n": types.MemberAvailabilityStatusChangeDocument,
    "\n  query getTotalRevenue($member_id: uuid!, $project_id: uuid) {\n    get_total_revenue(args: {member_id: $member_id, project_id: $project_id}) {\n      hours\n      rate\n    }\n  }\n": types.GetTotalRevenueDocument,
    "\n  mutation deleteMember($id: uuid!) {\n    update_members_member_details_by_pk(\n      pk_columns: {id: $id}\n      _set: {onboarded: false}\n    ) {\n      id\n    }\n  }\n": types.DeleteMemberDocument,
    "\n  mutation UpdateMemberRetainer(\n    $member_id: uuid!\n    $hours: Int!\n    $days: [String!]!\n    $retainer_type: Int!\n  ) {\n    insert_members_member_retainer_one(\n      object: {\n        member_id: $member_id\n        hours: $hours\n        days: $days\n        retainer_type: $retainer_type\n      }\n      on_conflict: {\n        constraint: member_retainer_member_id_key\n        update_columns: [hours, days, retainer_type]\n      }\n    ) {\n      id\n      hours\n      days\n      member_retainer_type {\n        name\n      }\n    }\n  }\n": types.UpdateMemberRetainerDocument,
    "\n  query GetMemberRetainer($member_id: uuid!) {\n    members_member_retainer(where: {member_id: {_eq: $member_id}}) {\n      id\n      hours\n      days\n      retainer_type\n      member_retainer_type {\n        name\n      }\n    }\n  }\n": types.GetMemberRetainerDocument,
    "\n  query MembersList {\n    members_member_details(\n      where: {_and: [{alias: {_neq: \"\"}}, {onboarded: {_eq: true}}]}\n    ) {\n      career_starting_year\n      alias\n      id\n      created_at\n      joining_date\n      type {\n        name\n      }\n      designation\n      member_status\n      member_photo\n      member_skills {\n        id\n        rating\n        skill_detail {\n          title\n          type\n          id\n        }\n      }\n      member_photo\n    }\n    members_member_details_aggregate(\n      where: {_and: [{alias: {_neq: \"\"}}, {onboarded: {_eq: true}}]}\n    ) {\n      aggregate {\n        count\n      }\n    }\n  }\n": types.MembersListDocument,
    "\n  query NewBies($startTime: date!) {\n    members_member_details(\n      where: {\n        _and: [{joining_date: {_gte: $startTime}}, {onboarded: {_eq: true}}]\n      }\n    ) {\n      id\n      alias\n    }\n  }\n": types.NewBiesDocument,
    "\n  mutation GenerateOTP(\n    $phone: String!\n    $purpose: String = \"login\"\n    $country_code: String = \"91\"\n  ) {\n    generateOTP(\n      arg1: {\n        phone_number: $phone\n        purpose: $purpose\n        country_code: $country_code\n      }\n    ) {\n      message\n      token\n      status\n      data {\n        phone_number\n        purpose\n        user_id\n      }\n    }\n  }\n": types.GenerateOtpDocument,
    "\n  mutation VerifyOTP(\n    $phone_number: String!\n    $otp: Int!\n    $country_code: String = \"91\"\n  ) {\n    verifyOTP(\n      arg1: {\n        otp: $otp\n        phone_number: $phone_number\n        country_code: $country_code\n      }\n    ) {\n      data {\n        user_id\n        member_details {\n          alias\n          bio\n          department\n          first_name\n          id\n          last_name\n          level\n          type {\n            name\n          }\n          linkedin_profile\n          member_engagement\n          member_photo\n          member_role\n          member_status\n          phone_number\n          uix_email\n          login_user_infos {\n            id\n            last_seen\n            member_id\n            mobile\n          }\n          member_badges {\n            badge_detail {\n              badge_media\n              badge_name\n              id\n            }\n          }\n          member_projects {\n            project_detail {\n              id\n              project_logo\n              project_name\n            }\n          }\n          member_skills {\n            skill_detail {\n              id\n              title\n              type\n            }\n          }\n        }\n      }\n      message\n    }\n  }\n": types.VerifyOtpDocument,
    "\n  mutation CreateMemberReferral(\n    $first_name: String\n    $last_name: String\n    $linkedin_url: String\n    $description: String\n    $referred_by: uuid\n    $phone_number: String\n    $email: String\n    $resume_link: String\n    $status: members_member_referral_statuses_enum = RECEIVED\n  ) {\n    insert_members_member_referrals_one(\n      object: {\n        first_name: $first_name\n        last_name: $last_name\n        linkedin_url: $linkedin_url\n        description: $description\n        referred_by: $referred_by\n        phone_number: $phone_number\n        status: $status\n        email: $email\n        resume_link: $resume_link\n        source: APP\n      }\n    ) {\n      id\n    }\n  }\n": types.CreateMemberReferralDocument,
    "\n  mutation CreateProjectReferral(\n    $first_name: String\n    $last_name: String\n    $description: String\n    $linkedin_url: String\n    $phone_number: String\n    $member_id: uuid!\n  ) {\n    insert_leads_lead_details_one(\n      object: {\n        first_name: $first_name\n        last_name: $last_name\n        description: $description\n        linkedin_url: $linkedin_url\n        phone_number: $phone_number\n        lead_dealmaker_hunters: {\n          data: {member_id: $member_id, member_role: Hunter}\n        }\n      }\n    ) {\n      id\n    }\n  }\n": types.CreateProjectReferralDocument,
    "\n  mutation DropReferMember(\n    $drop_id: uuid\n    $first_name: String\n    $last_name: String\n    $description: String\n    $department: members_member_department_enum\n    $phone_number: String\n    $linkedin_url: String\n    $referred_by: uuid\n    $resume_link: String = \"\"\n    $email: String\n  ) {\n    insert_drops_drop_members_one(\n      object: {\n        drop_id: $drop_id\n        interest_type: 1\n        referral: {\n          data: {\n            first_name: $first_name\n            last_name: $last_name\n            department: $department\n            description: $description\n            linkedin_url: $linkedin_url\n            phone_number: $phone_number\n            referred_by: $referred_by\n            resume_link: $resume_link\n            status: RECEIVED\n            email: $email\n          }\n        }\n      }\n    ) {\n      id\n    }\n  }\n": types.DropReferMemberDocument,
    "\n  mutation RegisterUser($user_id: String!, $profile: String) {\n    login(arg1: {user_id: $user_id, profile: $profile}) {\n      data {\n        token\n        user_id\n      }\n      message\n    }\n  }\n": types.RegisterUserDocument,
    "\n  mutation AddMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {\n    insert_members_member_skills_one(\n      object: {member_id: $id, rating: $rating, skill_id: $skillId}\n    ) {\n      id\n      skill_id\n      member_id\n    }\n  }\n": types.AddMemberSkillDocument,
    "\n  mutation DeleteMemberSkill($id: uuid!, $skillId: uuid!) {\n    delete_members_member_skills(\n      where: {skill_id: {_eq: $skillId}, member_id: {_eq: $id}}\n    ) {\n      returning {\n        id\n        skill_id\n      }\n    }\n  }\n": types.DeleteMemberSkillDocument,
    "\n  mutation UpdateMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {\n    update_members_member_skills(\n      _set: {rating: $rating}\n      where: {member_id: {_eq: $id}, skill_id: {_eq: $skillId}}\n    ) {\n      returning {\n        id\n        skill_id\n      }\n    }\n  }\n": types.UpdateMemberSkillDocument,
    "\n  query GetSkillsQuery($search: String) {\n    skills_skill_details(\n      limit: 100\n      where: {\n        _or: [{title: {_ilike: $search}}, {search_code: {_ilike: $search}}]\n      }\n    ) {\n      id\n      title\n      search_code\n      type\n    }\n  }\n": types.GetSkillsQueryDocument,
    "\n  mutation CreateSkill($title: String!, $searchCode: String) {\n    insert_skills_skill_details_one(\n      object: {title: $title, search_code: $searchCode, type: \"custom\"}\n    ) {\n      id\n      title\n      search_code\n      type\n    }\n  }\n": types.CreateSkillDocument,
    "\n  mutation AddTimeLogs($data: [members_member_timelogs_insert_input!]!) {\n    insert_members_member_timelogs(objects: $data) {\n      returning {\n        id\n      }\n    }\n  }\n": types.AddTimeLogsDocument,
    "\n  query GetTimelogs(\n    $id: uuid!\n    $limit: Int\n    $offset: Int\n    $status:members_member_timelog_statuses_enum\n    $projectIds: [uuid!]\n    $startDate: date\n    $endDate: date\n  ) {\n    members_member_timelogs (\n      where: {\n        member_id: { _eq: $id }\n        _and: [\n          { status: { _eq: $status } }\n          { project_id: { _in: $projectIds } }\n          { actual_date: { _gte: $startDate } }\n          { actual_date: { _lte: $endDate } }\n        ]\n      }\n      order_by:[\n        { actual_date: desc_nulls_last }\n        { created_at: desc_nulls_last }\n        { id: desc_nulls_last }\n      ]\n      limit: $limit\n      offset: $offset\n    ) {\n        no_of_hours\n        member_id\n        project_id\n        status\n        work_description\n        created_at\n        actual_date\n        project_detail{\n          id\n          project_logo\n          project_name\n        }\n        id\n    }\n    \n    # Get total count for pagination\n    members_member_timelogs_aggregate (\n      where: {\n        member_id: { _eq: $id }\n        _and: [\n          { status: { _eq: $status } }\n          { project_id: { _in: $projectIds } }\n          { actual_date: { _gte: $startDate } }\n          { actual_date: { _lte: $endDate } }\n        ]\n      }\n    ) {\n      aggregate {\n        count\n      }\n    }\n  }\n": types.GetTimelogsDocument,
    "\n  mutation UpdateTimelogs(\n    $id: uuid!\n    $work_description: String = \"\"\n    $no_of_hours: numeric = \"\"\n  ) {\n    update_members_member_timelogs_by_pk(\n      pk_columns: {id: $id}\n      _set: {work_description: $work_description, no_of_hours: $no_of_hours}\n    ) {\n      id\n    }\n  }\n": types.UpdateTimelogsDocument,
    "\n  mutation DeleteTimelogs($id: uuid!) {\n    delete_members_member_timelogs_by_pk(id: $id) {\n      id\n    }\n  }\n": types.DeleteTimelogsDocument,
};

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query AssignedBadgeQuery($id: uuid!) {\n    members_member_badges(where: {badge_id: {_eq: $id}}) {\n      created_at\n      member_detail {\n        alias\n      }\n    }\n  }\n"): typeof import('./graphql').AssignedBadgeQueryDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query GetBadgeDetails {\n    badges_badge_details {\n      badge_description\n      badge_media\n      badge_name\n      id\n    }\n  }\n"): typeof import('./graphql').GetBadgeDetailsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query DropsList($limit: Int!, $offset: Int!) {\n    drops_drop_details(\n      order_by: {expires_at: desc}\n      limit: $limit\n      offset: $offset\n    ) {\n      id\n      type\n      drop_summary\n      expires_at\n      title\n      media_url\n      created_by\n      created_at\n      surge\n      engagement_type\n      drop_members {\n        member_id\n      }\n    }\n  }\n"): typeof import('./graphql').DropsListDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query DropById($dropId: uuid!) {\n    drops_drop_details(where: {id: {_eq: $dropId}}) {\n      id\n      type\n      drop_summary\n      expires_at\n      title\n      media_url\n      created_by\n      created_at\n      surge\n      engagement_type\n      drop_members {\n        member_id\n      }\n    }\n  }\n"): typeof import('./graphql').DropByIdDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation AddDropInterest($drop_id: uuid!, $member_id: uuid!) {\n    insert_drops_drop_members_one(\n      object: {drop_id: $drop_id, interest_type: 1, member_id: $member_id}\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').AddDropInterestDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query DropInterestQuery($drop_id: uuid!, $member_id: uuid!) {\n    drops_drop_members(\n      where: {drop_id: {_eq: $drop_id}, member_id: {_eq: $member_id}}\n    ) {\n      drop_id\n      member_id\n      interest_type\n    }\n  }\n"): typeof import('./graphql').DropInterestQueryDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query HomeQuery(\n    $joiningStartTime: date!\n    $member_id: uuid\n    $dropDetailcurrentDate: timestamptz!\n  ) {\n    members_member_details(where: {_and: [{id: {_eq: $member_id}}]}) {\n      id\n      alias\n      bio\n      member_photo\n      member_badges {\n        badge_id\n        badge_detail {\n          badge_name\n          badge_media\n        }\n      }\n      member_status\n      type {\n        id\n        name\n      }\n    }\n\n    recently_joined_members: members_member_details(\n      where: {\n        _and: [\n          {joining_date: {_gte: $joiningStartTime}}\n          {onboarded: {_eq: true}}\n        ]\n      }\n    ) {\n      id\n      alias\n    }\n\n    drops_drop_details_aggregate(\n      where: {_and: [{expires_at: {_gt: $dropDetailcurrentDate}}]}\n    ) {\n      aggregate {\n        count\n      }\n    }\n\n    app_ui_homepage_banners {\n      id\n      banner_color\n      banner_description\n      banner_order\n      banner_title\n      action_type\n      action_url\n    }\n\n    stories_story_groups {\n      id\n      title\n      story_details(order_by: [{story_order: asc}]) {\n        id\n        cover_media\n        created_at\n        link\n        title\n        media_type\n        duration\n        isReadMore\n        isSeen\n        story_group_id\n        showOverlay\n        story_group {\n          id\n          title\n        }\n        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {\n          aggregate {\n            count\n          }\n        }\n      }\n    }\n  }\n"): typeof import('./graphql').HomeQueryDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query HomeStoriesQuery($member_id: uuid!) {\n    stories_story_groups {\n      id\n      title\n      story_details(order_by: [{story_order: asc}]) {\n        id\n        cover_media\n        created_at\n        link\n        title\n        media_type\n        duration\n        isReadMore\n        isSeen\n        story_group_id\n        showOverlay\n        story_group {\n          id\n          title\n        }\n        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {\n          aggregate {\n            count\n          }\n        }\n      }\n    }\n  }\n"): typeof import('./graphql').HomeStoriesQueryDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation markSeen($member_id: uuid!, $story_id: uuid!) {\n    insert_members_member_stories_one(\n      object: {member_id: $member_id, story_id: $story_id}\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').MarkSeenDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query LinkedinSearch($search: String!) {\n    LinkedinSearch(arg1: {search: $search}) {\n      status\n      message\n      data {\n        name\n        photo\n        headline\n        profile_url\n        location\n      }\n    }\n  }\n"): typeof import('./graphql').LinkedinSearchDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query MemberDetails($id: uuid!) {\n    GetRevenueGraphDataForApp(arg1: {member_id: $id}) {\n      status\n      message\n      data {\n        slot\n        label\n        hours\n        revenue\n      }\n    }\n    members_member_details_by_pk(id: $id) {\n      alias\n      bio\n      department\n      designation\n      created_at\n      first_name\n      id\n      last_name\n      level\n      linkedin_profile\n      member_badges {\n        badge_detail {\n          badge_description\n          badge_name\n          badge_media\n          id\n        }\n      }\n      member_bank_details {\n        account_number\n        bank_account_name\n        ifsc_code\n      }\n      member_engagement\n      member_photo\n      member_projects {\n        project_detail {\n          cover_image\n          project_name\n          project_logo\n          project_description\n          id\n        }\n      }\n      member_role\n      member_skills {\n        id\n        rating\n        skill_detail {\n          title\n          type\n          id\n        }\n      }\n      member_retainers {\n        id\n        hours\n        days\n        retainer_type\n        member_retainer_type {\n          name\n        }\n      }\n      member_status\n      member_type\n      pan_card_link\n      personal_email\n      phone_number\n      referred_by\n      resume_link\n      salary\n      uix_email\n      updated_at\n      joining_date\n      post_details {\n        post_content\n        post_subject\n        posted_by\n        type\n        id\n      }\n      member_projects_aggregate(where: {member_id: {_eq: $id}}) {\n        aggregate {\n          count\n        }\n      }\n      member_projects {\n        project_detail {\n          cover_image\n          project_name\n          project_logo\n          project_description\n          id\n        }\n      }\n      lead_dealmaker_hunters_aggregate(where: {member_id: {_eq: $id}}) {\n        aggregate {\n          count\n        }\n      }\n    }\n  }\n"): typeof import('./graphql').MemberDetailsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query MemberProject($id: uuid!) {\n    members_member_projects(where: {member_id: {_eq: $id}}) {\n      project_detail {\n        project_name\n        id\n        project_logo\n      }\n    }\n  }\n"): typeof import('./graphql').MemberProjectDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation MemberAvailabilityStatusChange($id: uuid!, $member_status: Int) {\n    update_members_member_details_by_pk(\n      pk_columns: {id: $id}\n      _set: {member_status: $member_status}\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').MemberAvailabilityStatusChangeDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getTotalRevenue($member_id: uuid!, $project_id: uuid) {\n    get_total_revenue(args: {member_id: $member_id, project_id: $project_id}) {\n      hours\n      rate\n    }\n  }\n"): typeof import('./graphql').GetTotalRevenueDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation deleteMember($id: uuid!) {\n    update_members_member_details_by_pk(\n      pk_columns: {id: $id}\n      _set: {onboarded: false}\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').DeleteMemberDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation UpdateMemberRetainer(\n    $member_id: uuid!\n    $hours: Int!\n    $days: [String!]!\n    $retainer_type: Int!\n  ) {\n    insert_members_member_retainer_one(\n      object: {\n        member_id: $member_id\n        hours: $hours\n        days: $days\n        retainer_type: $retainer_type\n      }\n      on_conflict: {\n        constraint: member_retainer_member_id_key\n        update_columns: [hours, days, retainer_type]\n      }\n    ) {\n      id\n      hours\n      days\n      member_retainer_type {\n        name\n      }\n    }\n  }\n"): typeof import('./graphql').UpdateMemberRetainerDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query GetMemberRetainer($member_id: uuid!) {\n    members_member_retainer(where: {member_id: {_eq: $member_id}}) {\n      id\n      hours\n      days\n      retainer_type\n      member_retainer_type {\n        name\n      }\n    }\n  }\n"): typeof import('./graphql').GetMemberRetainerDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query MembersList {\n    members_member_details(\n      where: {_and: [{alias: {_neq: \"\"}}, {onboarded: {_eq: true}}]}\n    ) {\n      career_starting_year\n      alias\n      id\n      created_at\n      joining_date\n      type {\n        name\n      }\n      designation\n      member_status\n      member_photo\n      member_skills {\n        id\n        rating\n        skill_detail {\n          title\n          type\n          id\n        }\n      }\n      member_photo\n    }\n    members_member_details_aggregate(\n      where: {_and: [{alias: {_neq: \"\"}}, {onboarded: {_eq: true}}]}\n    ) {\n      aggregate {\n        count\n      }\n    }\n  }\n"): typeof import('./graphql').MembersListDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query NewBies($startTime: date!) {\n    members_member_details(\n      where: {\n        _and: [{joining_date: {_gte: $startTime}}, {onboarded: {_eq: true}}]\n      }\n    ) {\n      id\n      alias\n    }\n  }\n"): typeof import('./graphql').NewBiesDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation GenerateOTP(\n    $phone: String!\n    $purpose: String = \"login\"\n    $country_code: String = \"91\"\n  ) {\n    generateOTP(\n      arg1: {\n        phone_number: $phone\n        purpose: $purpose\n        country_code: $country_code\n      }\n    ) {\n      message\n      token\n      status\n      data {\n        phone_number\n        purpose\n        user_id\n      }\n    }\n  }\n"): typeof import('./graphql').GenerateOtpDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation VerifyOTP(\n    $phone_number: String!\n    $otp: Int!\n    $country_code: String = \"91\"\n  ) {\n    verifyOTP(\n      arg1: {\n        otp: $otp\n        phone_number: $phone_number\n        country_code: $country_code\n      }\n    ) {\n      data {\n        user_id\n        member_details {\n          alias\n          bio\n          department\n          first_name\n          id\n          last_name\n          level\n          type {\n            name\n          }\n          linkedin_profile\n          member_engagement\n          member_photo\n          member_role\n          member_status\n          phone_number\n          uix_email\n          login_user_infos {\n            id\n            last_seen\n            member_id\n            mobile\n          }\n          member_badges {\n            badge_detail {\n              badge_media\n              badge_name\n              id\n            }\n          }\n          member_projects {\n            project_detail {\n              id\n              project_logo\n              project_name\n            }\n          }\n          member_skills {\n            skill_detail {\n              id\n              title\n              type\n            }\n          }\n        }\n      }\n      message\n    }\n  }\n"): typeof import('./graphql').VerifyOtpDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation CreateMemberReferral(\n    $first_name: String\n    $last_name: String\n    $linkedin_url: String\n    $description: String\n    $referred_by: uuid\n    $phone_number: String\n    $email: String\n    $resume_link: String\n    $status: members_member_referral_statuses_enum = RECEIVED\n  ) {\n    insert_members_member_referrals_one(\n      object: {\n        first_name: $first_name\n        last_name: $last_name\n        linkedin_url: $linkedin_url\n        description: $description\n        referred_by: $referred_by\n        phone_number: $phone_number\n        status: $status\n        email: $email\n        resume_link: $resume_link\n        source: APP\n      }\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').CreateMemberReferralDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation CreateProjectReferral(\n    $first_name: String\n    $last_name: String\n    $description: String\n    $linkedin_url: String\n    $phone_number: String\n    $member_id: uuid!\n  ) {\n    insert_leads_lead_details_one(\n      object: {\n        first_name: $first_name\n        last_name: $last_name\n        description: $description\n        linkedin_url: $linkedin_url\n        phone_number: $phone_number\n        lead_dealmaker_hunters: {\n          data: {member_id: $member_id, member_role: Hunter}\n        }\n      }\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').CreateProjectReferralDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation DropReferMember(\n    $drop_id: uuid\n    $first_name: String\n    $last_name: String\n    $description: String\n    $department: members_member_department_enum\n    $phone_number: String\n    $linkedin_url: String\n    $referred_by: uuid\n    $resume_link: String = \"\"\n    $email: String\n  ) {\n    insert_drops_drop_members_one(\n      object: {\n        drop_id: $drop_id\n        interest_type: 1\n        referral: {\n          data: {\n            first_name: $first_name\n            last_name: $last_name\n            department: $department\n            description: $description\n            linkedin_url: $linkedin_url\n            phone_number: $phone_number\n            referred_by: $referred_by\n            resume_link: $resume_link\n            status: RECEIVED\n            email: $email\n          }\n        }\n      }\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').DropReferMemberDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation RegisterUser($user_id: String!, $profile: String) {\n    login(arg1: {user_id: $user_id, profile: $profile}) {\n      data {\n        token\n        user_id\n      }\n      message\n    }\n  }\n"): typeof import('./graphql').RegisterUserDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation AddMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {\n    insert_members_member_skills_one(\n      object: {member_id: $id, rating: $rating, skill_id: $skillId}\n    ) {\n      id\n      skill_id\n      member_id\n    }\n  }\n"): typeof import('./graphql').AddMemberSkillDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation DeleteMemberSkill($id: uuid!, $skillId: uuid!) {\n    delete_members_member_skills(\n      where: {skill_id: {_eq: $skillId}, member_id: {_eq: $id}}\n    ) {\n      returning {\n        id\n        skill_id\n      }\n    }\n  }\n"): typeof import('./graphql').DeleteMemberSkillDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation UpdateMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {\n    update_members_member_skills(\n      _set: {rating: $rating}\n      where: {member_id: {_eq: $id}, skill_id: {_eq: $skillId}}\n    ) {\n      returning {\n        id\n        skill_id\n      }\n    }\n  }\n"): typeof import('./graphql').UpdateMemberSkillDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query GetSkillsQuery($search: String) {\n    skills_skill_details(\n      limit: 100\n      where: {\n        _or: [{title: {_ilike: $search}}, {search_code: {_ilike: $search}}]\n      }\n    ) {\n      id\n      title\n      search_code\n      type\n    }\n  }\n"): typeof import('./graphql').GetSkillsQueryDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation CreateSkill($title: String!, $searchCode: String) {\n    insert_skills_skill_details_one(\n      object: {title: $title, search_code: $searchCode, type: \"custom\"}\n    ) {\n      id\n      title\n      search_code\n      type\n    }\n  }\n"): typeof import('./graphql').CreateSkillDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation AddTimeLogs($data: [members_member_timelogs_insert_input!]!) {\n    insert_members_member_timelogs(objects: $data) {\n      returning {\n        id\n      }\n    }\n  }\n"): typeof import('./graphql').AddTimeLogsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query GetTimelogs(\n    $id: uuid!\n    $limit: Int\n    $offset: Int\n    $status:members_member_timelog_statuses_enum\n    $projectIds: [uuid!]\n    $startDate: date\n    $endDate: date\n  ) {\n    members_member_timelogs (\n      where: {\n        member_id: { _eq: $id }\n        _and: [\n          { status: { _eq: $status } }\n          { project_id: { _in: $projectIds } }\n          { actual_date: { _gte: $startDate } }\n          { actual_date: { _lte: $endDate } }\n        ]\n      }\n      order_by:[\n        { actual_date: desc_nulls_last }\n        { created_at: desc_nulls_last }\n        { id: desc_nulls_last }\n      ]\n      limit: $limit\n      offset: $offset\n    ) {\n        no_of_hours\n        member_id\n        project_id\n        status\n        work_description\n        created_at\n        actual_date\n        project_detail{\n          id\n          project_logo\n          project_name\n        }\n        id\n    }\n    \n    # Get total count for pagination\n    members_member_timelogs_aggregate (\n      where: {\n        member_id: { _eq: $id }\n        _and: [\n          { status: { _eq: $status } }\n          { project_id: { _in: $projectIds } }\n          { actual_date: { _gte: $startDate } }\n          { actual_date: { _lte: $endDate } }\n        ]\n      }\n    ) {\n      aggregate {\n        count\n      }\n    }\n  }\n"): typeof import('./graphql').GetTimelogsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation UpdateTimelogs(\n    $id: uuid!\n    $work_description: String = \"\"\n    $no_of_hours: numeric = \"\"\n  ) {\n    update_members_member_timelogs_by_pk(\n      pk_columns: {id: $id}\n      _set: {work_description: $work_description, no_of_hours: $no_of_hours}\n    ) {\n      id\n    }\n  }\n"): typeof import('./graphql').UpdateTimelogsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation DeleteTimelogs($id: uuid!) {\n    delete_members_member_timelogs_by_pk(id: $id) {\n      id\n    }\n  }\n"): typeof import('./graphql').DeleteTimelogsDocument;


export function gql(source: string) {
  return (documents as any)[source] ?? {};
}
