import {RootState} from '@store/store';
import {createContext, PropsWithChildren, useContext, useMemo} from 'react';
import {useSelector} from 'react-redux';

const MemberContext = createContext<{
  isCurrentUser: boolean;
  memberId: string;
}>({isCurrentUser: false, memberId: ''});

export const MemberContextProvider = (
  props: PropsWithChildren<{memberId: string}>,
) => {
  const {memberId} = props;

  const userId = useSelector((store: RootState) => store.user.id);

  const data = useMemo(() => {
    return {
      isCurrentUser: userId === memberId,
      memberId,
    };
  }, [userId, memberId]);

  return (
    <MemberContext.Provider value={data}>
      {props.children}
    </MemberContext.Provider>
  );
};

const useMemberContext = () => {
  return useContext(MemberContext);
};

export default useMemberContext;
