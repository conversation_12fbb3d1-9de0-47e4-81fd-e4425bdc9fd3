import React, {createContext, useContext} from 'react';

import {HomeQueryQuery} from '@/types/__generated__/graphql';

interface PageBuilderContextType {
  data: HomeQueryQuery | undefined;
  loading: boolean;
}

const PageBuilderContext = createContext<PageBuilderContextType | undefined>(
  undefined,
);

export const PageBuilderProvider: React.FC<
  PageBuilderContextType & {children: React.ReactNode}
> = ({children, ...props}) => {
  return (
    <PageBuilderContext.Provider
      value={{
        data: props.data,
        loading: props.loading,
      }}>
      {children}
    </PageBuilderContext.Provider>
  );
};

export const usePageBuilder = () => {
  const context = useContext(PageBuilderContext);
  if (context === undefined) {
    throw new Error('usePageBuilder must be used within a PageBuilderProvider');
  }
  return context;
};
