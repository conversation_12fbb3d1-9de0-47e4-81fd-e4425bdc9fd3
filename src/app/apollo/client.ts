import HttpClient from '@api/index';
import {
  ApolloClient,
  ApolloLink,
  from,
  HttpLink,
  InMemoryCache,
  NextLink,
  Operation,
} from '@apollo/client';
import {onError} from '@apollo/client/link/error';
import Utils from '@/app/utils/utils';
import axios from 'axios';

import {Tracker} from '@/app/analytics/tracker';
import {GRAPH_API_URL} from '@/app/constants';

const fetch = async (
  url: string | Request | URL,
  options?: RequestInit,
): Promise<Response> => {
  const body = JSON.parse(options?.body as string);

  const operationName = body?.operationName;

  let result;

  try {
    result = await HttpClient.post(`${url.toString()}`, body, {
      headers: {operationName},
    });
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response) {
        console.error(
          '[HTTP Error Response]:',
          error.response.status,
          error.response.data,
        );
        result = error.response;
      } else if (error.request) {
        console.error('[Network Error]: No response received', error.request);
        throw new Error('[Network Error]: No response received from server.');
      } else {
        console.error('[Axios Error]:', error.message);
        throw error;
      }
    } else {
      console.error('[Non-HTTP Error]:', error);
      throw error;
    }
  }

  return new Response(JSON.stringify(result.data), {
    status: result.status,
    statusText: result.statusText,
  });
};

const errorLink = onError(
  ({graphQLErrors, networkError, operation, forward}) => {
    if (graphQLErrors) {
      for (const err of graphQLErrors) {
        if (err.message.includes('JWTExpired')) {
          console.error(
            `[GraphQL error]: JWT Expired. Message: ${err.message}`,
            operation.operationName,
          );
          Utils.onLogout();
          return;
        } else {
          console.error(
            `[GraphQL error]: Message: ${err.message}, Location: ${err.locations}, Path: ${err.path}`,
            operation.operationName,
          );
        }
      }
    }

    if (networkError) {
      console.error(
        `[Network error]: ${networkError}`,
        operation.operationName,
      );
    }

    return forward(operation);
  },
);

const performanceTrackingMiddleWare = new ApolloLink(
  (operation: Operation, forward: NextLink) => {
    const startTime = performance.now();
    const log = false;

    return forward(operation).map(response => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      if (log)
        console.log(
          `GraphQL query ${operation.operationName} took ${duration}ms`,
        );
      return response;
    });
  },
);

const cache = new InMemoryCache({
  typePolicies: {},
});

const httpLink = new HttpLink({
  uri: GRAPH_API_URL,
  fetch: fetch,
});

export const client = new ApolloClient({
  link: from([errorLink, performanceTrackingMiddleWare, httpLink]),
  cache,
  credentials: 'same-origin',
});
