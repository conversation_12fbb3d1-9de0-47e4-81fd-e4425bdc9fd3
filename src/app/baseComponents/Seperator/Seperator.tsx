import React from 'react';
import {Image, ImageStyle, View} from 'react-native';
import {useStyles} from 'react-native-unistyles';
import {createStyleSheet} from 'react-native-unistyles';

interface SeperatorProps {
  paddingVertical?: number;
  marginTop?: number;
  marginBottom?: number;
  marginVertical?: number;
  tintColor?: string;
  style?: ImageStyle;
}

const Seperator: React.FC<SeperatorProps> = ({
  paddingVertical,
  marginTop,
  marginBottom,
  marginVertical,
  tintColor,
  style,
}) => {
  const {styles} = useStyles(stylesheet);

  return (
    <View
      style={{
        paddingVertical: paddingVertical || 1,
        marginTop: marginTop,
        marginBottom: marginBottom,
        marginVertical: marginVertical,
      }}>
      <Image
        source={require('@assets/border.png')}
        style={[styles.img, style]}
        tintColor={tintColor}
        resizeMode="stretch"
      />
    </View>
  );
};

export default Seperator;

const stylesheet = createStyleSheet(() => ({
  img: {
    width: '100%',
    height: 4,
  },
}));
