import React from 'react';
import {Image, View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

interface SuccessToastProps {
  message: string;
}

export function SuccessToast({message}: SuccessToastProps) {
  const {styles} = useStyles(stylesheet);

  return (
    <View style={styles.toastContainer}>
      <Image
        source={require('@assets/check-circle.png')}
        style={styles.toastIcon}
      />
      <UixText style={styles.toastText}>{message}</UixText>
    </View>
  );
}

const stylesheet = createStyleSheet(() => ({
  toastContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  toastIcon: {
    width: 24,
    height: 24,
  },
  toastText: {
    color: Colors.Yellow750,
    fontSize: 16,
  },
}));
