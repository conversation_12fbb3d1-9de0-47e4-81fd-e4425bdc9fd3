import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import React from 'react';
import {ReactNode, useCallback, useEffect, useRef} from 'react';
import {BackHandler, View, ViewStyle} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useStyles} from 'react-native-unistyles';

import {styleSheet} from './styles';

interface IProps {
  isVisible: boolean;
  style?: ViewStyle;
  closeOnBackdropClick?: boolean;
  children: ReactNode;
  possibleHeights?: string[];
  onClose?: () => void;
  enableDynamicSizing?: boolean;
  handleBackButton?: boolean;
  showHandle?: boolean;
  backgroundStyle?: ViewStyle;
}

const defaultProps = {
  style: {},
  closable: true,
  closeOnBackdropClick: true,
  onClose: () => {},
  headerText: '',
  enableDynamicSizing: true,
  handleBackButton: true,
  showHandle: false,
};

const BottomSheet = (props: IProps) => {
  const {
    isVisible,
    style = defaultProps.style,
    closeOnBackdropClick = defaultProps.closeOnBackdropClick,
    children,
    onClose = defaultProps.onClose,
    possibleHeights,
    enableDynamicSizing = defaultProps.enableDynamicSizing,
    handleBackButton: handleBackButtonFlag = defaultProps.handleBackButton,
    showHandle = defaultProps.showHandle,
    backgroundStyle,
    ...restProps
  } = props;
  const insets = useSafeAreaInsets();
  const {styles} = useStyles(styleSheet);
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  const closeCallback = useCallback(() => {
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  useEffect(() => {
    if (!isVisible) {
      bottomSheetModalRef.current?.dismiss();
    } else {
      bottomSheetModalRef.current?.present();
    }
  }, [isVisible]);

  const renderBackdrop = useCallback(
    (backDropProps: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...backDropProps}
        style={{
          //@ts-expect-error
          ...backDropProps.style,
          backgroundColor: 'black',
        }}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        pressBehavior={closeOnBackdropClick ? 'close' : 'none'}
      />
    ),
    [],
  );
  const CustomHandle = (
    <View style={styles.downArrow}>{/* <BottomSheetArrow /> */}</View>
  );

  useEffect(() => {
    const handleBackButton = () => {
      if (isVisible) {
        bottomSheetModalRef.current?.dismiss();
        onClose();
      } else {
        // navigation.goBack();
        return null;
      }
    };
    if (handleBackButtonFlag) {
      BackHandler.addEventListener('hardwareBackPress', handleBackButton);
    }
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', handleBackButton);
    };
  }, [isVisible, handleBackButtonFlag]);

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={isVisible ? 0 : -1}
      onChange={() => {}}
      onDismiss={() => {
        closeCallback();
      }}
      snapPoints={possibleHeights}
      keyboardBlurBehavior={'restore'}
      enableDynamicSizing={enableDynamicSizing}
      enablePanDownToClose={true}
      backgroundStyle={backgroundStyle ?? []}
      //   handleIndicatorStyle={styles.handle}
      backdropComponent={renderBackdrop}
      handleComponent={showHandle ? () => CustomHandle : null}
      {...restProps}>
      <BottomSheetScrollView
        style={[style]}
        keyboardShouldPersistTaps="handled">
        <View style={[styles.childrenWrapper(insets)]}>{children ?? null}</View>
      </BottomSheetScrollView>
    </BottomSheetModal>
  );
};

export default BottomSheet;
