import {configureFonts} from 'react-native-paper';

const baseFont = {
  fontFamily: 'SFProDisplay',
} as const;

const baseVariants = configureFonts({config: baseFont});

const customVariants = {
  dots: {
    ...baseVariants.bodyMedium,
    fontFamily: 'BPdotsUnicaseSquare',
  },
  dotsBold: {
    ...baseVariants.bodyMedium,
    fontFamily: 'BPdotsUnicaseSquare-Bold',
  },
  dotsLight: {
    ...baseVariants.bodyMedium,
    fontFamily: 'BPdotsUnicaseSquare-Light',
  },
} as const;

export const UixFonts = configureFonts({
  config: {
    ...baseVariants,
    ...customVariants,
  },
});
