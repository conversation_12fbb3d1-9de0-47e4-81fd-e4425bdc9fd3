import HttpClient from '@api/index';
import {DocumentPickerResponse} from '@react-native-documents/picker';

interface AttachResumeResponse {
  public_url: string;
  secure_url: string;
  public_id: string;
}

const attachResume = async (
  fileName: DocumentPickerResponse,
): Promise<AttachResumeResponse> => {
  try {
    const formData = new FormData();
    formData.append('file', fileName);

    const response = await HttpClient.post(
      'https://dash.uixlabs.co/api/media/upload',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    return response.data;
  } catch (error) {
    console.error(error);
    throw new Error('Failed to upload file');
  }
};

export default attachResume;
