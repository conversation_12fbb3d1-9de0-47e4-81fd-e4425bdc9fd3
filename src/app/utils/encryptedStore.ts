import _ from 'lodash';
import Storage from 'react-native-encrypted-storage';

type Keys = 'token' | 'refreshToken';

const getValue = _.memoize(
  async (key: Keys, defaultValue: string): Promise<string> => {
    try {
      const data = await Storage.getItem(key);
      return data ?? defaultValue;
    } catch (e) {
      console.error(`[EncryptedStore] Error getting key "${key}":`, e);
      return defaultValue;
    }
  },
  (key: Keys) => key,
);

const setValue = (key: Keys, value: string) => {
  getValue.cache?.clear?.();
  return Storage.setItem(key, value);
};

const setMultipleValue = (data: {key: Keys; value: string}[]) => {
  getValue.cache?.clear?.();
  const promises = data?.map(({key, value}) => Storage.setItem(key, value));
  return Promise.all(promises);
};

const removeValue = (key: Keys) => {
  getValue.cache?.delete?.(key);
  return Storage.removeItem(key);
};

const clearStore = () => {
  getValue.cache?.clear?.();
  return Storage.clear();
};

const EncryptedStore = {
  getValue,
  setValue,
  removeValue,
  clearStore,
  setMultipleValue,
};

export default EncryptedStore;
