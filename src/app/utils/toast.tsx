import React, {useCallback, useMemo, useRef} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {ToastOptions, useToast} from 'react-native-toast-notifications';

import {SuccessToast} from '@/app/baseComponents/Toast/Toast';
import {Colors} from '@/app/theme/colors';

export const useToastUtils = () => {
  const toast = useToast();
  const insets = useSafeAreaInsets();
  const toastIdRef = useRef('');

  const toastOptions: ToastOptions = useMemo(() => {
    return {
      placement: 'top',
      duration: 3000,
      animationType: 'slide-in',
      textStyle: {
        fontSize: 16,
        color: Colors.Yellow750,
      },
      style: {
        backgroundColor: '#192608',
        padding: 16,
        borderRadius: 8,
        marginHorizontal: 16,
        zIndex: 9999,
        marginTop: insets.top + 20,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
      },
    };
  }, [insets.top]);

  const showToast = useCallback(
    (message: string) => {
      if (toastIdRef.current) {
        toast.hide(toastIdRef.current);
      }
      setTimeout(() => {
        toastIdRef.current = toast.show(
          <SuccessToast message={message} />,
          toastOptions,
        );
      }, 100);
    },
    [toast, toastOptions],
  );

  return {showToast};
};
