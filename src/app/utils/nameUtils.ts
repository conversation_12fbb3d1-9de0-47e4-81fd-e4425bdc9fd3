export interface NameParts {
  firstName: string;
  lastName: string;
}

export function splitFullName(fullName: string | undefined | null): NameParts {
  if (!fullName || typeof fullName !== 'string' || fullName.trim() === '') {
    return {firstName: '', lastName: ''};
  }

  const parts = fullName.trim().split(/\s+/);

  const firstName = parts[0] || '';
  const lastName = parts.slice(1).join(' ');

  return {firstName, lastName};
}
