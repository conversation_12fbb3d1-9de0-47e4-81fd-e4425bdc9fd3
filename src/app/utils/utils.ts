import {CommonActions} from '@react-navigation/native';
import {UserLogoutAction} from '@store/CommonActions';
import store from '@store/store';
import {Platform} from 'react-native';

import Moengage from '../moengage';
import EncryptedStore from './encryptedStore';

import {Tracker} from '@/app/analytics/tracker';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';

export const isAndroid = Platform.OS === 'android';
export const isIOS = Platform.OS === 'ios';

function onLogout() {
  store.dispatch(UserLogoutAction());
  EncryptedStore.clearStore();
  Tracker.reset();
  Moengage.onLogout();

  NavigationService.navigationRef.dispatch(
    CommonActions.reset({
      index: 0,
      routes: [{name: ScreenNames.Login}],
    }),
  );
}

const Utils = {
  onLogout,
};

export default Utils;
