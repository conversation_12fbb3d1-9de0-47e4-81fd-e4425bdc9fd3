import HttpClient from '@api/index';

/**
 * Utility functions to send Slack notifications for various actions in the app
 */

interface SlackNotificationResponse {
  id: string;
}

/**
 * Base function to send a Slack notification
 * @param url The endpoint URL
 * @param id The ID to include in the request body
 * @returns Promise with the response
 */
const sendSlackNotification = async (
  url: string,
  id: string,
): Promise<SlackNotificationResponse> => {
  try {
    const response = await HttpClient.post(url, {
      id: id,
    });
    return response.data;
  } catch (error) {
    console.error(`Error sending Slack notification to ${url}:`, error);
    throw error;
  }
};

/**
 * Send a Slack notification for a new project referral
 * @param id The project referral ID
 * @returns Promise with the response
 */
export const notifyNewProjectReferral = async (
  id: string,
): Promise<SlackNotificationResponse> => {
  return sendSlackNotification(
    'https://api-ops.uixlabs.co/admin/send-slack-message-for-new-lead',
    id,
  );
};

/**
 * Send a Slack notification for a new member referral
 * @param id The member referral ID
 * @returns Promise with the response
 */
export const notifyNewMemberReferral = async (
  id: string,
): Promise<SlackNotificationResponse> => {
  return sendSlackNotification(
    'https://api-ops.uixlabs.co/admin/send-slack-message-for-new-member-referral',
    id,
  );
};

/**
 * Send a Slack notification for a new drop interest or referral
 * @param id The drop interest/referral ID
 * @returns Promise with the response
 */
export const notifyNewDropInterest = async (
  id: string,
): Promise<SlackNotificationResponse> => {
  return sendSlackNotification(
    'https://api-ops.uixlabs.co/admin/send-slack-message-for-new-drop-interest',
    id,
  );
};

export default {
  notifyNewProjectReferral,
  notifyNewMemberReferral,
  notifyNewDropInterest,
};
