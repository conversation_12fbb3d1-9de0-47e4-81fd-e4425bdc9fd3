import HttpClient from '@api/index';

import {base_url} from '../constants';

export const sendLeadAcknowledgementEmail = async ({id}: {id: string}) => {
  return HttpClient.post(`${base_url}/send-lead-acknowledgement-email`, {
    id,
  });
};

export const sendMemberReferralEmail = async ({id}: {id: string}) => {
  return HttpClient.post(`${base_url}/send-member-referral-email`, {
    id,
  });
};

export const sendDropInterestEmail = async ({id}: {id: string}) => {
  return HttpClient.post(`${base_url}/send-drop-interest-email`, {
    id,
  });
};
