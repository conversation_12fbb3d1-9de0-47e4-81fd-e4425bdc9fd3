function getThumbnailFromCloudinary(videoUrl: string, options = {time: 6}) {
  const {time} = options;

  // Match and decompose the Cloudinary video URL
  const urlParts = videoUrl.match(
    /(https:\/\/res\.cloudinary\.com\/[^/]+\/video\/upload)(\/.*\/)?([^/.]+)\.(mp4|webm|mov)/,
  );

  if (!urlParts) {
    console.warn('Invalid Cloudinary video URL');
    return null;
  }

  const baseUrl = urlParts[1]; // https://res.cloudinary.com/<cloud_name>/video/upload
  const folderPath = urlParts[2] || ''; // e.g., /myfolder/ (may be empty)
  const publicId = urlParts[3]; // public ID without extension

  // Build the thumbnail URL
  const transformation = `so_${time},c_fill`;
  const imageUrl = `${baseUrl}/${transformation}${folderPath}${publicId}.jpg`;

  return imageUrl;
}

export default getThumbnailFromCloudinary;
