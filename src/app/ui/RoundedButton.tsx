import React from 'react';
import {
  ActivityIndicator,
  Image,
  ImageSourcePropType,
  Pressable,
  StyleProp,
  View,
  ViewStyle,
} from 'react-native';
import LinearGradient, {
  LinearGradientProps,
} from 'react-native-linear-gradient';

import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixText} from '@/app/ui/UixText';

export enum ButtonType {
  PRIMARY = 'PRIMARY',
  SECONDARY = 'SECONDARY',
}

interface RoundedButtonProps extends Omit<LinearGradientProps, 'colors'> {
  label: string;
  onPress?: () => Promise<void>;
  type?: ButtonType;
  loading?: boolean;
  icon?: ImageSourcePropType;
  disabled?: boolean;
  center?: boolean;
  color?: string;
  innerStyle?: StyleProp<ViewStyle>;
  transparent?: boolean;
  iconPosition?: 'left' | 'right';
  capitalize?: boolean;
}

export function RoundedButton(props: RoundedButtonProps) {
  const {
    label,
    loading = false,
    transparent = false,
    onPress,
    type = ButtonType.PRIMARY,
    style,
    icon,
    disabled,
    center,
    innerStyle,
    iconPosition = 'right', // ✅ DEFAULT VALUE
    capitalize = true,
    ...rest
  } = props;

  return (
    <View style={style}>
      <View
        style={[
          {
            borderRadius: 8,
            borderWidth: 1,
            borderColor:
              type === ButtonType.SECONDARY
                ? (props.color ?? Colors.Yellow)
                : undefined,
          },
        ]}>
        <LinearGradient
          style={[
            {
              borderRadius: 8,
              height: 56,
              width: '100%',
            },
            innerStyle,
          ]}
          colors={
            transparent
              ? ['rgba(0,0,0,0)', 'rgba(0,0,0,0)']
              : type === ButtonType.SECONDARY
                ? [Colors.Yellow950, Colors.Yellow950]
                : props.color !== undefined
                  ? [props.color, props.color]
                  : [
                      updateColorWithOpacity(
                        Colors.Yellow750,
                        disabled ? 0.6 : 1,
                      ),
                      updateColorWithOpacity('#FFD600', disabled ? 0.6 : 1),
                    ]
          }
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}
          {...rest}>
          <Pressable
            disabled={disabled || loading}
            style={({pressed}) => [
              {
                flex: 1,
                padding: 16,
                paddingVertical: 8,
                alignItems: center ? 'center' : 'flex-start',
                opacity: pressed ? 0.5 : 1,
              },
            ]}
            onPress={onPress}
            {...rest}>
            <View
              style={{
                flex: 1,
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: center ? 'center' : 'space-between',
              }}>
              {/* Icon on the left (if specified and not loading) */}
              {iconPosition === 'left' && !loading && icon && (
                <Image
                  source={icon}
                  style={{
                    width: 24,
                    height: 24,
                    resizeMode: 'contain',
                    marginRight: 8,
                    opacity: disabled ? 0.4 : 1,
                  }}
                />
              )}

              {/* Label text */}
              {!(center && loading) && (
                <UixText
                  variant="dotsBold"
                  style={{
                    fontSize: 20,
                    lineHeight: 18,
                    borderRadius: 8,
                    color:
                      type === ButtonType.SECONDARY
                        ? props.color
                          ? props.color
                          : disabled
                            ? Colors.Yellow700
                            : Colors.Yellow
                        : disabled
                          ? Colors.Black700
                          : Colors.Black,
                  }}
                  capitalize={capitalize}>
                  {label}
                </UixText>
              )}

              {/* Loading spinner OR right icon */}
              {loading ? (
                <ActivityIndicator
                  style={{
                    width: 24,
                    height: 24,
                    marginLeft: iconPosition === 'right' ? 8 : 0,
                    marginRight: iconPosition === 'left' ? 8 : 0,
                  }}
                  color={
                    type === ButtonType.PRIMARY ? Colors.Black : Colors.Yellow
                  }
                />
              ) : (
                iconPosition === 'right' &&
                icon && (
                  <Image
                    source={icon}
                    style={{
                      width: 24,
                      height: 24,
                      resizeMode: 'contain',
                      marginLeft: 8,
                      opacity: disabled ? 0.4 : 1,
                    }}
                  />
                )
              )}
            </View>
          </Pressable>
        </LinearGradient>
      </View>
    </View>
  );
}
