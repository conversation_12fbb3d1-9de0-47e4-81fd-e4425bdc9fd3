import React, {PropsWithChildren, useMemo} from 'react';
import {View} from 'react-native';
import Animated from 'react-native-reanimated';

import useAnimated from './useAnimated';

interface MarqueeProps {
  noOfRep?: number;
  speed?: number;
}

export function Marquee(props: PropsWithChildren<MarqueeProps>) {
  const {noOfRep = 2, speed = 5000} = props;

  const tempArr = useMemo(
    () => Array.from({length: noOfRep - 1}, () => 0),
    [noOfRep],
  );

  const {onLayout, animatedStyle} = useAnimated(noOfRep > 2 ? 2 : 1, speed);

  return (
    <Animated.View style={animatedStyle}>
      <View onLayout={onLayout}>{props.children}</View>
      {tempArr.map((_, index) => {
        return <View key={index}>{props.children}</View>;
      })}
    </Animated.View>
  );
}
