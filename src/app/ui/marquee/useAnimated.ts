import {useEffect, useState} from 'react';
import {LayoutChangeEvent} from 'react-native';
import {
  cancelAnimation,
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

import Dimension from '@/app/utils/dimension';

export default function useAnimated(
  widthMultiplier: number = 1,
  speed: number = 5000,
) {
  const animated = useSharedValue(0);
  const [width, setWidth] = useState(0);

  const onLayout = (event: LayoutChangeEvent) => {
    setWidth(event.nativeEvent.layout.width * widthMultiplier);
  };

  useEffect(() => {
    cancelAnimation(animated);

    const totalScreenContent = width / Dimension.SCREEN_WIDTH;
    const duration = totalScreenContent * speed;

    animated.value = 0;
    animated.value = withRepeat(
      withTiming(width, {
        duration,
        easing: Easing.linear,
      }),
      -1,
      false,
    );
  }, [width, speed, animated]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      flexDirection: 'row',
      transform: [{translateX: -animated.value}],
    };
  }, [width]);

  return {onLayout, animatedStyle};
}
