import React, {useState} from 'react';
import Video, {ReactVideoProps} from 'react-native-video';
import convertToProxyURL from 'react-native-video-cache-control';

interface Props extends Omit<ReactVideoProps, 'source'> {
  url: string;
}

export function UixVideo(props: Props) {
  const {url, ...rest} = props;

  const [error, setError] = useState(false);

  return (
    <Video
      {...rest}
      ignoreSilentSwitch="ignore"
      source={{uri: error ? url : convertToProxyURL({url: url})}}
      disableFocus={true}
      onError={() => {
        setError(true);
      }}
    />
  );
}
