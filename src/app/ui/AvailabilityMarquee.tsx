import React from 'react';
import {View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {Marquee} from '@/app/ui/marquee/Marquee';
import {UixText} from '@/app/ui/UixText';

interface Props {
  isActive: boolean;
}

export function AvailabilityMarquee(props: Props) {
  const {isActive} = props;

  return (
    <View
      style={{
        position: 'absolute',
        bottom: 5,
        left: 0,
        overflow: 'hidden',
        right: 0,
        backgroundColor: isActive ? Colors.Green950 : Colors.White950,
      }}>
      <Marquee noOfRep={4} speed={10000}>
        <UixText
          capitalize
          variant="dotsBold"
          style={{
            fontSize: 16,
            lineHeight: 14,
            paddingVertical: 4,
            color: isActive ? '#08FE03' : Colors.White,
          }}>
          {isActive ? 'Available / ' : 'Offline / '}
        </UixText>
      </Marquee>
    </View>
  );
}
