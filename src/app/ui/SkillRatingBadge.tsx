import SkillBG from '@assets/skillBG.png';
import React from 'react';
import {ImageBackground, Pressable} from 'react-native';

import {Colors} from '../theme/colors';
import {UixText} from './UixText';

import Dimension from '@/app/utils/dimension';
import useMemberContext from '@/contexts/MemberContext';
import {Members_Member_Skills} from '@/types/__generated__/graphql';

export default function SkillRatingBadge(props: {
  skill: Members_Member_Skills;
  onPress: (skill: Members_Member_Skills) => void;
  width?: number;
  currentUserOnly?: boolean;
  opacity?: number;
}) {
  const {
    skill,
    onPress,
    width = Dimension.SCREEN_WIDTH / 4,
    currentUserOnly = false,
    opacity,
  } = props;

  const {isCurrentUser} = useMemberContext();

  const press = () => {
    if (!currentUserOnly || isCurrentUser) {
      onPress(skill);
    }
  };

  return (
    <Pressable
      onPress={press}
      style={{
        justifyContent: 'flex-start',
        alignItems: 'center',
      }}>
      <ImageBackground
        source={SkillBG}
        style={{
          aspectRatio: 65 / 75,
          width: width,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        imageStyle={{opacity: opacity ? opacity : skill.rating ? 0.7 : 0.3}}>
        {skill.rating ? (
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 48,
              lineHeight: 40,
              paddingLeft: 6,
            }}
            capitalize>
            {`${skill.rating}`}
          </UixText>
        ) : (
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 20,
              lineHeight: 18,
              paddingLeft: 2,
              textAlign: 'center',
              color: Colors.White400,
            }}
            capitalize>
            {isCurrentUser ? 'TAP TO RATE' : 'NOT RATED'}
          </UixText>
        )}
      </ImageBackground>
      <UixText
        variant="dotsBold"
        numberOfLines={2}
        ellipsizeMode="tail"
        style={{
          color: Colors.Teal,
          fontSize: 16,
          marginTop: 8,
          textAlign: 'center',
          width: width,
          overflow: 'hidden',
        }}
        capitalize>
        {skill.skill_detail.title}
      </UixText>
    </Pressable>
  );
}
