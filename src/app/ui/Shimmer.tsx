import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder, {
  ShimmerPlaceholderProps,
} from 'react-native-shimmer-placeholder';

import {updateColorWithOpacity} from '../theme/utils';

export function Shimmer(props: ShimmerPlaceholderProps) {
  return (
    <ShimmerPlaceholder
      LinearGradient={LinearGradient}
      shimmerColors={['#002922', updateColorWithOpacity('#807A40', 0.1)]}
      location={[0.3, 0.7]}
      {...props}
    />
  );
}
