import CloseIcon from '@assets/cross.svg';
import React, {useCallback, useState} from 'react';
import {NativeSyntheticEvent, TextInputChangeEventData} from 'react-native';
import {TextInput} from 'react-native-paper';

import useMeasureRenderTime from '../hooks/useMeasureRenderTime';

import {Colors} from '@/app/theme/colors';
import {UixTextInput} from '@/app/ui/UixTextInput';
import {debounce} from 'lodash';

export interface ISearchBarProps {
  searchText?: string;
  disabled?: boolean;
  onSearchText: (searchText: string) => void;
  minCharacter?: number;
}

export default function SearchBar(props: ISearchBarProps) {
  const {
    searchText = '',
    onSearchText,
    minCharacter = 1,
    disabled = false,
  } = props;
  const [value, setValue] = useState(searchText);

  const onSearch = useCallback(
    debounce((text: string) => {
      if (!text) {
        onSearchText?.('');
      }

      if (text?.length < minCharacter) {
        return;
      }

      onSearchText?.(text);
    }, 300),
    [onSearchText],
  );

  const onChangeText = ({
    nativeEvent: {text},
  }: NativeSyntheticEvent<TextInputChangeEventData>) => {
    setValue(text);
    onSearch(text);
  };

  useMeasureRenderTime('SearchBar');

  return (
    <UixTextInput
      mode="outlined"
      placeholder="SEARCH MEMBERS"
      autoFocus={Boolean(searchText)}
      textContentType="name"
      outlineColor={Colors.Teal900}
      activeOutlineColor={Colors.Teal800}
      textColor={Colors.Teal}
      value={value}
      onChange={onChangeText}
      placeholderTextColor={Colors.Teal800}
      style={{backgroundColor: Colors.Teal950}}
      disabled={disabled}
      right={
        searchText.length > 0 ? (
          <TextInput.Icon
            icon={() => <CloseIcon />}
            onPress={() => {
              setValue('');
              onSearch('');
            }}
          />
        ) : null
      }
    />
  );
}
