import {
  GestureResponderEvent,
  Image,
  ImageSourcePropType,
  StyleProp,
  ViewStyle,
} from 'react-native';
import {Button, ButtonProps} from 'react-native-paper';

import {Colors} from '../theme/colors';
import {UixCornerView} from './UixCornerView';
import {UixText} from './UixText';

interface UixButtonProps extends Omit<ButtonProps, 'children'> {
  children?: React.ReactNode;
  viewStyle?: StyleProp<ViewStyle>;
  variant?: 'masked' | 'rounded';
  tintColor?: 'Teal' | 'White' | 'Orange';
  label?: string;
  iconImage?: ImageSourcePropType;
  onPress?: (e: GestureResponderEvent) => void;
}

export const UixButton = ({
  tintColor = 'White',
  viewStyle,
  children,
  label,
  iconImage,
  onPress,
  ...props
}: UixButtonProps) => {
  return (
    <UixCornerView
      color={
        tintColor === 'Teal'
          ? Colors.Teal950
          : tintColor === 'White'
            ? Colors.White950
            : Colors.Orange1000
      }
      viewStyle={viewStyle}>
      <Button
        {...props}
        mode="contained"
        onPress={onPress}
        buttonColor={'transparent'}
        icon={
          iconImage
            ? () => (
                <Image
                  source={iconImage}
                  style={{
                    width: 24,
                    height: 24,
                    tintColor:
                      tintColor === 'Teal'
                        ? Colors.Teal
                        : tintColor === 'White'
                          ? Colors.White
                          : Colors.Orange,
                  }}
                />
              )
            : undefined
        }
        style={[props.style, {alignSelf: 'flex-start', borderRadius: 0}]}>
        {label && (
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 22,
              lineHeight: 20,
              color:
                tintColor === 'Teal'
                  ? Colors.Teal
                  : tintColor === 'White'
                    ? Colors.White
                    : Colors.Orange,
            }}
            capitalize>
            {label}
          </UixText>
        )}
        {children}
      </Button>
    </UixCornerView>
  );
};
