import MaskedView from '@react-native-masked-view/masked-view';
import React, {useCallback, useMemo, useState} from 'react';
import {LayoutChangeEvent, StyleProp, View, ViewStyle} from 'react-native';
import Svg, {ClipPath, Defs, Path, Rect} from 'react-native-svg';

type CutType = 'all' | 'tlbr' | 'bltr' | 'tl' | 'br' | 'bl';

const CUTS: Record<CutType, [number, number, number, number]> = {
  all: [1, 1, 1, 1],
  tlbr: [1, 0, 1, 0],
  bltr: [0, 1, 0, 1],
  tl: [1, 0, 0, 0],
  br: [0, 0, 1, 0],
  bl: [0, 0, 0, 1],
};

export const UixCornerView = React.memo(
  ({
    children,
    color,
    background,
    viewStyle,
    type = 'tlbr',
    cut = 0.3,
    borderRadius = 0,
  }: {
    children: React.ReactNode;
    color?: string;
    background?: React.ReactNode;
    viewStyle?: StyleProp<ViewStyle>;
    type?: CutType;
    cut?: number;
    borderRadius?: number;
  }) => {
    const [dims, setDims] = useState({width: 100, height: 100});

    const handleLayout = useCallback((event: LayoutChangeEvent) => {
      const {width, height} = event.nativeEvent.layout;
      setDims({width, height});
    }, []);

    const getCutPath = useCallback(
      (width: number, height: number, type: CutType): string => {
        const cutW = cut * height;
        const [topLeft, topRight, bottomRight, bottomLeft] = CUTS[type].map(
          v => v * cutW,
        );

        return `
      M${topLeft},0 L${width - topRight},0 L${width},${topRight} 
      L${width},${height - bottomRight} L${width - bottomRight},${height} 
      L${bottomLeft},${height} L0,${height - bottomLeft} L0,${topLeft} Z
    `;
      },
      [cut],
    );

    const path = useMemo(
      () => getCutPath(dims.width, dims.height, type),
      [getCutPath, dims.width, dims.height, type],
    );

    const containerStyle = useMemo<StyleProp<ViewStyle>>(
      () => [
        viewStyle,
        {
          alignSelf: 'flex-start' as const,
          flexShrink: 1,
          position: 'relative' as const,
          flexDirection: 'row' as const,
          backgroundColor: 'transparent',
        },
      ],
      [viewStyle],
    );

    const maskedViewStyle = useMemo<StyleProp<ViewStyle>>(
      () => ({
        position: 'absolute' as const,
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
      }),
      [],
    );

    const backgroundStyle = useMemo<StyleProp<ViewStyle>>(
      () => ({
        position: 'absolute' as const,
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
        backgroundColor: color,
        borderRadius: borderRadius,
        overflow: 'hidden' as const,
      }),
      [color, borderRadius],
    );

    return (
      <View style={containerStyle}>
        <MaskedView
          key={`${dims.width}-${dims.height}`}
          style={maskedViewStyle}
          maskElement={
            <View onLayout={handleLayout}>
              <Svg
                width="100%"
                height="100%"
                viewBox={`0 0 ${dims.width} ${dims.height}`}>
                <Defs>
                  <ClipPath id="cutout">
                    <Path d={path} />
                  </ClipPath>
                </Defs>
                <Rect
                  width="100%"
                  height="100%"
                  fill="black"
                  clipPath="url(#cutout)"
                />
              </Svg>
            </View>
          }>
          <View style={backgroundStyle}>{background}</View>
        </MaskedView>
        {children}
      </View>
    );
  },
);

UixCornerView.displayName = 'UixCornerView';
