import React from 'react';
import {TextProps} from 'react-native';
import {customText} from 'react-native-paper';
import {VariantProp} from 'react-native-paper/lib/typescript/components/Typography/types';

const UixTextInner = customText<'dots' | 'dotsBold' | 'dotsLight'>();

interface UixTextProps extends TextProps {
  children: string;
  variant?: VariantProp<'dots' | 'dotsBold' | 'dotsLight'> | undefined;
  capitalize?: boolean;
}

export const UixText = ({
  children,
  variant,
  capitalize = false,
  ...props
}: UixTextProps) => {
  return (
    <UixTextInner {...props} variant={variant}>
      {capitalize ? children.toUpperCase() : children}
    </UixTextInner>
  );
};
