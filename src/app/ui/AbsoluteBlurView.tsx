import {BlurView} from '@react-native-community/blur';
import {isIOS} from '@/app/utils/utils';
import {View} from 'react-native';

export function AbsoluteBlurView() {
  return (
    <View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        backgroundColor: 'transparent',
      }}>
      {isIOS && (
        <BlurView
          blurType={'ultraThinMaterialDark'}
          blurAmount={32}
          reducedTransparencyFallbackColor="black"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
          }}
        />
      )}
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          backgroundColor: isIOS ? 'rgba(0, 0, 0, 0.5)' : 'rgba(5, 7, 7, 0.9)',
        }}
      />
    </View>
  );
}
