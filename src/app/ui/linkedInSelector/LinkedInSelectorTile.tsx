import {Pressable, View} from 'react-native';

import {UixText} from '../UixText';
import {PersonIcon, SearchIcon} from './LinkedInSelectorIcons';
import {getHighLightedIndex} from './utils';

import {Colors} from '@/app/theme/colors';
import {UixImage} from '@/app/ui/uixImage/UixImage';
import {LinkedinProfile} from '@/types/__generated__/graphql';

export function LinkedInSelectorTile({
  textInput,
  profile,
  color,
  onPress,
}: {
  textInput: string;
  profile: LinkedinProfile;
  color: string;
  onPress: () => void;
}) {
  const highlightIndex = getHighLightedIndex(
    textInput?.toLowerCase() ?? '',
    profile.name.toLowerCase(),
  );

  return (
    <Pressable
      onPress={onPress}
      style={({pressed}) => ({
        paddingHorizontal: 16,
        gap: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        opacity: pressed ? 0.5 : 1,
      })}>
      <SearchIcon stroke={color} />
      <View
        style={{
          flexDirection: 'row',
          paddingLeft: 6,
          overflow: 'hidden',
          flex: 1,
          justifyContent: 'flex-start',
        }}>
        {`${profile.name} | ${profile.location}`
          .split('')
          .map((char, index) => (
            <UixText
              variant="bodyMedium"
              key={index}
              style={{
                color: index < highlightIndex ? color : Colors.White,
              }}>
              {char}
            </UixText>
          ))}
      </View>
      {profile.photo ? (
        <UixImage
          source={{uri: profile.photo}}
          style={{width: 40, height: 40, borderRadius: 20}}
        />
      ) : (
        <PersonIcon size={40} color={color} />
      )}
    </Pressable>
  );
}
