import Svg, {Circle, Path} from 'react-native-svg';

export const PersonIcon = ({size, color}: {size: number; color: string}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 100 100">
      <Circle
        cx="50"
        cy="50"
        r="48"
        stroke={color}
        strokeWidth="4"
        fill="transparent"
      />
      <Circle cx="50" cy="35" r="15" fill={color} />
      <Path
        d="M 50 60 C 30 60, 30 80, 50 80 C 70 80, 70 60, 50 60"
        fill={color}
      />
    </Svg>
  );
};

export const SearchIcon = ({
  stroke,
  size = 24,
}: {
  stroke: string;
  size?: number;
}) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={'none'}>
    <Path
      d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
      stroke={stroke}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={'none'}
    />
    <Path
      d="M20.9999 21.0004L16.6499 16.6504"
      stroke={stroke}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
