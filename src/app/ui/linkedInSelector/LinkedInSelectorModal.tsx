import {debounce} from 'lodash';
import {useCallback, useState} from 'react';
import {
  ActivityIndicator,
  Keyboard,
  NativeSyntheticEvent,
  TextInputChangeEventData,
  View,
} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';

import {FullScreenModal} from '../FullScreenModal';
import {UixButton} from '../UixButton';
import {UixText} from '../UixText';
import {UixTextInput} from '../UixTextInput';
import {LinkedInSelectorTile} from './LinkedInSelectorTile';

import useUixQuery from '@/app/hooks/useUixQuery';
import {Colors} from '@/app/theme/colors';
import {LinkedInSearchQuery} from '@/graphQL/linkedIn';
import {LinkedinSearchOutput} from '@/types/__generated__/graphql';

interface LinkedinQueryResponse {
  LinkedinSearch: LinkedinSearchOutput;
}

export function LinkedInSelectorModal({
  isOpen,
  value,
  setIsOpen,
  onChange,
  onDone,
  color,
}: {
  isOpen: boolean;
  value: string;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onChange: (text: string) => void;
  onDone: (text: string) => void;
  color: 'Teal' | 'Orange';
}) {
  const [textInput, setTextInput] = useState(value);

  const primaryColor = color === 'Teal' ? Colors.Teal : Colors.Orange;
  const secondaryColor = color === 'Teal' ? Colors.Teal800 : Colors.Orange900;
  const tertiaryColor = color === 'Teal' ? Colors.Teal950 : Colors.Orange1000;

  const {data, loading, refetch} = useUixQuery<LinkedinQueryResponse>(
    LinkedInSearchQuery,
    {
      variables: {
        search: textInput,
      },
      skip: textInput == '',
    },
  );

  const debouncedFetch = useCallback(
    debounce((text: string) => {
      setTextInput(text);
    }, 1000),
    [refetch],
  );

  const shouldRender = loading || data?.LinkedinSearch;
  const profiles = data?.LinkedinSearch?.data ?? [];

  const handleOnchange = useCallback(
    ({nativeEvent: {text}}: NativeSyntheticEvent<TextInputChangeEventData>) => {
      if (text) debouncedFetch(text);
    },
    [debouncedFetch],
  );

  const EmptyListPlaceHolder = () => (
    <UixText
      variant="dotsBold"
      style={{
        fontSize: 16,
        lineHeight: 14,
        color: secondaryColor,
        textAlign: 'center',
      }}
      capitalize>
      {'No Results Found'}
    </UixText>
  );

  return (
    <FullScreenModal
      isOpen={isOpen}
      onClose={() => {
        Keyboard.dismiss();
        setIsOpen(false);
      }}>
      <View
        style={{
          gap: 10,
          padding: 16,
        }}>
        <UixButton
          label="Close"
          iconImage={require('@assets/cross.png')}
          onPress={() => {
            Keyboard.dismiss();
            setIsOpen(false);
          }}
          tintColor={color}
        />
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 20,
            lineHeight: 16,
            marginTop: 20,
            color: primaryColor,
            marginBottom: 8,
          }}
          capitalize>{`Linkedin`}</UixText>
        <View
          style={{
            backgroundColor: tertiaryColor,
            borderRadius: 8,
            borderWidth: 2,
            borderColor: isOpen ? primaryColor : undefined,
          }}>
          <UixTextInput
            defaultValue={value}
            mode="outlined"
            placeholder="www.linkedin.com/in/JohnDoe"
            textContentType="URL"
            outlineStyle={{borderWidth: 0}}
            outlineColor={tertiaryColor}
            activeOutlineColor={primaryColor}
            textColor={primaryColor}
            onChange={handleOnchange}
            placeholderTextColor={secondaryColor}
            style={{backgroundColor: 'transparent'}}
          />
          {shouldRender && textInput && (
            <FlatList
              scrollEnabled={false}
              keyboardShouldPersistTaps={'handled'}
              data={loading ? [] : profiles}
              keyExtractor={item => item.profile_url}
              ListEmptyComponent={
                loading ? (
                  <ActivityIndicator size="small" />
                ) : (
                  <EmptyListPlaceHolder />
                )
              }
              contentContainerStyle={{
                gap: 12,
                minHeight: 20,
                justifyContent: 'center',
                marginBottom: 16,
              }}
              renderItem={({item}) => (
                <LinkedInSelectorTile
                  textInput={textInput}
                  profile={item}
                  color={primaryColor}
                  onPress={() => {
                    onChange(item.profile_url);
                    onDone(`${item.name} | ${item.location}`);
                    setIsOpen(false);
                  }}
                />
              )}
            />
          )}
        </View>
      </View>
    </FullScreenModal>
  );
}
