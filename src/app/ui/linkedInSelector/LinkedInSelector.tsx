import {useState} from 'react';
import {View} from 'react-native';
import {TextInput} from 'react-native-paper';

import {Colors} from '../../theme/colors';
import {ThreeDotsLoader} from '../ThreeDotsLoader';
import {UixText} from '../UixText';
import {UixTextInput} from '../UixTextInput';
import {LinkedInSelectorModal} from './LinkedInSelectorModal';

type VerificationStatus = 'idle' | 'verifying' | 'verified' | 'failed';

export function LinkedinSelector({
  value,
  onChange,
  color,
  errored = false,
}: {
  value: string;
  onChange: (text: string) => void;
  color: 'Teal' | 'Orange';
  errored?: boolean;
}) {
  const primaryColor = color === 'Teal' ? Colors.Teal : Colors.Orange;
  const secondaryColor = color === 'Teal' ? Colors.Teal800 : Colors.Orange900;
  const tertiaryColor = color === 'Teal' ? Colors.Teal950 : Colors.Orange1000;
  const [selectedText, setSelectedText] = useState(value);
  const [isOpen, setIsOpen] = useState(false);
  const [verificationStatus, setVerificationStatus] =
    useState<VerificationStatus>('idle');

  const verifyLinkedInProfile = async (text: string) => {
    let cleanUrl = text.trim();
    if (!cleanUrl.startsWith('http')) {
      cleanUrl = 'https://' + cleanUrl;
    }

    if (!cleanUrl.includes('linkedin.com/in/')) {
      setVerificationStatus('failed');
      setSelectedText(text);
      onChange('');
      return;
    }

    setVerificationStatus('verifying');
    setSelectedText(cleanUrl);

    try {
      const identifier = cleanUrl
        .split('linkedin.com/in/')[1]
        .split('/')[0]
        .split('?')[0];

      const response = await fetch(
        'https://api-ops.uixlabs.co/admin/get-linkedin-profile',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({identifier}),
        },
      );

      const data = await response.json();

      if (data?.data?.name) {
        onChange(cleanUrl);
        setSelectedText(data.data.name);
        setVerificationStatus('verified');
      } else {
        setSelectedText(cleanUrl);
        setVerificationStatus('failed');
        onChange('');
      }
    } catch (error) {
      console.error('Error verifying LinkedIn profile:', error);
      setSelectedText(cleanUrl);
      setVerificationStatus('failed');
      onChange('');
    }
  };

  const handleTextChange = (text: string) => {
    const oldSelectedText = selectedText;
    setSelectedText(text);

    const isLinkedInUrl = text.includes('linkedin.com/in/');

    if (isLinkedInUrl) {
      setVerificationStatus('idle');
      verifyLinkedInProfile(text);
    } else if (text.length === 0) {
      onChange('');
      setVerificationStatus('idle');
    } else {
      setVerificationStatus('failed');

      if (text.length > oldSelectedText.length) {
        setIsOpen(true);
        onChange('');
      } else {
        onChange(text);
      }
    }
  };

  return (
    <View>
      <View style={{gap: 16}}>
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 20,
            lineHeight: 16,
            marginTop: 20,
            color: primaryColor,
          }}
          capitalize>{`Linkedin`}</UixText>
        <View style={{flexDirection: 'row', gap: 8}}>
          <UixTextInput
            editable={true}
            mode="outlined"
            placeholder="www.linkedin.com/in/JohnDoe"
            textContentType="URL"
            outlineColor={
              errored || verificationStatus === 'failed'
                ? Colors.Error
                : tertiaryColor
            }
            activeOutlineColor={
              errored || verificationStatus === 'failed'
                ? Colors.Error
                : primaryColor
            }
            right={
              verificationStatus === 'verifying' && (
                <TextInput.Icon
                  icon={() => (
                    <ThreeDotsLoader
                      color={primaryColor}
                      size={6}
                      style={{
                        alignSelf: 'center',
                      }}
                    />
                  )}
                  forceTextInputFocus={false}
                />
              )
            }
            textColor={primaryColor}
            value={selectedText}
            onChangeText={handleTextChange}
            returnKeyType="done"
            placeholderTextColor={secondaryColor}
            style={{
              backgroundColor: tertiaryColor,
              height: 56,
              flex: 1,
            }}
          />
        </View>
      </View>
      {isOpen && verificationStatus !== 'verified' && (
        <LinkedInSelectorModal
          isOpen={isOpen}
          value={value}
          setIsOpen={setIsOpen}
          onChange={onChange}
          color={color}
          onDone={profileUrl => {
            setSelectedText(profileUrl);
            onChange(profileUrl);
            setIsOpen(false);
            setVerificationStatus('verified');
          }}
        />
      )}
    </View>
  );
}
