import {pick} from '@react-native-documents/picker';
import {Platform} from 'react-native';

export const pickDocument = async () => {
  const pickerOptions = {
    allowMultiSelection: false,
    type: Platform.select({
      ios: [
        'com.adobe.pdf',
        'public.text',
        'public.content',
        'public.data',
        'public.item',
        'com.microsoft.word.doc',
        'org.openxmlformats.wordprocessingml.document',
        'com.microsoft.excel.xls',
        'org.openxmlformats.spreadsheetml.sheet',
        'com.microsoft.powerpoint.ppt',
        'org.openxmlformats.presentationml.presentation',
        'public.image',
      ],
      android: ['*/*'],
    }),
  };

  const result = await pick(pickerOptions);
  if (!result || result.length === 0) {
    console.log('No file selected');
    return null;
  }

  return result[0];
};
