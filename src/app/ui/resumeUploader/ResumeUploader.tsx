import {DocumentPickerResponse, pick} from '@react-native-documents/picker';
import React, {useState} from 'react';
import {Image, Platform, Pressable, Text} from 'react-native';

import {Colors} from '@/app/theme/colors';

interface ResumeUploaderProps {
  backgroundColor?: string;
  color?: string;
  onFileChange: (file: DocumentPickerResponse | undefined) => void;
  errored?: boolean;
}

const ResumeUploader: React.FC<ResumeUploaderProps> = ({
  backgroundColor = Colors.Orange1000,
  color = Colors.Orange900,
  onFileChange,
  errored = false,
}) => {
  const [selectedFileName, setSelectedFileName] = useState('');

  const selectDoc = async () => {
    try {
      const result = await pick({
        allowMultiSelection: false,
        type: Platform.select({
          ios: ['public.image', 'com.adobe.pdf'],
          android: ['image/*', 'application/pdf'],
        }),
      });

      const fileName = result[0].name || `file-${Date.now()}`;
      setSelectedFileName(fileName);

      const fileUri = Platform.select({
        ios: decodeURIComponent(result[0].uri),
        android: result[0].uri,
      });

      if (!fileUri) {
        setSelectedFileName('');
        return;
      }

      onFileChange(result?.[0]);
    } catch (e) {
      console.error('Error uploading resume:', e);
      setSelectedFileName('');
      onFileChange(undefined);
    }
  };

  const removeDoc = () => {
    setSelectedFileName('');
    onFileChange(undefined);
  };

  const handlePress = selectedFileName ? removeDoc : selectDoc;

  return (
    <Pressable
      style={{
        backgroundColor: backgroundColor,
        padding: 16,
        display: 'flex',
        justifyContent: 'space-between',
        borderRadius: 8,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: errored ? Colors.Error : 'transparent',
      }}
      onPress={handlePress}>
      {selectedFileName ? (
        <>
          <Text style={{color: color}}>{selectedFileName}</Text>
          <Image
            source={require('@assets/DeleteIcon.png')}
            style={{tintColor: color, width: 20, height: 20}}
          />
        </>
      ) : (
        <>
          <Text style={{color: color}}>No File Selected</Text>
          <Image
            source={require('@assets/UploadIcon.png')}
            style={{tintColor: color, width: 20, height: 20}}
          />
        </>
      )}
    </Pressable>
  );
};

export default ResumeUploader;
