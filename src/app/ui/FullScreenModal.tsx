import {ImageBackground, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {useKeyboardVisible} from '../screens/utils';

import {isIOS} from '@/app/utils/utils';

export function FullScreenModal({
  isOpen,
  onClose,
  backgroundColor,
  children,
}: {
  isOpen: boolean;
  onClose: () => void;
  backgroundColor?: string;
  children: React.ReactNode;
}) {
  const insets = useSafeAreaInsets();
  const isKeyboardVisible = useKeyboardVisible();
  return (
    <View>
      <Modal
        isVisible={isOpen}
        style={{
          margin: 0,
        }}
        onBackButtonPress={onClose}
        onBackdropPress={onClose}>
        <ImageBackground
          source={
            backgroundColor ? undefined : require('@assets/background.png')
          }
          style={{
            flexGrow: 1,
            paddingTop: isIOS ? insets.top : 0,
            paddingBottom: isKeyboardVisible ? 0 : insets.bottom,
            backgroundColor: backgroundColor,
          }}>
          <KeyboardAwareScrollView
            contentContainerStyle={{
              flexGrow: 1,
            }}
            keyboardShouldPersistTaps={'handled'}
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled>
            {children}
          </KeyboardAwareScrollView>
        </ImageBackground>
      </Modal>
    </View>
  );
}
