import MaskedView from '@react-native-masked-view/masked-view';
import React from 'react';
import {StyleProp, TextStyle} from 'react-native';
import LinearGradient, {
  LinearGradientProps,
} from 'react-native-linear-gradient';

import {UixText} from './UixText';

import {Colors} from '@/app/theme/colors';

interface GradientTextProps {
  text: string;
  colors?: string[];
  style?: StyleProp<TextStyle>;
  gradientProps?: Omit<LinearGradientProps, 'colors'>;
}

const GradientText = ({
  text,
  colors = [Colors.Yellow750, Colors.Yellow150],
  style,
  gradientProps,
}: GradientTextProps) => {
  return (
    <MaskedView maskElement={<UixText style={[style]}>{text}</UixText>}>
      <LinearGradient
        colors={colors}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        {...gradientProps}>
        <UixText style={[style, {opacity: 0}]}>{text}</UixText>
      </LinearGradient>
    </MaskedView>
  );
};

export default GradientText;
