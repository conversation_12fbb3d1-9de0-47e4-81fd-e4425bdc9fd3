import React from 'react';
import {View} from 'react-native';
import {SvgProps} from 'react-native-svg';

import {Colors} from '../theme/colors';
import {UixButton} from './UixButton';
import {UixText} from './UixText';

interface Props {
  Icon: React.FC<SvgProps>;
  label: string;
  onPress?: () => void;
}

export default function UixButtonWithIcon(props: Props) {
  const {Icon, label, onPress} = props;

  return (
    <UixButton tintColor="Teal" onPress={onPress}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Icon />
        <UixText
          variant="dotsBold"
          style={{
            lineHeight: 20,
            fontSize: 22,
            color: Colors.Teal,
            marginLeft: 12,
          }}>
          {label}
        </UixText>
      </View>
    </UixButton>
  );
}
