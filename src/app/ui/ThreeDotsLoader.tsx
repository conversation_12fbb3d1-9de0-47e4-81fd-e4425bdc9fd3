import React, {useEffect, useRef} from 'react';
import {Animated, StyleProp, StyleSheet, View, ViewStyle} from 'react-native';

interface ThreeDotsLoaderProps {
  color?: string;
  size?: number;
  style?: StyleProp<ViewStyle>;
}

export function ThreeDotsLoader({
  color,
  size = 8,
  style = {},
}: ThreeDotsLoaderProps) {
  const anims = [
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
  ];

  useEffect(() => {
    const animations = anims.map((anim, i) =>
      Animated.loop(
        Animated.sequence([
          Animated.delay(i * 120),
          Animated.timing(anim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]),
      ),
    );
    animations.forEach(anim => anim.start());
    return () => {
      animations.forEach(anim => anim.stop());
    };
  }, []);

  return (
    <View style={[styles.container, style]}>
      {anims.map((anim, i) => (
        <Animated.View
          key={i}
          style={[
            styles.dot,
            {
              width: size,
              height: size,
              backgroundColor: color,
              marginHorizontal: size / 3,
              opacity: anim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.3, 1],
              }),
              transform: [
                {
                  scale: anim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.5],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    borderRadius: 50,
  },
});
