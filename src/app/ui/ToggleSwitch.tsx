import {useEffect, useRef} from 'react';
import {Animated, Pressable, View} from 'react-native';
import {SvgProps} from 'react-native-svg';

import {Colors} from '../theme/colors';
import {updateColorWithOpacity} from '../theme/utils';

export const ToggleSwitch = ({
  switchOn,
  toggleSwitch,
  highlightColor = switchOn ? Colors.Yellow : Colors.White300,
  backgroundColor = switchOn ? Colors.Yellow950 : Colors.White950,
  thumbIcon,
}: {
  switchOn: boolean;
  toggleSwitch: () => void;
  highlightColor?: string;
  backgroundColor?: string;
  thumbIcon?: React.FC<SvgProps>;
}) => {
  const animatedValue = useRef(new Animated.Value(switchOn ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: switchOn ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [switchOn]);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 30],
  });

  const Icon = thumbIcon;

  return (
    <Pressable onPress={toggleSwitch}>
      <View
        style={{
          width: 72,
          padding: 4,
          borderRadius: 20,
          borderWidth: 1,
          borderColor: updateColorWithOpacity(highlightColor, 0.5),
          backgroundColor: backgroundColor,
          alignItems: 'flex-start',
        }}>
        <Animated.View
          style={{
            height: 32,
            width: 32,
            borderRadius: 16,
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: highlightColor,
            transform: [{translateX}],
          }}>
          {Icon && <Icon />}
        </Animated.View>
      </View>
    </Pressable>
  );
};
