function addTransformationToURL(url: string, size: number): string {
  if (!url?.includes('cloudinary.com')) {
    return url;
  }
  const bucket = Math.ceil(size / 100) * 100;
  const regex = /upload\/[^/]*[_,][^/]*\//;
  const transformation = `c_fit,w_${bucket}/f_auto/`;
  if (regex.test(url)) {
    return url.replace(regex, `upload/${transformation}`);
  } else {
    return url.replace('upload/', `upload/${transformation}`);
  }
}

const ImageHelper = {
  addTransformationToURL,
};

export default ImageHelper;
