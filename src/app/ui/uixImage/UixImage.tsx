import React, {useMemo} from 'react';
import {PixelRatio} from 'react-native';
import FastImage, {FastImageProps} from 'react-native-fast-image';

import Helper from './helper';

import Dimension from '@/app/utils/dimension';

interface Props extends FastImageProps {
  size?: number;
}

export function UixImage(props: Props) {
  const {source, size = Dimension.SCREEN_WIDTH} = props;

  const imageSize = useMemo(() => {
    return PixelRatio.getPixelSizeForLayoutSize(size);
  }, []);

  const imageSource = useMemo(() => {
    if (typeof source === 'object' && source.uri) {
      return {
        ...source,
        uri: Helper.addTransformationToURL(source.uri, size),
      };
    }

    return source;
  }, [source, imageSize]);

  return <FastImage {...props} source={imageSource} />;
}
