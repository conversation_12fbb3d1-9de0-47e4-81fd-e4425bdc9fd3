import {useEffect, useRef} from 'react';

import {Tracker} from '@/app/analytics/tracker';

export default function useMeasureRenderTime(componentName: string) {
  const startTime = useRef(performance.now());

  useEffect(() => {
    const endTime = performance.now();
    const diff = endTime - startTime.current;
    Tracker.track('Component_Render_Time', {
      component_name: componentName,
      render_time: diff,
    });
  }, [componentName]);

  return null;
}
