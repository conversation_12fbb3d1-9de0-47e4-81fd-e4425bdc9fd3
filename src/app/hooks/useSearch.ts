import {useCallback, useEffect, useRef, useState} from 'react';

export default function useSearch<T>({
  data,
  searchText: initialSearchText = '',
  filter,
}: {
  data: T[];
  searchText?: string;
  filter: (item: T, query: string) => boolean;
}) {
  const [filteredData, setFilteredData] = useState(data);
  const searchTextRef = useRef(initialSearchText);

  const filterData = useCallback(
    (query: string) => {
      const trimmedQuery = query.trim();
      const newData = trimmedQuery
        ? data.filter(item => filter(item, trimmedQuery))
        : data;

      setFilteredData(newData);
    },
    [data, filter],
  );

  useEffect(() => {
    filterData(searchTextRef.current);
  }, [data, filterData]);

  const onSearch = useCallback(
    (query: string) => {
      searchTextRef.current = query;
      filterData(query);
    },
    [filterData],
  );

  useEffect(() => {
    if (initialSearchText !== searchTextRef.current) {
      searchTextRef.current = initialSearchText;
      filterData(initialSearchText);
    }
  }, [initialSearchText, filterData]);

  return {
    filteredData,
    onSearch,
    searchText: searchTextRef.current,
  };
}
