import {
  ApolloCache,
  DefaultContext,
  DocumentNode,
  MutationHookOptions,
  MutationTuple,
  NoInfer,
  OperationVariables,
  TypedDocumentNode,
  useMutation,
} from '@apollo/client';

export default function useUixMutation<
  TData = unknown,
  TVariables = OperationVariables,
  TContext = DefaultContext,
  T<PERSON>ache extends ApolloCache<unknown> = ApolloCache<unknown>,
>(
  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>,
  options?: MutationHookOptions<
    NoInfer<TData>,
    NoInfer<TVariables>,
    TContext,
    TCache
  >,
): MutationTuple<TData, TVariables, TContext, TCache> {
  return useMutation(mutation, options);
}
