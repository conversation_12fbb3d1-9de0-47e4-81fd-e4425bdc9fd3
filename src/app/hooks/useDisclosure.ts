import {useCallback, useState} from 'react';

function useDisclosure(
  initialIsOpen = false,
  callbacks?: {
    onOpen?: () => void;
    onClose?: () => void;
  },
) {
  const {onOpen, onClose} = callbacks || {};
  const [opened, setOpened] = useState(initialIsOpen);

  const open = useCallback(() => {
    setOpened(isOpened => {
      if (!isOpened) {
        onOpen?.();
        return true;
      }
      return isOpened;
    });
  }, [onOpen]);

  const close = useCallback(() => {
    setOpened(isOpened => {
      if (isOpened) {
        onClose?.();
        return false;
      }
      return isOpened;
    });
  }, [onClose]);

  const toggle = useCallback(() => {
    setOpened(isOpened => {
      const newState = !isOpened;
      if (newState) {
        onOpen?.();
      } else {
        onClose?.();
      }
      return newState;
    });
  }, [onOpen, onClose]);

  return {
    opened,
    controls: {
      open,
      close,
      toggle,
    },
  };
}

export default useDisclosure;
