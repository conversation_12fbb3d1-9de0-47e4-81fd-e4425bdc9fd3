import {
  DocumentNode,
  NoInfer,
  OperationVariables,
  QueryHookOptions,
  QueryResult,
  TypedDocumentNode,
  useQuery,
} from '@apollo/client';

export default function useUixQuery<
  TData = unknown,
  TVariables extends OperationVariables = OperationVariables,
>(
  query: DocumentNode | TypedDocumentNode<TData, TVariables>,
  options?: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>,
  log: boolean = false,
): QueryResult<TData, TVariables> {
  const queryResponseObject = useQuery(query, options);
  if (log) {
    console.log('*------------ Query Called ------------*');
    console.log(options);
    console.log(queryResponseObject.data);
    console.log('*---------------------------------------*');
  }
  return queryResponseObject;
}
