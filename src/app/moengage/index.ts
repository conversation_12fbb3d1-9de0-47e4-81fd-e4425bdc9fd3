import store from '@store/store';
import {Platform} from 'react-native';
import ReactMoE, {
  MoEAppStatus,
  MoEInitConfig,
  MoEngageLogConfig,
  MoEngageLogLevel,
  MoEPushConfig,
} from 'react-native-moengage';

const init = () => {
  const moEInitConfig: MoEInitConfig = new MoEInitConfig(
    MoEPushConfig.defaultConfig(),
    new MoEngageLogConfig(MoEngageLogLevel.DEBUG, __DEV__),
  );

  ReactMoE.initialize('LC5Y6GRHHK804DZV3H0TI6T3', moEInitConfig);

  if (store.getState().common.isFirstLaunch) {
    ReactMoE.setAppStatus(MoEAppStatus.Install);
  }

  const user = store.getState().user;

  if (Platform.OS === 'android') {
    ReactMoE.requestPushPermissionAndroid();
  } else {
    ReactMoE.registerForPush();
  }

  if (user) {
    ReactMoE.setUserUniqueID(user.id);
    ReactMoE.setUserFirstName(user.firstName ?? '');
    ReactMoE.setUserLastName(user.lastName ?? '');
    ReactMoE.setUserEmailID(user.email ?? '');
    ReactMoE.setUserContactNumber(user.phone_number ?? '');
  }
};

const onLogin = (phoneNumber: string, userId: string) => {
  ReactMoE.setUserUniqueID(userId);
  ReactMoE.setUserContactNumber(phoneNumber);
};

const onLogout = () => {
  ReactMoE.logout();
};

const Moengage = {
  init,
  onLogin,
  onLogout,
};

export default Moengage;
