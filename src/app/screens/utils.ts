import {useEffect, useState} from 'react';
import {Keyboard} from 'react-native';

export const useKeyboardVisible = () => {
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardWillShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardWillHide',
      () => setKeyboardVisible(false),
    );
    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  return isKeyboardVisible;
};

export function createDynamicUrl(uri: string, color: string) {
  if (!uri?.length) {
    return;
  }
  if (uri.indexOf('/upload/') < 0) {
    return uri;
  }
  const arr = uri?.split('/upload/');
  const newuri = arr.join(
    `/upload/e_grayscale/e_gamma:-50/e_tint:50:${color}/e_sharpen:200/e_ordered_dither:7/`,
  );
  return newuri;
}

export function createGreyscaleUrl(uri: string) {
  if (!uri?.length) {
    return;
  }
  if (uri.indexOf('/upload/') < 0) {
    return uri;
  }
  const arr = uri?.split('/upload/');
  const newuri = arr.join('/upload/e_grayscale/');
  return newuri;
}
