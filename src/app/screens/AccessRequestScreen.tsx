import React from 'react';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {ReferMemberForm} from '@/app/screens/home/<USER>/ReferMemberForm';

export function AccessRequestScreen() {
  return (
    <ScreenContainer name={ScreenNames.AccessRequest}>
      <ReferMemberForm
        modalDisclosure={{
          opened: true,
          controls: {
            open: () => {},
            close: () => NavigationService.goBack(),
            toggle: () => {},
          },
        }}
        showUploadResume={true}
        onboarding
      />
    </ScreenContainer>
  );
}
