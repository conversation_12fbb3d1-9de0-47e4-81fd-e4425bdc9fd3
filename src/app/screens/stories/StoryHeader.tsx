/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {ProfileHeader} from 'react-native-story-view';

export const StoryHeader = ({
  userStories,
  multiStoryRef,
  progressIndex,
  onClose,
  story,
  ...props
}: any) => {
  return (
    <ProfileHeader
      userImage={{uri: userStories?.profile ?? ''}}
      userName={userStories?.username}
      userMessage={story?.[progressIndex]?.storyTitle}
      rootStyle={{marginBottom: 0}}
      containerStyle={{marginBottom: 0}}
      onClosePress={() => {
        onClose();
      }}
      {...props}
    />
  );
};
