import {useNavigation, useRoute} from '@react-navigation/native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootState} from '@store/store';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import React from 'react';
import {ActivityIndicator, View} from 'react-native';
import {
  MultiStoryContainer,
  MultiStoryRef,
  StoriesType,
} from 'react-native-story-view';
import {useSelector} from 'react-redux';

import {findFirstUnseen, getStoryTypeFromStory} from './utils';

import {client} from '@/app/apollo/client';
import useUixMutation from '@/app/hooks/useUixMutation';
import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {RootStackParamsList} from '@/app/navigation/types';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {StoryHeader} from '@/app/screens/stories/StoryHeader';
import {Colors} from '@/app/theme/colors';
import Dimension from '@/app/utils/dimension';
import {HomeStoriesQuery, markSeenMutation} from '@/graphQL/home';
import {
  HomeStoriesQueryQuery,
  MarkSeenMutation,
  MarkSeenMutationVariables,
  Stories_Story_Groups,
} from '@/types/__generated__/graphql';

type NavigationProps = NativeStackScreenProps<
  RootStackParamsList,
  ScreenNames.Stories
>;

export default function StoriesScreen() {
  const route = useRoute<NavigationProps['route']>();
  const navigation = useNavigation();
  const multiStoryRef = useRef<MultiStoryRef>(null);
  const prevProgressRef = useRef<number>(-1);
  const prevStoryRef = useRef<number>(-1);

  const [visibility, setVisibility] = useState(true);
  const [userStories, setUserStories] = useState<StoriesType[]>([]);

  const userDetails = useSelector((store: RootState) => store.user);

  const {data, loading, refetch} = useUixQuery<HomeStoriesQueryQuery>(
    HomeStoriesQuery,
    {
      variables: {
        member_id: userDetails.id,
      },
    },
  );

  const [mutation] = useUixMutation<
    MarkSeenMutation,
    MarkSeenMutationVariables
  >(markSeenMutation);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      client.refetchQueries({include: [HomeStoriesQuery]});
    });

    return unsubscribe;
  }, [navigation]);
  
  useEffect(() => {
    if (!data) return;
    setUserStories(
      data.stories_story_groups.map(storyGroup =>
        getStoryTypeFromStory(storyGroup as Stories_Story_Groups),
      ),
    );
  }, [data]);

  const [userStoryIndex, progressIndex] = useMemo(
    () => findFirstUnseen(userStories, route.params?.id),
    [userStories, route.params?.id],
  );

  const markSeen = useCallback(
    async (progressIndex: number, storyIndex: number) => {
      const story = userStories?.[storyIndex]?.stories?.[progressIndex];
      if (!story) return;

      await mutation({
        variables: {
          member_id: userDetails.id,
          story_id: story.id.toString(),
        },
        onCompleted: () => {
          setUserStories(prev => {
            const updated = [...prev];
            const currentStory =
              updated?.[storyIndex]?.stories?.[progressIndex];
            if (currentStory) {
              currentStory.isSeen = true;
            }
            return updated;
          });
        },
      });
    },
    [userStories, mutation, userDetails.id],
  );

  const isLoading = useMemo(
    () => loading || !userDetails || !data || userStories.length == 0,
    [loading, userDetails, data, userStories],
  );

  const handlePositionChange = useCallback(
    (progressIndex: number, storyIndex: number | undefined) => {
      if (
        prevProgressRef.current === progressIndex &&
        prevStoryRef.current === storyIndex
      ) {
        return;
      }

      prevProgressRef.current = progressIndex;
      prevStoryRef.current = storyIndex ?? -1;

      if (
        storyIndex !== undefined &&
        !(userStories?.[storyIndex]?.stories?.[progressIndex]?.isSeen ?? true)
      ) {
        markSeen(progressIndex, storyIndex);
      }
    },
    [userStories, markSeen],
  );

  return (
    <ScreenContainer name={ScreenNames.Stories}>
      {isLoading ? (
        <View
          style={{alignContent: 'center', justifyContent: 'center', flex: 1}}>
          <ActivityIndicator />
        </View>
      ) : (
        <MultiStoryContainer
          visible={visibility}
          stories={userStories}
          viewedStories={userStories.map(userStory =>
            userStory.stories.map(story => story.isSeen ?? false),
          )}
          progressIndex={progressIndex}
          userStoryIndex={userStoryIndex}
          onChangePosition={handlePositionChange}
          renderHeaderComponent={({userStories, progressIndex, story}) => (
            <View
              style={{position: 'absolute', top: 0, left: 0, width: '100%'}}>
              <StoryHeader
                {...{userStories, progressIndex, multiStoryRef, story}}
                onClose={() => {
                  setVisibility(false);
                  refetch();
                  navigation.goBack();
                }}
              />
            </View>
          )}
          imageStyle={{
            resizeMode: 'contain',
            width: Dimension.SCREEN_WIDTH,
            height: '100%',
          }}
          renderFooterComponent={() => <></>}
          barStyle={{
            barActiveColor: Colors.Teal,
            barInActiveColor: Colors.Teal950,
          }}
        />
      )}
    </ScreenContainer>
  );
}
