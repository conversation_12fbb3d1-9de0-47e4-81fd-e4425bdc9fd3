import {StoriesType} from 'react-native-story-view';

import ImageHelper from '@/app/ui/uixImage/helper';
import Dimension from '@/app/utils/dimension';
import {Stories_Story_Groups} from '@/types/__generated__/graphql';

export function findFirstUnseen(
  userStories: StoriesType[],
  id?: string,
): [number, number] {
  if (id) {
    for (let groupIndex = 0; groupIndex < userStories?.length; groupIndex++) {
      const group = userStories[groupIndex];
      for (
        let storyIndex = 0;
        storyIndex < group.stories?.length;
        storyIndex++
      ) {
        if (group.stories[storyIndex].id.toString() === id) {
          return [groupIndex, storyIndex];
        }
      }
    }
  }

  const viewedStories = userStories.map(userStory =>
    userStory.stories.map(story => story.isSeen ?? false),
  );

  for (let i = 0; i < viewedStories.length; i++) {
    for (let j = 0; j < viewedStories[i].length; j++) {
      if (!viewedStories[i][j]) {
        return [i, j];
      }
    }
  }

  let latestDate = '2000-01-01T00:00:00.000000+00:00';
  let latestIndices: [number, number] = [0, 0];

  for (let groupIndex = 0; groupIndex < userStories?.length; groupIndex++) {
    const group = userStories[groupIndex];
    for (let storyIndex = 0; storyIndex < group.stories?.length; storyIndex++) {
      const storyDate = group.stories[storyIndex].created;
      if (storyDate && storyDate > latestDate) {
        latestDate = storyDate;
        latestIndices = [groupIndex, storyIndex];
      }
    }
  }

  return latestIndices;
}

export function getStoryTypeFromStory(storyGroup: Stories_Story_Groups) {
  return {
    id: storyGroup.id,
    username: storyGroup.title ?? '',
    profile:
      'https://res.cloudinary.com/dkxocdrky/image/upload/v1726750031/playbook/nzzig9ccil38zberz9ga.png',
    stories: storyGroup.story_details.map(storyDetail => {
      const isVideo = storyDetail?.media_type?.toLocaleLowerCase() === 'video';

      const url = isVideo
        ? storyDetail.cover_media!
        : ImageHelper.addTransformationToURL(
            storyDetail.cover_media ?? '',
            2 * Dimension.SCREEN_WIDTH,
          );
          
      return {
        url: url,
        id: storyDetail.id,
        type: storyDetail?.media_type?.toLocaleLowerCase(),
        storyTitle : storyDetail.title,
        duration: storyDetail.duration ?? 5,
        isReadMore: storyDetail.isReadMore ?? false,
        storyId: storyDetail.story_group_id,
        isSeen: Boolean(storyDetail.seen_by_aggregate.aggregate?.count ?? 0),
        showOverlay: storyDetail.showOverlay ?? false,
        link: storyDetail.link ?? '',
        created: storyDetail.created_at,
      };
    }),
  };
}
