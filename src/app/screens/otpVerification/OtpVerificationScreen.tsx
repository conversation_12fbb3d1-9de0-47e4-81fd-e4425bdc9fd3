import {useMutation} from '@apollo/client';
import {useRoute} from '@react-navigation/native';
import {CommonActions} from '@store/slices/CommonSlice';
import {UserActions} from '@store/slices/UserSlice';
import React, {useEffect, useState} from 'react';
import {Pressable, View} from 'react-native';
import {OtpInput} from 'react-native-otp-entry';
import {useDispatch} from 'react-redux';

import {OtpVerificationLoader} from './OtpVerificationLoader';

import {Tracker} from '@/app/analytics/tracker';
import useUixMutation from '@/app/hooks/useUixMutation';
import Moengage from '@/app/moengage';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import EncryptedStore from '@/app/utils/encryptedStore';
import {GenerateOTP, VerifyOTP} from '@/graphQL/otp';
import {RegisterUser} from '@/graphQL/register';
import {
  GenerateOtpMutation,
  GenerateOtpMutationVariables,
  LoginOutput,
  VerifyOtpResponse,
} from '@/types/__generated__/graphql';

export default function OtpVerificationScreen() {
  const [error, setError] = useState('');
  const dispatch = useDispatch();
  const [timerEnd, setTimerEnd] = useState(false);
  const [mutationLoading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60); // 60 seconds countdown

  useEffect(() => {
    if (timeLeft === 0) {
      setTimerEnd(true);
      return;
    }
    const interval = setInterval(() => {
      setTimeLeft(prevTime => prevTime - 1);
    }, 1000);
    return () => clearInterval(interval);
  }, [timeLeft]);

  const {phone} = (useRoute().params ?? {phone: ''}) as {
    phone: string;
    userId: string;
  };

  const [verifyOTP] = useMutation<
    {verifyOTP: VerifyOtpResponse},
    {phone_number: string; otp: number; country_code: string}
  >(VerifyOTP);

  const [registerUser] = useMutation<
    {login: LoginOutput},
    {user_id: string; profile: string}
  >(RegisterUser);

  const handleNavigate = async (otp: string) => {
    setLoading(true);
    setError('');
    const data = await verifyOTP({
      variables: {
        otp: parseInt(otp, 10),
        phone_number: phone,
        country_code: '+91',
      },
    });
    if (
      data.data?.verifyOTP?.data?.user_id &&
      data.data?.verifyOTP?.data?.member_details
    ) {
      const userId = data.data?.verifyOTP?.data?.user_id;
      const memberDetails = data.data?.verifyOTP?.data?.member_details;

      Tracker.identify(userId);
      Tracker.getPeople().set({
        $phone: phone,
        $distinct_id: userId,
        $name: memberDetails.alias,
      });

      dispatch(
        UserActions.updateUserDetails({
          ...memberDetails,
          id: userId,
        }),
      );

      Moengage.onLogin(phone, userId);

      const response = await registerUser({
        variables: {
          profile: '',
          user_id: data.data?.verifyOTP?.data?.user_id,
        },
      });
      const {token} = response?.data?.login?.data ?? {};
      if (!token) {
        setLoading(false);
        return;
      }
      EncryptedStore.setValue('token', token);
      dispatch(CommonActions.updateIsLogin(true));
      setLoading(false);
      NavigationService.replace(ScreenNames.App, {
        screen: ScreenNames.Home,
      });
    } else {
      setLoading(false);
      setError('Oops, that’s not the correct OTP.');
    }
  };

  const [mutation] = useUixMutation<
    GenerateOtpMutation,
    GenerateOtpMutationVariables
  >(GenerateOTP);

  return (
    <ScreenContainer
      name={ScreenNames.OtpVerification}
      background="grid"
      style={{padding: 16}}>
      {mutationLoading ? (
        <OtpVerificationLoader />
      ) : (
        <View style={{gap: 16}}>
          <UixButton
            label="Back"
            iconImage={require('@assets/backArrow.png')}
            onPress={() => {
              NavigationService.goBack();
            }}
            viewStyle={{marginBottom: 38}}
          />
          <UixText
            variant="dotsBold"
            capitalize
            style={{fontSize: 32, lineHeight: 32}}>
            {'Enter\n4-digit Code'}
          </UixText>
          <UixText variant="titleMedium" style={{color: Colors.White700}}>
            {`A 4 digit code has been sent to ${phone}`}
          </UixText>
          <OtpInput
            numberOfDigits={4}
            focusColor={Colors.White950}
            onFilled={handleNavigate}
            autoFocus={true}
            textInputProps={{
              accessibilityLabel: 'One-Time Password',
              keyboardAppearance: 'dark',
            }}
            theme={{
              containerStyle: {justifyContent: 'flex-start'},
              pinCodeContainerStyle: {
                backgroundColor: Colors.White950,
                borderColor: 'transparent',
                height: 56,
                width: 52,
                borderRadius: 8,
                marginRight: 8,
              },
              pinCodeTextStyle: {
                fontSize: 36,
                lineHeight: 30,
                color: 'white',
                fontFamily: 'BPdotsUnicaseSquare-Bold',
                marginLeft: 6,
              },
              focusedPinCodeContainerStyle: {borderColor: 'transparent'},
            }}
          />
          <View>
            {error && (
              <UixText
                variant="titleMedium"
                style={{
                  color: '#E55C5C',
                  fontWeight: '600',
                }}>
                {error}
              </UixText>
            )}
            {timerEnd ? (
              <Pressable
                onPress={() => {
                  mutation({
                    variables: {phone: `${phone}`, country_code: '+91'},
                  });
                  setTimeLeft(60); // Reset timer
                  setTimerEnd(false);
                }}>
                <UixText
                  variant="titleMedium"
                  style={{textDecorationLine: 'underline', color: Colors.Teal}}>
                  Resend OTP
                </UixText>
              </Pressable>
            ) : (
              <UixText
                style={{
                  color: Colors.White700,
                  fontSize: 18,
                  fontWeight: '600',
                }}>
                {`Resend code in ${timeLeft}s`}
              </UixText>
            )}
          </View>
        </View>
      )}
    </ScreenContainer>
  );
}
