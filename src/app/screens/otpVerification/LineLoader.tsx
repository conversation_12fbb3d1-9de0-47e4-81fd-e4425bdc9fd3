import {useEffect, useRef} from 'react';
import {Animated, View} from 'react-native';

import {LogoLoader} from './LogoLoader';

import {Colors} from '@/app/theme/colors';
import Dimension from '@/app/utils/dimension';

export function LineLoader() {
  const x = useRef(new Animated.Value(-Dimension.SCREEN_WIDTH)).current;
  const color = x.interpolate({
    inputRange: [-Dimension.SCREEN_WIDTH, 0],
    outputRange: ['rgb(31, 31, 31)', 'rgb(173, 255, 0)'],
  });
  useEffect(() => {
    Animated.timing(x, {
      toValue: 0,
      duration: 3000,
      useNativeDriver: true,
    }).start();
  }, []);
  return (
    <View
      style={{
        position: 'relative',
        flexDirection: 'column',
        alignItems: 'center',
      }}>
      <View
        style={{
          backgroundColor: 'black',
          paddingHorizontal: 18,
          paddingVertical: 8,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: -20,
          zIndex: 1,
        }}>
        <LogoLoader color={color} />
      </View>
      <View
        style={{
          width: '100%',
          height: 2,
          backgroundColor: '#333333',
          position: 'relative',
          overflow: 'hidden',
        }}>
        <Animated.View
          style={{
            width: '100%',
            height: 2,
            backgroundColor: Colors.Yellow750,
            transform: [{translateX: x}],
          }}
        />
      </View>
    </View>
  );
}
