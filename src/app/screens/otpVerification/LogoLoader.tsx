import {Animated} from 'react-native';
import Svg, {Path} from 'react-native-svg';

const AnimatedPath = Animated.createAnimatedComponent(Path);
export function LogoLoader({
  color,
}: {
  color: Animated.AnimatedInterpolation<string | number>;
}) {
  return (
    <Svg width="60" height="24" viewBox="0 0 60 24" fill="none">
      <AnimatedPath
        d="M19.2001 2.0282C19.2001 1.17988 18.5124 0.492188 17.6641 0.492188H1.53601C0.687696 0.492188 0 1.17988 0 2.0282V13.9323C0 19.2343 4.2981 23.5323 9.60007 23.5323C14.902 23.5323 19.2001 19.2343 19.2001 13.9323V2.0282Z"
        fill={color}
      />
      <AnimatedPath
        d="M23.0396 2.0282C23.0396 1.17988 23.7272 0.492188 24.5756 0.492188H31.1036C31.9519 0.492188 32.6396 1.17988 32.6396 2.0282V18.7323C32.6396 21.3833 30.4906 23.5323 27.8396 23.5323C25.1886 23.5323 23.0396 21.3833 23.0396 18.7323V2.0282Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M57.5197 1.6662L57.0383 1.19102C55.4294 -0.397004 52.8209 -0.397007 51.212 1.19102L50.7305 1.6662H57.5197ZM44.9552 1.6662H38.7979L38.9633 1.50287C40.5722 -0.085149 43.1808 -0.0851474 44.7897 1.50287L44.9552 1.6662Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M58.1968 2.33203H50.0554L47.8438 4.51494L45.6322 2.33203H38.1227L37.2084 3.23453C36.6932 3.74302 36.343 4.35268 36.1577 4.99872H59.9219C59.7682 4.23802 59.3926 3.51233 58.7952 2.92267L58.1968 2.33203Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M59.9984 5.66602H36.0251C35.9232 6.58456 36.1377 7.53082 36.6686 8.3327H59.104C59.7314 7.55697 60.0296 6.60718 59.9984 5.66602Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M58.465 9H37.2212L39.9229 11.6667H55.7633L58.465 9Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M55.7632 12.334H39.9225L37.2207 15.0007H58.465L55.7632 12.334Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M59.1025 15.666H36.6692C36.138 16.4678 35.9233 17.4141 36.025 18.3327H59.9978C60.0287 17.3915 59.7303 16.4417 59.1025 15.666Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M59.9205 18.998H36.1572C36.3425 19.6439 36.6927 20.2534 37.2077 20.7617L38.1226 21.6647H45.631L47.8428 19.4817L50.0546 21.6647H58.1949L58.7938 21.0736C59.3911 20.4841 59.7666 19.7586 59.9205 18.998Z"
        fill={color}
      />
      <AnimatedPath
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M57.5205 22.332H50.7293L51.2117 22.8082C52.8206 24.3962 55.4292 24.3962 57.0381 22.8082L57.5205 22.332ZM44.9566 22.332H38.7974L38.9638 22.4963C40.5727 24.0843 43.1813 24.0843 44.7902 22.4963L44.9566 22.332Z"
        fill={color}
      />
    </Svg>
  );
}
