import {Image, View} from 'react-native';

import {LineLoader} from './LineLoader';

export function OtpVerificationLoader() {
  return (
    <View
      style={{
        height: '100%',
        width: '100%',
        flex: 1,
      }}>
      <View
        style={{
          height: '100%',
          width: '100%',
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Image
          source={require('@assets/roboLoader.png')}
          style={{
            width: 160,
            height: 160,
            resizeMode: 'contain',
          }}
        />
      </View>
      <LineLoader />
      <View style={{height: 40}} />
    </View>
  );
}
