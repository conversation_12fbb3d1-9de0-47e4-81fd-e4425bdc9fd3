import React, {useCallback, useState} from 'react';
import {
  Keyboard,
  NativeSyntheticEvent,
  TextInputChangeEventData,
  View,
} from 'react-native';
import {TextInput} from 'react-native-paper';

import {RoundedButton} from '../ui/RoundedButton';

import useUixMutation from '@/app/hooks/useUixMutation';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {UixTextInput} from '@/app/ui/UixTextInput';
import EncryptedStore from '@/app/utils/encryptedStore';
import {GenerateOTP} from '@/graphQL/otp';
import {
  GenerateOtpMutation,
  GenerateOtpMutationVariables,
} from '@/types/__generated__/graphql';

export default function EnterMobileScreen() {
  const [valid, setValid] = useState(false);
  const [validNumber, setValidNumber] = useState<string>('');
  const [mutation, {loading}] = useUixMutation<
    GenerateOtpMutation,
    GenerateOtpMutationVariables
  >(GenerateOTP);

  const handleNavigate = async () => {
    if (loading) {
      return;
    }
    if (!Number(validNumber)) {
      return;
    }
    Keyboard.dismiss();
    const otpResponse = await mutation({
      variables: {phone: `${validNumber}`, country_code: '+91'},
    });

    if (otpResponse.data?.generateOTP?.message === 'Member not found') {
      EncryptedStore.setValue(
        'token',
        otpResponse.data?.generateOTP.token ?? '',
      );
      NavigationService.replace(ScreenNames.AccessRequest);
    } else {
      NavigationService.navigate(ScreenNames.OtpVerification, {
        phone: validNumber,
      });
    }
  };

  const handleTextChange = useCallback(
    ({nativeEvent: {text}}: NativeSyntheticEvent<TextInputChangeEventData>) => {
      if (Number(text) && text.length === 10) setValid(true);
      setValidNumber(text);
    },
    [],
  );

  return (
    <ScreenContainer
      name={ScreenNames.EnterMobile}
      background="grid"
      style={{padding: 16}}
      scrollDisabled
      enableKeyboardScrollView>
      <UixButton
        label="Back"
        iconImage={require('@assets/backArrow.png')}
        onPress={() => {
          NavigationService.goBack();
        }}
        viewStyle={{marginBottom: 38}}
      />
      <View style={{flex: 1, gap: 16}}>
        <UixText
          variant="dotsBold"
          capitalize
          style={{fontSize: 32, lineHeight: 32}}>
          {'Enter\nMobile Number'}
        </UixText>
        <UixText variant="titleMedium" style={{color: Colors.White700}}>
          We will send you a confirmation code there
        </UixText>
        <UixTextInput
          mode="outlined"
          keyboardType="numeric"
          inputMode="numeric"
          keyboardAppearance="dark"
          submitBehavior="submit"
          maxLength={10}
          value={validNumber}
          left={
            <TextInput.Icon
              icon={require('@assets/phone.png')}
              color={Colors.White}
            />
          }
          style={{
            fontSize: 20,
            fontWeight: '600',
            lineHeight: 24,
          }}
          textContentType="telephoneNumber"
          placeholder="XXX-XXX-XXXX"
          autoFocus
          onChange={handleTextChange}
        />
        {((valid && validNumber.length < 10) || !Number(validNumber)) &&
          validNumber && (
            <UixText
              variant="titleMedium"
              style={{
                color: '#E55C5C',
                fontWeight: '600',
              }}>
              {'Oops, that’s not the correct number.'}
            </UixText>
          )}
      </View>
      <RoundedButton
        loading={loading}
        label="Verify with OTP"
        onPress={handleNavigate}
        icon={require('@assets/arrow_right_dotted.png')}
        disabled={!(validNumber.length === 10 && Number(validNumber))}
      />
    </ScreenContainer>
  );
}
