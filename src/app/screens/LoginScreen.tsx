import React from 'react';
import {Image, View} from 'react-native';

import {RoundedButton} from '../ui/RoundedButton';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import GradientText from '@/app/ui/GradientText';
import {UixText} from '@/app/ui/UixText';

export default function LoginScreen() {
  const handleNavigate = async () => {
    NavigationService.navigate(ScreenNames.EnterMobile);
  };

  return (
    <ScreenContainer name={ScreenNames.Login} disableBackground>
      <Image
        source={require('@assets/logoNew.png')}
        style={{
          margin: 16,
          width: 72,
          height: 40,
        }}
      />
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          justifyContent: 'flex-end',
        }}>
        <Image
          style={{
            width: '90%',
            height: '90%',
            resizeMode: 'contain',
            marginRight: '-24%',
          }}
          source={require('@assets/cube.png')}
        />
      </View>
      <View style={{padding: 16, gap: 8}}>
        <UixText
          variant="dots"
          style={{fontSize: 24, lineHeight: 24}}
          capitalize>
          Welcome to
        </UixText>
        <GradientText
          text={'UIX CLUB'}
          style={{fontSize: 56, fontWeight: 600, marginBottom: 8}}
        />
        <UixText
          variant="dots"
          style={{fontSize: 20, lineHeight: 20, marginBottom: 16}}
          capitalize>
          {'Unlock your earning\npotential with UIX club'}
        </UixText>
        <View style={{height: 16}} />
        <RoundedButton
          onPress={handleNavigate}
          label="get started"
          icon={require('@assets/arrow_right_dotted.png')}
        />
      </View>
    </ScreenContainer>
  );
}
