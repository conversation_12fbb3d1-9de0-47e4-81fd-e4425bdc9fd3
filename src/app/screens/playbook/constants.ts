export const playbookStories = [
  {
    id: 1,
    profile:
      'https://res.cloudinary.com/dkxocdrky/image/upload/v1726750031/playbook/nzzig9ccil38zberz9ga.png',
    username: 'The Playbook',
    stories: [
      {
        id: 1,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748977/playbook/xcla5oa8zd9g88rfp2uo.png',
        chapter: 'Welcome',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: true,
        showOverlay: true,
        link: 'https://google.com',
      },
      {
        id: 2,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748977/playbook/utrhfya8jlatjhxvthp1.png',
        chapter: 'Our Vision',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: true,
      },
      {
        id: 3,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748977/playbook/okq160ht74fqlu1ufxab.png',
        chapter: 'Our Vision',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 4,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748979/playbook/lj2rmpxjaok4p5lqpyn8.png',
        chapter: 'What we do',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 5,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748977/playbook/gk16fk43ixifumxoomdv.png',
        chapter: 'What we do',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 6,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748978/playbook/oetcvkkdvqbde2l7d9rf.png',
        chapter: 'What drives us',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 7,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748977/playbook/wpsjrztwnivu85yagqrh.png',
        chapter: 'What drives us',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 8,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748978/playbook/bqkznygaimzdloku8kpr.png',
        chapter: 'What drives us',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 9,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748981/playbook/crlffjjcxxfbyzc47l5v.png',
        chapter: 'What drives us',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 10,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748979/playbook/znrwzhjtdvcuzaai4zje.png',
        chapter: 'What drives us',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 11,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748979/playbook/uppu2hyiu3hwmvhgqeij.png',
        chapter: 'What drives us',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 12,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748979/playbook/eridme40srs9qinuckek.png',
        chapter: 'Roles',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 13,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748979/playbook/u4qpjiaahrxcklj5rxfe.png',
        chapter: 'Roles',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 14,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748982/playbook/fs20f043b1mjpylhmhx5.png',
        chapter: 'Roles',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 15,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748984/playbook/n5wjmi7ftdnbfv4qdlxy.png',
        chapter: 'Aliases',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 16,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748980/playbook/dyx1lk70c9qjodk3khih.png',
        chapter: 'Avatars',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 17,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748981/playbook/cqekphmkvrmmrsthzov0.png',
        chapter: 'Badges',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 18,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748984/playbook/lue1yecpev4wc690tbis.png',
        chapter: 'Badges',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 19,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748983/playbook/ea9ak2ee5ip4i0ybpxj6.png',
        chapter: 'Revenue Distribution',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 20,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748982/playbook/cvof7lcthmedtq3ogh7x.png',
        chapter: 'Revenue Distribution',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 21,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748985/playbook/ecg5ftwcm5z8lynsvdov.png',
        chapter: 'Revenue Distribution',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 22,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748983/playbook/hh2i9usjqagsrx6dzddo.png',
        chapter: 'Milestones',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 23,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748984/playbook/snzyl67xe3kcfaarsenm.png',
        chapter: 'Milestones',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 24,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748984/playbook/v8mang9lobkp2s39j9jz.png',
        chapter: 'Community Stats',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 25,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748984/playbook/jbox6yxgyvyhe5jqhbks.png',
        chapter: 'Community Stats',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 26,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748986/playbook/w4dxrvqfyhtbbmzxs0u4.png',
        chapter: 'Milestones',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 27,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748987/playbook/rvy0hc7f9kxjzmajpqf5.png',
        chapter: 'Milestones',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
      {
        id: 28,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1726748992/playbook/krnhlvhoabjlyffi4idk.png',
        chapter: 'Milestones',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 1,
        isSeen: false,
      },
    ],
  },
];

export const homeScreenStories = [
  {
    id: 1,
    username: 'Projects',
    title: 'New Projects',
    profile:
      'https://res.cloudinary.com/dkxocdrky/image/upload/v1724710192/rkx1dmzl1do8xvp1cl7d.png',
    stories: [
      {
        id: 1,
        url: 'https://res.cloudinary.com/dkxocdrky/video/upload/v1726573089/jgu7gc6iysdp0ucgfs04.mp4',
        type: 'video',
        isReadMore: true,
        storyId: 1,
        isSeen: true,
        showOverlay: true,
        link: 'https:google.com',
      },
    ],
  },
  {
    id: 2,
    username: 'Yuta',
    title: '',
    profile:
      'https://res.cloudinary.com/dkxocdrky/image/upload/v1724685398/pt7usktfvdfkjfrzxrud.jpg',
    stories: [
      {
        id: 1,
        url: 'https://res.cloudinary.com/dkxocdrky/video/upload/v1726573088/rynrlwuxulttl0xggwft.mp4',
        type: 'video',
        isReadMore: true,
        storyId: 2,
        isSeen: true,
        showOverlay: true,
        link: 'https:google.com',
      },
    ],
  },
  {
    id: 3,
    username: 'Lazyowl',
    title: '',
    profile:
      'https://res.cloudinary.com/dkxocdrky/image/upload/v1724684926/m8mwghufbki9nnnfcgw1.jpg',
    stories: [
      {
        id: 1,
        url: 'https://res.cloudinary.com/dkxocdrky/image/upload/v1721751978/1_dgeibh.png',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 2,
        isSeen: true,
        showOverlay: true,
        link: 'https:google.com',
      },
    ],
  },
  {
    id: 4,
    username: 'Updates',
    title: '',
    profile:
      'https://res.cloudinary.com/dkxocdrky/image/upload/v1724710192/rkx1dmzl1do8xvp1cl7d.png',
    stories: [
      {
        id: 1,
        url: 'https://res.cloudinary.com/dkxocdrky/video/upload/v1726573107/qd5gw6wxyrjiacpkefcd.mp4',
        type: 'image',
        duration: 5,
        isReadMore: true,
        storyId: 4,
        isSeen: true,
        showOverlay: true,
        link: 'https:google.com',
      },
    ],
  },
];
