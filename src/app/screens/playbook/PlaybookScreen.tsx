import {useNavigation} from '@react-navigation/native';
import {useState} from 'react';
import {StatusBar, View} from 'react-native';
import {MultiStoryContainer} from 'react-native-story-view';

import {StoryHeader} from '../stories/StoryHeader';
import {playbookStories} from './constants';

import {ScreenNames} from '@/app/navigation/constants';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {updateColorWithOpacity} from '@/app/theme/utils';
import Dimension from '@/app/utils/dimension';

export const PlaybookScreen = function () {
  const [visibility, setVisibility] = useState(true);
  const navigation = useNavigation();

  return (
    <ScreenContainer name={ScreenNames.PlayBook}>
      <MultiStoryContainer
        viewedStories={[[false]]}
        visible={visibility}
        stories={playbookStories}
        renderHeaderComponent={({userStories, progressIndex, story}) => (
          <View style={{position: 'absolute', top: 0, left: 0, width: '100%'}}>
            <StoryHeader
              {...{userStories, progressIndex, story}}
              onClose={() => {
                setVisibility(false);
                navigation.goBack();
              }}
            />
          </View>
        )}
        imageStyle={{
          resizeMode: 'contain',
          width: Dimension.SCREEN_WIDTH - 16,
          padding: 0,
          marginHorizontal: 8,
          marginTop: -64,
          height: Dimension.SCREEN_HEIGHT - 32,
        }}
        customViewStyle={{height: 30}}
        renderFooterComponent={() => <></>}
        barStyle={{
          barActiveColor: '#00FFD1',
          barInActiveColor: updateColorWithOpacity('#00FFD1', 0.2),
        }}
        onComplete={() => {
          setVisibility(false);
          StatusBar.setHidden(false);
          navigation.goBack();
        }}
      />
    </ScreenContainer>
  );
};
