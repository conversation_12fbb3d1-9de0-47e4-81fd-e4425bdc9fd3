import {useRoute} from '@react-navigation/native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import React from 'react';
import {View} from 'react-native';

import {MemberInfoCard} from './components/MemberInfoCard';

import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {RootStackParamsList} from '@/app/navigation/types';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {MemberContextProvider} from '@/contexts/MemberContext';
import {MemberDetailQuery} from '@/graphQL/member';
import {
  MemberDetailsQuery,
  Members_Member_Details,
} from '@/types/__generated__/graphql';

type NavigationProps = NativeStackScreenProps<
  RootStackParamsList,
  ScreenNames.MemberDetail
>;

export default function MemberDetailsScreen() {
  const route: NavigationProps['route'] = useRoute();
  const initialMember = route?.params?.member;

  const {data, loading, refetch} = useUixQuery<MemberDetailsQuery>(
    MemberDetailQuery,
    {
      variables: {
        id: route?.params.id,
      },
    },
  );

  const getMember = () => {
    return !loading && data
      ? (data.members_member_details_by_pk as Members_Member_Details)
      : initialMember;
  };

  const getEarnings = () =>
    !loading && data
      ? ((data?.GetRevenueGraphDataForApp.data as {
          hours: number;
          label: string;
          revenue: number;
          slot: number;
        }[]) ?? [])
      : [];

  return (
    <ScreenContainer
      name={ScreenNames.MemberDetail}
      style={{backgroundColor: Colors.DarkBackground, padding: 16}}
      disableBackground
      enableKeyboardScrollView>
      <MemberContextProvider memberId={route?.params.id}>
        <View style={{gap: 10}}>
          <UixButton
            label="Back"
            iconImage={require('@assets/backArrow.png')}
            onPress={() => {
              NavigationService.goBack();
            }}
            tintColor={'Teal'}
            viewStyle={{marginBottom: 24}}
          />
          <MemberInfoCard
            member={getMember()}
            onRefetch={refetch}
            earnings={getEarnings()}
            isLoading={loading}
            isRedirectingFromHome={!!route?.params.id}
          />
        </View>
      </MemberContextProvider>
    </ScreenContainer>
  );
}
