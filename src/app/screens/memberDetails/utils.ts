export const formatDate = (date: Date) => {
  const day = date.getDate();
  const suffix = getDaySuffix(day);

  return `${day}${suffix} ${date.toLocaleString('en-US', {month: 'short'})} ${date.getFullYear()}`;
};

const getDaySuffix = (day: number) => {
  if (day > 3 && day < 21) return 'th'; // 4th to 20th
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
};
