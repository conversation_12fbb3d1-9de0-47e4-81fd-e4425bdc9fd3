import {Pressable, View} from 'react-native';

import {CalendarIcon, HourGlassIcon} from '../../engagement/icons';
import MemberSectionEmptyState from './MemberSectionEmptyState';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Retainer} from '@/types/__generated__/graphql';

export function MemberEngagementSection({
  active,
  retainers,
  isCurrentUser,
}: {
  active: boolean;
  retainers: Members_Member_Retainer[];
  isCurrentUser: boolean;
}) {
  return (
    <View style={{flexDirection: 'column', gap: 16}}>
      <UixText
        variant="dotsBold"
        style={{
          fontSize: 20,
          lineHeight: 16,
          color: active ? Colors.Teal : Colors.White300,
        }}
        capitalize>
        {`Engagement`}
      </UixText>
      {retainers.length == 0 ? (
        <MemberSectionEmptyState
          title={`No engagement type\nadded yet`}
          subtitle={`Set how you’d like to work\nhourly or monthly.`}
          description={`This helps us match you with\nthe right projects.`}
          screenName={ScreenNames.Engagement}
        />
      ) : (
        <Pressable
          onPress={() =>
            isCurrentUser && NavigationService.navigate(ScreenNames.Engagement)
          }
          style={{
            gap: 12,
            borderRadius: 8,
            backgroundColor: Colors.Teal950,
            padding: 16,
          }}>
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 20,
              lineHeight: 16,
              color: active ? Colors.Teal : Colors.White300,
            }}
            capitalize>
            {retainers[0].member_retainer_type.name}
          </UixText>
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <View style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
              <HourGlassIcon />
              <UixText variant="titleMedium" style={{color: Colors.White}}>
                {`${retainers[0].hours} hours / day`}
              </UixText>
            </View>
            {retainers[0].days && (
              <View
                style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
                <CalendarIcon />
                <UixText variant="titleMedium" style={{color: Colors.White}}>
                  {`${retainers[0].days.length} days / week`}
                </UixText>
              </View>
            )}
          </View>
        </Pressable>
      )}
    </View>
  );
}
