import HourIcon from '@assets/hour.svg';
import RupeeIcon from '@assets/rupee.svg';
import React, {useMemo, useState} from 'react';
import {View} from 'react-native';
import {LineChart} from 'react-native-gifted-charts';

import {Colors} from '@/app/theme/colors';
import {ToggleSwitch} from '@/app/ui/ToggleSwitch';
import {UixText} from '@/app/ui/UixText';
import Dimension from '@/app/utils/dimension';

export function MemberEarningsSection({
  earnings,
  showSwitch = false,
}: {
  earnings: Array<{
    hours: number;
    label: string;
    revenue: number;
    slot: number;
  }>;
  showSwitch?: boolean;
}) {
  const [isEarnings, setIsEarnings] = useState(false);
  const onToggleSwitch = () => setIsEarnings(!isEarnings);

  const {totalHours, totalEarnings, data, xAxisLabels, maxValue} =
    useMemo(() => {
      const totalHours = earnings.reduce(
        (sum: number, entry: {hours: number; revenue: number}) =>
          sum + (entry.hours || 0),
        0,
      );
      const totalEarnings = Math.round(
        earnings.reduce(
          (sum: number, entry: {hours: number; revenue: number}) =>
            sum + (entry.revenue || 0),
          0,
        ),
      );

      const data = isEarnings
        ? [
            {value: earnings[0].revenue / 2},
            ...earnings.map(earning => ({value: earning.revenue})),
            {value: earnings[earnings.length - 1].revenue / 2},
          ]
        : [
            {value: earnings[0].hours / 2},
            ...earnings.map(earning => ({value: earning.hours})),
            {value: earnings[earnings.length - 1].hours / 2},
          ];
      const xAxisLabels = ['', ...earnings.map(earning => earning.label), ''];
      const maxValue =
        Math.ceil(Math.max(...data.map(item => item.value)) / 10) * 10;

      return {totalHours, totalEarnings, data, xAxisLabels, maxValue};
    }, [earnings, isEarnings]);

  if (totalEarnings > 0 && totalHours > 0) {
    return (
      <View style={{gap: 10}}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View>
            <UixText
              variant="dotsBold"
              style={{fontSize: 20, lineHeight: 14, color: Colors.Teal}}
              capitalize>{`LIFETIME ${isEarnings ? 'EARNINGS' : 'HOURS'}`}</UixText>
            <UixText
              style={{
                fontSize: 32,
                fontWeight: 'bold',
                color: isEarnings ? Colors.Teal : Colors.Yellow,
              }}>
              {isEarnings
                ? `₹${totalEarnings.toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`
                : `${totalHours.toFixed(2)} hrs`}
            </UixText>
          </View>
          {showSwitch && (
            <ToggleSwitch
              switchOn={isEarnings}
              toggleSwitch={onToggleSwitch}
              highlightColor={isEarnings ? Colors.Teal : Colors.Yellow}
              backgroundColor={isEarnings ? Colors.Teal1000 : Colors.Yellow950}
              thumbIcon={isEarnings ? RupeeIcon : HourIcon}
            />
          )}
        </View>
        <LineChart
          isAnimated
          animateOnDataChange
          animationDuration={300}
          onDataChangeAnimationDuration={300}
          areaChart
          data={data}
          startFillColor={Colors.Teal}
          startOpacity={0.25}
          endFillColor={Colors.Teal}
          endOpacity={0}
          color={Colors.Teal}
          xAxisColor={Colors.Teal}
          yAxisColor={Colors.Teal}
          xAxisIndicesColor={Colors.Teal}
          yAxisIndicesColor={Colors.Teal}
          textColor={Colors.Teal}
          initialSpacing={0}
          endSpacing={0}
          dataPointsRadius1={0}
          yAxisTextStyle={{fontSize: 10, color: Colors.Teal}}
          yAxisLabelSuffix={isEarnings ? '' : 'hrs'}
          yAxisLabelPrefix={isEarnings ? '₹' : ''}
          yAxisOffset={0}
          yAxisLabelWidth={55}
          maxValue={maxValue}
          yAxisLabelContainerStyle={{
            justifyContent: 'flex-end',
            paddingRight: 10,
          }}
          xAxisLabelTextStyle={{
            fontSize: 10,
            marginLeft: 10,
            color: Colors.Teal,
          }}
          xAxisLabelTexts={xAxisLabels}
          xAxisLabelsVerticalShift={4}
          noOfSections={5}
          spacing={(Dimension.SCREEN_WIDTH - 36) / (earnings.length + 2)}
          onlyPositive
          disableScroll
          roundToDigits={1}
          hideRules
        />
      </View>
    );
  }
}
