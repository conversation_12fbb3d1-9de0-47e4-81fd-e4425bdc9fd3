import {RootState} from '@store/store';
import React, {useEffect, useMemo, useState} from 'react';
import {View} from 'react-native';
import {useSelector} from 'react-redux';

import {BadgeListModal} from '../badges/BadgeListModal';
import {formatDate} from '../utils';
import {MemberBadgeCard} from './MemberBadgeCard';
import {MemberDetailTile} from './MemberDetailTile';
import {MemberEarningsSection} from './MemberEarnigsSection';
import {MemberEngagementSection} from './MemberEngagementSection';
import {MemberPodsSection} from './MemberPodsSection';
import {MemberSkillSection} from './MemberSkillsSection';

import useUixMutation from '@/app/hooks/useUixMutation';
import {MemberActions} from '@/app/screens/memberDetails/components/MemberActions';
import {Colors} from '@/app/theme/colors';
import {Shimmer} from '@/app/ui/Shimmer';
import {ToggleSwitch} from '@/app/ui/ToggleSwitch';
import {UixText} from '@/app/ui/UixText';
import {MemberAvailability} from '@/graphQL/member';
import {
  MemberAvailabilityStatusChangeMutation,
  MemberAvailabilityStatusChangeMutationVariables,
  Members_Member_Details,
} from '@/types/__generated__/graphql';

interface Props {
  member: Members_Member_Details | undefined;
  earnings: Array<{
    hours: number;
    label: string;
    revenue: number;
    slot: number;
  }>;
  onRefetch: () => void;
  isLoading: boolean;
  isRedirectingFromHome?: boolean;
}

export function MemberInfoCard({
  member,
  earnings,
  onRefetch,
  isLoading,
  isRedirectingFromHome,
}: Props) {
  const projectCount = useMemo(
    () => member?.member_projects_aggregate?.aggregate?.count ?? 0,
    [member],
  );
  const leadCount = useMemo(
    () => member?.lead_dealmaker_hunters_aggregate?.aggregate?.count ?? 0,
    [member],
  );
  const memberProjects = member?.member_projects ?? [];
  const skills = useMemo(() => member?.member_skills ?? [], [member]);
  const retainers = useMemo(() => member?.member_retainers ?? [], [member]);
  const userDetails = useSelector((store: RootState) => store.user);
  const isCurrentUser = userDetails.id === member?.id;
  const [{switchOn, loading}, setState] = useState({
    switchOn: false,
    loading: false,
  });
  const highlightColor = switchOn ? Colors.Yellow : Colors.White300;

  const [mutation] = useUixMutation<
    MemberAvailabilityStatusChangeMutation,
    MemberAvailabilityStatusChangeMutationVariables
  >(MemberAvailability);

  useEffect(() => {
    setState(prev => ({
      ...prev,
      switchOn: (member?.member_status ?? 0) % 2 === 1,
    }));
  }, [member?.member_status]);

  const toggleSwitch = async () => {
    if (loading) return;
    setState(prev => ({...prev, loading: true, switchOn: !prev.switchOn}));
    await mutation({
      variables: {
        id: userDetails.id,
        member_status: switchOn ? 2 : 1,
      },
    });
    await onRefetch();
    setState(prev => ({...prev, loading: false}));
  };

  const [isBadgeModalOpen, setIsBadgeModalOpen] = useState(false);

  return (
    <View style={{gap: 24}}>
      <MemberBadgeCard
        member={member}
        isActive={switchOn}
        isLoading={isLoading}
        onBadgeModalOpen={() => setIsBadgeModalOpen(true)}
        isRedirectingFromHome={isRedirectingFromHome}
      />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View style={{flex: 1}}>
          {member ? (
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 32,
                lineHeight: 24,
                color: highlightColor,
              }}
              capitalize>
              {`${member.alias}`}
            </UixText>
          ) : (
            <Shimmer style={{height: 32, borderRadius: 6}} />
          )}
          {member?.designation && (
            <UixText
              variant="dots"
              style={{
                fontSize: 24,
                lineHeight: 28,
                color: switchOn ? Colors.Teal : Colors.White300,
              }}
              capitalize>
              {`${member.designation}`}
            </UixText>
          )}
        </View>
        {isCurrentUser && (
          <ToggleSwitch switchOn={switchOn} toggleSwitch={toggleSwitch} />
        )}
      </View>
      {member?.bio?.length && (
        <UixText
          variant="titleMedium"
          style={{color: switchOn ? Colors.Teal700 : Colors.White500}}>
          {member.bio}
        </UixText>
      )}
      {isLoading && (
        <Shimmer style={{height: 64, width: 'full', borderRadius: 6}} />
      )}
      {!isLoading &&
        member &&
        (retainers.length > 0 || isCurrentUser) &&
        switchOn && (
          <MemberEngagementSection
            active={switchOn}
            retainers={retainers}
            isCurrentUser={isCurrentUser}
          />
        )}
      {member && (skills.length || isCurrentUser) && switchOn && (
        <MemberSkillSection
          skills={skills}
          active={switchOn}
          isCurrentUser={isCurrentUser}
        />
      )}
      {!isLoading && Boolean(memberProjects.length) && switchOn && (
        <MemberPodsSection projects={memberProjects} />
      )}
      {!isLoading && Boolean(earnings.length) && switchOn && (
        <MemberEarningsSection
          earnings={earnings}
          showSwitch={
            isCurrentUser || ['Founder'].includes(`${userDetails.type?.name}`)
          }
        />
      )}
      <View style={{gap: 10}}>
        {!isLoading && Boolean(projectCount || leadCount) && (
          <View style={{flexDirection: 'row', gap: 10}}>
            <MemberDetailTile
              label="Projects"
              value={`${projectCount && projectCount > 0 ? projectCount + '+' : '0'}`}
              active={switchOn}
              loading={projectCount == undefined}
              clipped
            />
            <MemberDetailTile
              label="Leads"
              value={`${leadCount && leadCount > 0 ? leadCount + '+' : '0'}`}
              active={switchOn}
              loading={leadCount == undefined}
            />
          </View>
        )}
        <MemberDetailTile
          label="Member Since"
          value={`${formatDate(new Date(member?.joining_date))}`}
          active={switchOn}
          loading={member == undefined}
        />
        {isCurrentUser && <MemberActions isActive={switchOn} />}
      </View>
      <BadgeListModal
        isOpen={isBadgeModalOpen}
        onClose={() => setIsBadgeModalOpen(false)}
        memberBadges={member?.member_badges ?? []}
      />
    </View>
  );
}
