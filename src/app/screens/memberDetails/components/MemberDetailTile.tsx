import {View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixCornerView} from '@/app/ui/UixCornerView';
import {UixText} from '@/app/ui/UixText';

export const MemberDetailTile = ({
  label,
  value,
  active,
  loading,
  clipped = false,
}: {
  label: string;
  value: string;
  active: boolean;
  loading: boolean;
  clipped?: boolean;
}) => (
  <UixCornerView
    color={active ? Colors.Teal950 : Colors.Teal975}
    cut={0.25}
    type="tl"
    borderRadius={8}
    viewStyle={{flexGrow: 0}}>
    <View
      style={{
        padding: 24,
        paddingTop: 30,
        flex: 1,
        borderRadius: 8,
        backgroundColor: clipped
          ? 'transparent'
          : active
            ? Colors.Teal950
            : Colors.Teal975,
      }}>
      {loading ? (
        <Shimmer
          style={{
            height: 22,
            width: 100,
            borderRadius: 4,
            marginVertical: 10,
          }}
        />
      ) : (
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 36,
            lineHeight: 38,
            color: active ? Colors.Teal : Colors.White300,
          }}
          capitalize>
          {value}
        </UixText>
      )}
      <UixText
        variant="dotsBold"
        style={{
          fontSize: 25,
          lineHeight: 25,
          color: active ? Colors.Teal700 : Colors.White700,
        }}
        capitalize>
        {label}
      </UixText>
    </View>
  </UixCornerView>
);
