import {Image, TouchableOpacity, View} from 'react-native';

import {createGreyscaleUrl} from '../../utils';

import {Colors} from '@/app/theme/colors';
import {Shimmer} from '@/app/ui/Shimmer';

export function MemberBadgeTile({
  badge,
  isActive,
  onPress,
}: {
  badge?: string;
  isActive: boolean;
  onPress: () => void;
}) {
  return badge !== undefined ? (
    <TouchableOpacity
      activeOpacity={0.5}
      disabled={!badge}
      onPress={onPress}
      style={{
        flex: 1,
        aspectRatio: 1,
        backgroundColor: isActive ? Colors.Teal950 : Colors.White900,
        borderRadius: 8,
      }}>
      {badge && (
        <View
          style={{
            shadowColor: isActive ? Colors.Teal : Colors.White,
            shadowOpacity: 0.5,
            shadowRadius: 32,
            elevation: 50,
            borderRadius: 40,
            padding: 16,
          }}>
          <Image
            source={{
              uri: isActive ? (badge ?? '') : createGreyscaleUrl(badge ?? ''),
            }}
            style={{
              width: '100%',
              height: '100%',
            }}
          />
        </View>
      )}
    </TouchableOpacity>
  ) : (
    <Shimmer style={{flex: 1, width: '100%', borderRadius: 8}} />
  );
}
