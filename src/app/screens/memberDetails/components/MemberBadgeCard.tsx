import MemberCardFrame from '@assets/member_card_frame.svg';
import MemberCardFramePlain from '@assets/member_card_frame_plain.svg';
import React from 'react';
import {Image, View} from 'react-native';

import {createGreyscaleUrl} from '../../utils';
import {formatDate} from '../utils';
import {MemberBadgeTile} from './MemberBadgeTile';

import {Colors} from '@/app/theme/colors';
import {AvailabilityMarquee} from '@/app/ui/AvailabilityMarquee';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Details} from '@/types/__generated__/graphql';

export function MemberBadgeCard({
  member,
  onBadgeModalOpen,
  isActive,
  isLoading = false,
  isRedirectingFromHome = false,
}: {
  member?: Members_Member_Details;
  onBadgeModalOpen: () => void;
  isActive: boolean;
  isLoading?: boolean;
  isRedirectingFromHome?: boolean;
}) {
  const badges = member?.member_badges ?? [];

  const getBadge = (index: number) => {
    return member && !isLoading
      ? (badges[index]?.badge_detail.badge_media ?? '')
      : undefined;
  };

  return (
    <View>
      {member && !isLoading && badges.length === 0 ? (
        <MemberCardFrame
          width="100%"
          height="100%"
          preserveAspectRatio="none"
          style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0}}
        />
      ) : (
        <MemberCardFramePlain
          width="100%"
          height="100%"
          preserveAspectRatio="none"
          style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0}}
        />
      )}
      <View
        style={{
          padding: 10,
          flexDirection: 'row',
          gap: 10,
        }}>
        <View
          style={{
            flex: 1,
            aspectRatio: 1,
            backgroundColor: Colors.Teal950,
            borderRadius: 8,
          }}>
          {member ? (
            <Image
              resizeMode="cover"
              source={
                member.member_photo
                  ? {
                      uri: isActive
                        ? (member.member_photo ?? '')
                        : createGreyscaleUrl(member.member_photo ?? ''),
                    }
                  : isActive
                    ? require('@assets/avatars/default.png')
                    : require('@assets/avatars/defaultGrayscale.png')
              }
              style={{
                height: '100%',
                width: '100%',
                borderRadius: 8,
              }}
            />
          ) : (
            <Shimmer style={{width: '100%', height: '100%', borderRadius: 8}} />
          )}
          {member && <AvailabilityMarquee isActive={isActive} />}
        </View>
        <View style={{flex: 1, aspectRatio: 1, flexDirection: 'row', gap: 10}}>
          {badges.length > 0 && (
            <>
              <View style={{flex: 1, gap: 10}}>
                {[0, 1].map(index => (
                  <MemberBadgeTile
                    key={index}
                    badge={getBadge(index)}
                    isActive={isActive}
                    onPress={onBadgeModalOpen}
                  />
                ))}
              </View>
              <View style={{flex: 1, gap: 10}}>
                {[2, 3].map(index => (
                  <MemberBadgeTile
                    key={index}
                    badge={getBadge(index)}
                    isActive={isActive}
                    onPress={onBadgeModalOpen}
                  />
                ))}
              </View>
            </>
          )}

          {badges.length === 0 && !isRedirectingFromHome && (
            <View style={{flexDirection: 'column', justifyContent: 'flex-end'}}>
              <UixText
                variant="dotsBold"
                style={{
                  fontSize: 20,
                  lineHeight: 20,
                  color: isActive ? '#04ECC0B2' : Colors.White300,
                  alignSelf: 'flex-end',
                }}>
                JOINED
              </UixText>
              <UixText
                variant="dotsBold"
                style={{
                  fontSize: 20,
                  lineHeight: 20,
                  color: isActive ? '#04ECC0B2' : Colors.White300,
                }}>
                {`${formatDate(new Date(member?.joining_date))}`}
              </UixText>
            </View>
          )}
        </View>
      </View>
    </View>
  );
}
