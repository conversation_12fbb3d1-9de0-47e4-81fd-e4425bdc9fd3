import {Image, Pressable, View} from 'react-native';

import {SuggestionList} from '../../AddSkill/SuggestionList';
import MemberSectionEmptyState from './MemberSectionEmptyState';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {UixCornerView} from '@/app/ui/UixCornerView';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Skills} from '@/types/__generated__/graphql';

export function MemberSkillSection({
  active,
  skills,
  isCurrentUser,
}: {
  active: boolean;
  skills: Members_Member_Skills[];
  isCurrentUser: boolean;
}) {
  const onEditSkill = () => {
    if (isCurrentUser) NavigationService.navigate(ScreenNames.AddSkill);
  };

  return (
    <View style={{flexDirection: 'column', gap: 16}}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 20,
            lineHeight: 16,
            color: active ? Colors.Teal : Colors.White300,
          }}
          capitalize>{`Skills${skills.length > 0 ? ` . ${skills.length}` : ''}`}</UixText>
        {isCurrentUser && (
          <UixCornerView
            color={Colors.Teal950}
            viewStyle={{padding: 8, paddingHorizontal: 10}}>
            <Pressable
              onPress={onEditSkill}
              style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              <Image
                source={require('@assets/plusTeal.png')}
                style={{width: 16, height: 16}}
              />
              <UixText
                variant="dotsBold"
                style={{fontSize: 20, lineHeight: 18, color: Colors.Teal}}
                capitalize>
                Add
              </UixText>
            </Pressable>
          </UixCornerView>
        )}
      </View>
      {skills.length ? (
        <SuggestionList userSkills={skills} />
      ) : (
        <MemberSectionEmptyState
          title={`Highlight what\nyou’re great at`}
          subtitle={`Add skills to get discovered for\nthe work you do best.`}
          description={`This helps recruiters find you for\nthe work you do best.`}
          screenName={ScreenNames.AddSkill}
        />
      )}
    </View>
  );
}
