import ChevronDown from '@assets/chevron-down.svg';
import ChevronUp from '@assets/chevron-up.svg';
import {useRef, useState} from 'react';
import {Image, LayoutChangeEvent, View} from 'react-native';
import {Pressable} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import Dimension from '@/app/utils/dimension';
import {Members_Member_Projects} from '@/types/__generated__/graphql';

export function MemberPodsSection({
  projects,
}: {
  projects: Members_Member_Projects[];
}) {
  const [isOpen, setIsOpen] = useState(true);
  const [measured, setMeasured] = useState(false);

  const contentHeight = useRef(0);
  const heightValue = useSharedValue(0);
  const opacityValue = useSharedValue(1);

  const toggleDropdown = () => {
    try {
      if (measured && contentHeight.current > 0) {
        const toValue = isOpen ? 0 : contentHeight.current;
        heightValue.value = withTiming(toValue, {duration: 300});
        opacityValue.value = withTiming(isOpen ? 0 : 1, {duration: 200});
        setIsOpen(prev => !prev);
      }
    } catch (error) {
      console.warn('Animation error:', error);
      setIsOpen(prev => !prev);
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: measured ? heightValue.value : null,
      opacity: opacityValue.value,
      overflow: 'hidden',
    };
  });

  const handleContentLayout = (event: LayoutChangeEvent) => {
    try {
      const height = event.nativeEvent.layout.height;
      if (height > 0 && isFinite(height)) {
        contentHeight.current = height;
        if (!measured) {
          heightValue.value = isOpen ? height : 0;
          opacityValue.value = isOpen ? 1 : 0;
          setMeasured(true);
        }
      }
    } catch (error) {
      console.warn('Layout measurement error:', error);
    }
  };

  function ProjectsList() {
    return (
      <View
        style={{
          gap: 10,
          marginBottom: 10,
          paddingHorizontal: 10,
          justifyContent: 'space-between',
          flexDirection: 'row',
          flexWrap: 'wrap',
        }}>
        {projects.map((project, index) => {
          const logoUri = project.project_detail?.project_logo;
          if (!logoUri) return null;
          return (
            <View
              key={project.project_detail?.id?.toString() ?? index.toString()}
              style={{
                backgroundColor: Colors.Black,
                padding: 10,
                borderRadius: 8,
                width: (Dimension.SCREEN_WIDTH - 62) / 2,
              }}>
              <Image
                source={{uri: logoUri}}
                style={{
                  width: '100%',
                  borderRadius: 6,
                  aspectRatio: 149 / 39,
                }}
                resizeMode="contain"
                onError={e => {
                  console.warn(
                    'Failed to load logo:',
                    logoUri,
                    e.nativeEvent.error,
                  );
                }}
              />
            </View>
          );
        })}
      </View>
    );
  }

  return (
    <View style={{backgroundColor: Colors.Teal950, borderRadius: 8}}>
      <Pressable
        onPress={toggleDropdown}
        style={{
          flexDirection: 'row',
          padding: 10,
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <UixText
          variant="dotsBold"
          style={{fontSize: 20, lineHeight: 18, color: Colors.Teal}}
          capitalize>
          {`All pods ${projects.length > 0 ? `- ${projects.length}` : ''}`}
        </UixText>
        <View
          style={{
            width: 44,
            height: 44,
            backgroundColor: Colors.Teal900,
            borderRadius: 6,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          {isOpen ? <ChevronUp /> : <ChevronDown />}
        </View>
      </Pressable>
      {measured ? (
        <Animated.View style={[animatedStyle]}>
          <ProjectsList />
        </Animated.View>
      ) : (
        <View onLayout={handleContentLayout} style={{opacity: 0}}>
          <ProjectsList />
        </View>
      )}
    </View>
  );
}
