import React from 'react';
import {View} from 'react-native';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {RootStackParamsList} from '@/app/navigation/types';
import {Colors} from '@/app/theme/colors';
import {ButtonType, RoundedButton} from '@/app/ui/RoundedButton';
import {UixText} from '@/app/ui/UixText';

export default function MemberSectionEmptyState({
  title,
  subtitle,
  description,
  screenName,
}: {
  title: string;
  subtitle: string;
  description: string;
  screenName: ScreenNames;
}) {
  const onPress = async () => {
    NavigationService.navigate(screenName as keyof RootStackParamsList);
  };

  return (
    <View
      style={{
        padding: 16,
        borderRadius: 8,
        backgroundColor: Colors.Teal950,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 12,
      }}>
      <UixText
        variant="dotsBold"
        style={{
          fontSize: 24,
          lineHeight: 30,
          color: Colors.Teal,
          textAlign: 'center',
        }}
        capitalize>
        {title}
      </UixText>
      <UixText
        variant="titleMedium"
        style={{
          color: Colors.Teal600,
          fontWeight: 'medium',
          textAlign: 'center',
        }}>
        {subtitle}
      </UixText>
      <RoundedButton
        type={ButtonType.SECONDARY}
        onPress={onPress}
        label={'        + Add        '}
        transparent
      />
      <UixText
        variant="titleMedium"
        style={{textAlign: 'center', color: Colors.Yellow600}}>
        {description}
      </UixText>
    </View>
  );
}
