import DeleteIcon from '@assets/delete.svg';
import LogoutIcon from '@assets/logout.svg';
import {CommonActions} from '@store/slices/CommonSlice';
import {RootState} from '@store/store';
import React from 'react';
import {Linking, Pressable, View} from 'react-native';
import Svg, {Path} from 'react-native-svg';
import {useDispatch, useSelector} from 'react-redux';

import useUixMutation from '@/app/hooks/useUixMutation';
import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import EncryptedStore from '@/app/utils/encryptedStore';
import Utils from '@/app/utils/utils';
import {deleteMember} from '@/graphQL/member';
import {
  DeleteMemberMutation,
  DeleteMemberMutationVariables,
} from '@/types/__generated__/graphql';

function TermsIcon({
  size = 24,
  color = '#00FFD1',
}: {
  size?: number;
  color?: string;
}) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M20.2168 3.78711H21.3093V22.4027H20.2168V3.78711Z"
        fill={color}
      />
      <Path d="M19.1172 2.6875H20.2169V3.78719H19.1172V2.6875Z" fill={color} />
      <Path d="M18.0249 1.5957H19.1174V2.6882H18.0249V1.5957Z" fill={color} />
      <Path
        d="M19.1158 5.97219H18.0233V4.88328H16.9308V3.78719H15.8312V2.6875H2.69385V23.4989H19.1158V5.97219ZM18.0233 22.4028H3.78635V3.78719H13.6404V8.16437H18.0248L18.0233 22.4028Z"
        fill={color}
      />
      <Path
        d="M3.78613 0.496094H18.0246V1.59578H3.78613V0.496094Z"
        fill={color}
      />
      <Path
        d="M5.97852 19.1191H15.8326V20.2116H5.97852V19.1191Z"
        fill={color}
      />
      <Path
        d="M5.97852 14.7344H15.8326V15.8341H5.97852V14.7344Z"
        fill={color}
      />
      <Path
        d="M5.97852 10.3574H15.8326V11.4499H5.97852V10.3574Z"
        fill={color}
      />
      <Path
        d="M5.97852 5.97266H10.3557V7.07234H5.97852V5.97266Z"
        fill={color}
      />
    </Svg>
  );
}

function PrivacyIcon({
  size = 24,
  color = '#00FFD1',
}: {
  size?: number;
  color?: string;
}) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M22.4077 3.78809H23.5002V20.2115H22.4077V3.78809Z"
        fill={color}
      />
      <Path
        d="M14.7383 20.2109H22.4073V21.3106H14.7383V20.2109Z"
        fill={color}
      />
      <Path
        d="M18.0303 3.78809H19.1228V4.88059H18.0303V3.78809Z"
        fill={color}
      />
      <Path
        d="M16.9307 10.3574H19.1229V11.4499H16.9307V10.3574Z"
        fill={color}
      />
      <Path d="M13.646 14.7344H19.1229V15.8341H13.646V14.7344Z" fill={color} />
      <Path d="M13.646 12.5498H19.1229V13.6423H13.646V12.5498Z" fill={color} />
      <Path d="M13.646 10.3574H15.8382V11.4499H13.646V10.3574Z" fill={color} />
      <Path d="M13.646 8.16504H18.0304V9.26473H13.646V8.16504Z" fill={color} />
      <Path d="M13.646 5.97266H19.1229V7.07234H13.646V5.97266Z" fill={color} />
      <Path d="M13.646 3.78809H16.9307V4.88059H13.646V3.78809Z" fill={color} />
      <Path d="M12.5532 1.5957H20.2151V2.6882H12.5532V1.5957Z" fill={color} />
      <Path d="M9.26855 21.3105H14.7382V22.403H9.26855V21.3105Z" fill={color} />
      <Path
        d="M9.26855 10.3574H10.3611V11.4499H9.26855V10.3574Z"
        fill={color}
      />
      <Path
        d="M9.26855 8.16504H10.3611V9.26473H9.26855V8.16504Z"
        fill={color}
      />
      <Path
        d="M9.26855 5.97266H10.3611V7.07234H9.26855V5.97266Z"
        fill={color}
      />
      <Path
        d="M9.26855 3.78809H10.3611V4.88059H9.26855V3.78809Z"
        fill={color}
      />
      <Path
        d="M1.59961 20.2109H9.26867V21.3106H1.59961V20.2109Z"
        fill={color}
      />
      <Path
        d="M7.07666 14.7344H10.3613V15.8341H7.07666V14.7344Z"
        fill={color}
      />
      <Path
        d="M5.97705 12.5498H10.3614V13.6423H5.97705V12.5498Z"
        fill={color}
      />
      <Path
        d="M4.88428 14.7344H5.97678V15.8341H4.88428V14.7344Z"
        fill={color}
      />
      <Path
        d="M4.88449 11.4498H5.97699V10.3573H7.07668V11.4498H8.16918V2.6882H11.4539V1.5957H3.79199V2.6882H4.88449V11.4498Z"
        fill={color}
      />
      <Path
        d="M2.69211 19.1191H10.3612V20.2116H13.6459V19.1191H21.3149V3.78816H22.4074V2.68848H20.2152V18.0266H12.5534V2.68848H11.4537V18.0266H3.7918V2.68848H1.59961V3.78816H2.69211V19.1191Z"
        fill={color}
      />
      <Path d="M0.5 3.78809H1.59969V20.2115H0.5V3.78809Z" fill={color} />
    </Svg>
  );
}

export function MemberActions({isActive}: {isActive: boolean}) {
  const dispatch = useDispatch();
  const userDetails = useSelector((store: RootState) => store.user);

  const actions = [
    {
      icon: TermsIcon,
      label: 'Terms & Conditions',
      onClick: () => {
        Linking.openURL(
          'https://docs.google.com/document/d/1u5zBE_5AeJo1OE4L0x2H-ffPXgcLBLcUaK-ZA34JA0Y/edit?tab=t.0',
        );
      },
      color: isActive ? Colors.Teal : Colors.White,
    },
    {
      icon: PrivacyIcon,
      label: 'Privacy Policy',
      onClick: () => {
        Linking.openURL(
          'https://docs.google.com/document/d/1gHaxttfuozsOD_qtCOYli9nkf-syeXjjAtaqzpcmrko/edit?tab=t.0',
        );
      },
      color: isActive ? Colors.Teal : Colors.White,
    },
    {
      icon: DeleteIcon,
      label: 'Delete Account',
      onClick: async () => {
        await mutation({
          variables: {
            id: userDetails.id,
          },
        }).then(() => {
          EncryptedStore.clearStore();
          dispatch(CommonActions.updateIsLogin(false));
          Utils.onLogout();
        });
      },
      color: Colors.Logout,
    },
    {
      icon: LogoutIcon,
      label: 'Logout',
      onClick: () => {
        EncryptedStore.clearStore();
        dispatch(CommonActions.updateIsLogin(false));
        Utils.onLogout();
      },
      color: Colors.Logout,
    },
  ];

  const [mutation] = useUixMutation<
    DeleteMemberMutation,
    DeleteMemberMutationVariables
  >(deleteMember);

  return (
    <View
      style={{
        padding: 16,
        gap: 16,
        borderRadius: 8,
        backgroundColor: isActive ? Colors.Teal950 : Colors.Teal975,
      }}>
      {actions.map(action => {
        const Icon = action?.icon;
        return (
          <Pressable
            key={action.label}
            style={{flexDirection: 'row', alignItems: 'center', gap: 16}}
            onPress={action.onClick}>
            <View
              style={{
                height: 40,
                width: 40,
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: isActive ? Colors.Teal900 : Colors.Teal950,
              }}>
              <Icon color={action.color} />
            </View>
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 18,
                color: action.color,
              }}
              capitalize>
              {action.label}
            </UixText>
          </Pressable>
        );
      })}
    </View>
  );
}
