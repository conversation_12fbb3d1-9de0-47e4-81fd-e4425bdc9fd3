import {useState} from 'react';
import {ActivityIndicator, FlatList, View} from 'react-native';

import {BadgeInfoView} from './BadgeInfoView';
import {BadgeListItem} from './BadgeListItem';

import useUixQuery from '@/app/hooks/useUixQuery';
import {Colors} from '@/app/theme/colors';
import {FullScreenModal} from '@/app/ui/FullScreenModal';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {GetBadges} from '@/graphQL/badge';
import {
  Badges_Badge_Details,
  Members_Member_Badges,
} from '@/types/__generated__/graphql';

interface GetBadgesResponse {
  badges_badge_details: Badges_Badge_Details[];
}

export function BadgeListModal({
  isOpen,
  memberBadges,
  onClose,
}: {
  isOpen: boolean;
  memberBadges: Members_Member_Badges[];
  onClose: () => void;
}) {
  const [selectedBadge, setSelectedBadge] = useState<Badges_Badge_Details>();
  const {data, loading} = useUixQuery<GetBadgesResponse>(GetBadges);

  const allBadges: Badges_Badge_Details[] = data?.badges_badge_details ?? [];

  const assignedBadgeIds = new Set(
    memberBadges.flatMap(badge => [badge.badge_detail.id, badge.badge_id]),
  );

  const filteredBadges = allBadges
    .map(badge => ({
      ...badge,
      assigned: assignedBadgeIds.has(badge.id),
    }))
    .sort((a, b) => (a.assigned === b.assigned ? 0 : a.assigned ? -1 : 1));

  return (
    <FullScreenModal
      isOpen={isOpen}
      onClose={onClose}
      backgroundColor={Colors.DarkBackground}>
      {selectedBadge ? (
        <BadgeInfoView
          badge={selectedBadge}
          assigned={assignedBadgeIds.has(selectedBadge.id)}
          onDone={() => setSelectedBadge(undefined)}
        />
      ) : (
        <FlatList
          contentContainerStyle={{
            flexGrow: 1,
            padding: 16,
            justifyContent: 'center',
            gap: 10,
          }}
          data={loading ? [] : filteredBadges}
          ListEmptyComponent={<ActivityIndicator style={{flexGrow: 1}} />}
          ListHeaderComponent={
            <View style={{gap: 24, marginBottom: 12}}>
              <UixButton
                label="Close"
                iconImage={require('@assets/cross.png')}
                onPress={onClose}
                tintColor={'Teal'}
                viewStyle={{height: 40}}
              />
              <UixText
                variant="dotsBold"
                style={{color: Colors.Yellow, fontSize: 32, lineHeight: 22}}
                capitalize>
                Collectibles
              </UixText>
            </View>
          }
          removeClippedSubviews={false}
          scrollEnabled={false}
          renderItem={({item}) => (
            <BadgeListItem
              item={item}
              assigned={item.assigned}
              setSelectedBadge={setSelectedBadge}
            />
          )}
        />
      )}
    </FullScreenModal>
  );
}
