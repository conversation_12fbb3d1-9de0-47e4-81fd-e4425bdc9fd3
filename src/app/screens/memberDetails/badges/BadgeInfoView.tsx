import dayjs from 'dayjs';
import {useMemo} from 'react';
import {View} from 'react-native';

import {BadgeImageWidget} from './BadgeImageWidget';

import useUixQuery from '@/app/hooks/useUixQuery';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {AssignedBadgeQuery} from '@/graphQL/badge';
import {
  AssignedBadgeQueryQuery,
  Badges_Badge_Details,
} from '@/types/__generated__/graphql';

export function BadgeInfoView({
  badge,
  assigned,
  onDone,
}: {
  badge: Badges_Badge_Details;
  assigned: boolean;
  onDone: () => void;
}) {
  const {data, loading} = useUixQuery<AssignedBadgeQueryQuery>(
    AssignedBadgeQuery,
    {
      variables: {
        id: badge.id,
      },
    },
  );

  const statusText = useMemo(() => {
    return assigned
      ? `Collected on ${loading ? '....' : dayjs(data?.members_member_badges?.[0].created_at).format('MMM YYYY')}`
      : `Unlocked by ${
          data?.members_member_badges?.length || (loading ? 'x' : 0)
        } members so far`;
  }, [assigned, data]);

  return (
    <View style={{flex: 1}}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: 16,
        }}>
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 20,
            color: assigned ? Colors.Teal700 : Colors.White600,
          }}
          capitalize>
          Achievement
        </UixText>
        <UixButton
          label="Done"
          onPress={onDone}
          tintColor={assigned ? 'Teal' : 'White'}
          viewStyle={{height: 40}}
        />
      </View>
      <BadgeImageWidget
        badgeMedia={badge?.badge_media ?? ''}
        assigned={assigned}
      />
      <View
        style={{
          alignItems: 'center',
          gap: 16,
          padding: 16,
          paddingHorizontal: 32,
        }}>
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 36,
            lineHeight: 36,
            color: assigned ? Colors.Yellow : Colors.White,
            textShadowColor: Colors.Yellow900,
            textShadowRadius: 10,
            textShadowOffset: {width: 0, height: 0},
            textAlign: 'center',
          }}
          capitalize>
          {badge.badge_name}
        </UixText>
        <UixText
          variant="titleMedium"
          style={{
            textAlign: 'center',
            color: assigned ? Colors.Teal600 : Colors.White600,
          }}>
          {statusText}
        </UixText>
        {badge.badge_description && (
          <UixText
            style={{
              fontSize: 18,
              fontWeight: 'medium',
              textAlign: 'center',
              color: assigned ? Colors.Teal : Colors.White300,
            }}>
            {badge.badge_description}
          </UixText>
        )}
      </View>
    </View>
  );
}
