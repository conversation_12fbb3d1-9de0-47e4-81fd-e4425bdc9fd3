import {Dispatch} from 'react';
import {Image, Pressable, View} from 'react-native';

import {createGreyscaleUrl} from '../../utils';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import {Badges_Badge_Details} from '@/types/__generated__/graphql';

interface Props {
  item: Badges_Badge_Details;
  assigned: boolean;
  setSelectedBadge: Dispatch<
    React.SetStateAction<Badges_Badge_Details | undefined>
  >;
}

export function BadgeListItem({item, assigned, setSelectedBadge}: Props) {
  return (
    <Pressable
      onPress={() => setSelectedBadge(item)}
      style={({pressed}) => ({
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: assigned ? Colors.Teal1000 : Colors.White1000,
        padding: 10,
        borderRadius: 12,
        gap: 16,
        borderWidth: 1,
        borderColor: assigned ? Colors.Teal950 : Colors.White950,
        opacity: pressed ? 0.5 : 1,
      })}>
      <View
        style={{
          width: 100,
          height: 100,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 6,
          backgroundColor: assigned ? Colors.Teal950 : Colors.White950,
        }}>
        <View
          style={
            assigned
              ? {
                  shadowColor: Colors.Teal,
                  shadowOpacity: 0.5,
                  shadowRadius: 25,
                  elevation: 50,
                  borderRadius: 40,
                }
              : undefined
          }>
          {item?.badge_media?.length && (
            <Image
              source={{
                uri: assigned
                  ? item.badge_media
                  : createGreyscaleUrl(item?.badge_media),
              }}
              style={{width: 70, height: 70}}
            />
          )}
        </View>
      </View>
      <View
        style={{
          flex: 1,
          gap: 4,
        }}>
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 24,
            lineHeight: 20,
            color: assigned ? Colors.Teal : Colors.White300,
          }}
          capitalize>
          {item.badge_name}
        </UixText>
        {item.badge_description && (
          <UixText
            variant="dots"
            style={{
              fontSize: 16,
              lineHeight: 16,
              color: assigned ? Colors.Teal600 : Colors.White700,
            }}>
            {item.badge_description}
          </UixText>
        )}
      </View>
    </Pressable>
  );
}
