import {Image, ImageBackground, View} from 'react-native';

import {createGreyscaleUrl} from '../../utils';

export function BadgeImageWidget({
  assigned,
  badgeMedia,
}: {
  assigned: boolean;
  badgeMedia: string;
}) {
  const renderLock = () => (
    <View style={{position: 'relative'}}>
      <Image
        source={{uri: assigned ? badgeMedia : createGreyscaleUrl(badgeMedia)}}
        style={{height: 180, width: 180}}
      />
      {!assigned && (
        <Image
          source={require('@assets/locked.png')}
          style={{
            height: 40,
            width: 40,
            position: 'absolute',
            bottom: -20,
            left: 70,
            zIndex: 1,
          }}
        />
      )}
    </View>
  );

  return (
    <ImageBackground
      source={
        assigned ? require('@assets/badgeDetailBackground.png') : undefined
      }
      style={{alignItems: 'center', justifyContent: 'center', flex: 1}}
      resizeMode="cover"
      resizeMethod="scale">
      {badgeMedia && renderLock()}
    </ImageBackground>
  );
}
