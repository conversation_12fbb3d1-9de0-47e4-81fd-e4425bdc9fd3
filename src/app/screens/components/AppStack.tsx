import {createNativeStackNavigator} from '@react-navigation/native-stack';
import store from '@store/store';

import {BottomNavigator} from '../../navigation/components/BottomNavigator';
import {ScreenNames} from '../../navigation/constants';
import {AccessRequestScreen} from '../AccessRequestScreen';
import AddSkillsScreen from '../AddSkill/AddSkillScreen';
import {EngagementScreen} from '../engagement/EngagementScreen';
import ReminderScreen from '../home/<USER>';
import LogEntries from '../home/<USER>/LogEntries/LogEntries';
import {NetworkScreen} from '../NetworkScreen';
import StoriesScreen from '../stories/StoriesScreen';
import {StoryTimelineScreen} from '../storyTimeline/StoryTimelineScreen';

import DropDetailsScreen from '@/app/screens/drops/DropDetailsScreen';
import DropsScreen from '@/app/screens/drops/DropsScreen';
import EnterMobileScreen from '@/app/screens/EnterMobileScreen';
import LoginScreen from '@/app/screens/LoginScreen';
import MemberDetailsScreen from '@/app/screens/memberDetails/MemberDetailsScreen';
import {MembersScreen} from '@/app/screens/members/MembersScreen';
import OtpVerificationScreen from '@/app/screens/otpVerification/OtpVerificationScreen';
import {PlaybookScreen} from '@/app/screens/playbook/PlaybookScreen';
import {SplashScreen} from '@/app/screens/SplashScreen';

const Stack = createNativeStackNavigator();

export const AppStack = () => {
  const isLoggedIn = store.getState().common.isLogin;

  const initialScreenName = isLoggedIn ? ScreenNames.App : ScreenNames.Login;

  return (
    <Stack.Navigator
      initialRouteName={initialScreenName}
      screenOptions={{
        headerShown: false,
        headerTransparent: false,
      }}>
      <Stack.Screen name={ScreenNames.Splash} component={SplashScreen} />
      <Stack.Screen name={ScreenNames.Login} component={LoginScreen} />
      <Stack.Screen
        name={ScreenNames.AccessRequest}
        component={AccessRequestScreen}
      />
      <Stack.Screen
        name={ScreenNames.OtpVerification}
        component={OtpVerificationScreen}
      />
      <Stack.Screen
        name={ScreenNames.EnterMobile}
        component={EnterMobileScreen}
      />
      <Stack.Screen name={ScreenNames.App} component={BottomNavigator} />
      <Stack.Screen name={ScreenNames.Drops} component={DropsScreen} />
      <Stack.Screen
        name={ScreenNames.DropDetail}
        component={DropDetailsScreen}
      />
      <Stack.Screen name={ScreenNames.PlayBook} component={PlaybookScreen} />
      <Stack.Group>
        <Stack.Screen name={ScreenNames.Stories} component={StoriesScreen} />
        <Stack.Screen
          name={ScreenNames.StoryTimeline}
          component={StoryTimelineScreen}
        />
      </Stack.Group>
      <Stack.Screen
        name={ScreenNames.MemberDetail}
        component={MemberDetailsScreen}
      />
      <Stack.Screen
        name={ScreenNames.Engagement}
        component={EngagementScreen}
      />
      <Stack.Screen name={ScreenNames.AddSkill} component={AddSkillsScreen} />
      <Stack.Screen name={ScreenNames.Network} component={NetworkScreen} />
      <Stack.Screen name={ScreenNames.Members} component={MembersScreen} />

      <Stack.Screen name={ScreenNames.LogEntries} component={LogEntries} />

      <Stack.Screen
        name={ScreenNames.TimeLogReminder}
        component={ReminderScreen}
      />
    </Stack.Navigator>
  );
};
