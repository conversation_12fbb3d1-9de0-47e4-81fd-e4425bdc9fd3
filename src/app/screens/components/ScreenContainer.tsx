import {PropsWithChildren, useEffect} from 'react';
import {
  ImageBackground,
  ImageSourcePropType,
  RefreshControl,
  StyleProp,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import {
  Edges,
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import {useKeyboardVisible} from '../utils';

import {Tracker} from '@/app/analytics/tracker';
import {ScreenNames} from '@/app/navigation/constants';
import {Colors} from '@/app/theme/colors';
import {useGetHeaderHeight, useGetTabBarHeight} from '@/app/theme/hooks';

interface ScreenContainerProps extends PropsWithChildren {
  name: keyof typeof ScreenNames;
  background?: 'old' | 'new' | 'grid';
  disableBackground?: boolean;
  scrollDisabled?: boolean;
  tabScreen?: boolean;
  style?: StyleProp<ViewStyle>;
  contentContainerStyle?: StyleProp<ViewStyle>;
  onRefresh?: () => void;
  edges?: Edges;
  enableKeyboardScrollView?: boolean;
  refresh?: boolean;
}

const Backgrounds: Record<'old' | 'new' | 'grid', ImageSourcePropType> = {
  old: require('@assets/background.png'),
  new: require('@assets/background_2.png'),
  grid: require('@assets/grid.png'),
};

export function ScreenContainer({
  name,
  children,
  background = 'old',
  disableBackground = false,
  scrollDisabled = false,
  tabScreen = false,
  enableKeyboardScrollView = false,
  style,
  edges,
  onRefresh,
  refresh = false,
  contentContainerStyle,
}: ScreenContainerProps) {
  const HEADER_HEIGHT = useGetHeaderHeight();
  const TAB_BAR_HEIGHT = useGetTabBarHeight();
  const isKeyboardVisible = useKeyboardVisible();
  const {backgroundColor} = style ? StyleSheet.flatten(style) : {};
  const insets = useSafeAreaInsets();

  const ContainerView = enableKeyboardScrollView
    ? KeyboardAwareScrollView
    : View;

  const containerStyle = {
    paddingTop: tabScreen ? HEADER_HEIGHT : 0,
    paddingBottom: tabScreen
      ? TAB_BAR_HEIGHT
      : isKeyboardVisible && enableKeyboardScrollView
        ? 0
        : insets.bottom,
    flexGrow: 1,
  };

  useEffect(() => {
    Tracker.track('ScreenView', {screen_name: name});
  }, [name]);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: backgroundColor ?? Colors.Black,
      }}
      edges={
        edges ?? (tabScreen ? ['left', 'right'] : ['top', 'left', 'right'])
      }>
      <ImageBackground
        source={disableBackground ? undefined : Backgrounds[background]}
        style={[{flex: 1}, style]}>
        <ContainerView
          keyboardDismissMode="on-drag"
          refreshControl={
            onRefresh ? (
              <RefreshControl refreshing={refresh} onRefresh={onRefresh} />
            ) : undefined
          }
          style={
            enableKeyboardScrollView || tabScreen ? {flex: 1} : containerStyle
          }
          scrollEnabled={!scrollDisabled}
          contentContainerStyle={[containerStyle, contentContainerStyle]}
          keyboardShouldPersistTaps={'handled'}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled>
          {children}
        </ContainerView>
      </ImageBackground>
    </SafeAreaView>
  );
}
