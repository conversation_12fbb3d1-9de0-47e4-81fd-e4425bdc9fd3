import React from 'react';
import {Pressable} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import Dimension from '@/app/utils/dimension';

export function DevToolButton() {
  const offset = useSharedValue({x: 0, y: 0});
  const animated = useSharedValue({x: 0, y: 0});

  const gesture = Gesture.Pan()
    .onBegin(() => {
      offset.value.x = animated.value.x;
      offset.value.y = animated.value.y;
    })
    .onUpdate(({translationX, translationY}) => {
      animated.value = {
        x: offset.value.x + translationX,
        y: offset.value.y + translationY,
      };
    })
    .onEnd(() => {
      if (animated.value.x > Dimension.SCREEN_WIDTH / 2) {
        animated.value = withSpring({
          x: Dimension.SCREEN_WIDTH - 50,
          y: animated.value.y,
        });
      } else {
        animated.value = withSpring({x: 50, y: animated.value.y});
      }
    });

  const onPress = () => {
    if (
      NavigationService.navigationRef.getCurrentRoute()?.name ===
      ScreenNames.Network
    ) {
      NavigationService.goBack();
    } else {
      NavigationService.navigate(ScreenNames.Network);
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {translateX: animated.value.x},
        {translateY: animated.value.y},
      ],
    };
  });

  if (!__DEV__) {
    return null;
  }

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          right: 50,
          bottom: 50,
          backgroundColor: Colors.Teal,
          width: 65,
          height: 65,
          borderRadius: 80,
          justifyContent: 'center',
          alignItems: 'center',
        },
        animatedStyle,
      ]}>
      <GestureDetector gesture={gesture}>
        <Pressable onPress={onPress}>
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 20,
              lineHeight: 16,
              color: Colors.Black,
              textAlign: 'center',
            }}>
            Dev Menu
          </UixText>
        </Pressable>
      </GestureDetector>
    </Animated.View>
  );
}
