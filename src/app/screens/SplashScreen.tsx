import {Image, View} from 'react-native';

import {ScreenContainer} from './components/ScreenContainer';

import {ScreenNames} from '@/app/navigation/constants';

export function SplashScreen() {
  return (
    <ScreenContainer
      name={ScreenNames.Splash}
      edges={[]}
      contentContainerStyle={{paddingBottom: 0}}>
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          flex: 1,
        }}>
        <Image
          source={require('@assets/app_logo.png')}
          style={{width: 60, height: 24, resizeMode: 'cover'}}
        />
      </View>
    </ScreenContainer>
  );
}
