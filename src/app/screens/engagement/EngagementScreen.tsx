import {RootState} from '@store/store';
import React, {useEffect} from 'react';
import {useState} from 'react';
import {Pressable, View} from 'react-native';
import {useSelector} from 'react-redux';

import {ScreenNames} from '../../navigation/constants';
import {NavigationService} from '../../navigation/NavigationService';
import {UixButton} from '../../ui/UixButton';
import {UixText} from '../../ui/UixText';
import {ScreenContainer} from '../components/ScreenContainer';
import {TimelogCounter} from '../home/<USER>/TimelogCounter';
import {Engagement} from './constants';
import {DaysSelector} from './DaysSelector';
import {RadioCheckedIcon, RadioUncheckedIcon} from './icons';

import {invalidateQueries} from '@/app/apollo/cacheUtils';
import {client} from '@/app/apollo/client';
import useUixMutation from '@/app/hooks/useUixMutation';
import useUixQuery from '@/app/hooks/useUixQuery';
import {Colors} from '@/app/theme/colors';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {GetMemberRetainer, UpdateMemberRetainer} from '@/graphQL/member';
import {
  GetMemberRetainerQuery,
  UpdateMemberRetainerMutation,
  UpdateMemberRetainerMutationVariables,
} from '@/types/__generated__/graphql';

export function EngagementScreen() {
  const userDetails = useSelector((store: RootState) => store.user);

  const {
    data,
    loading: queryLoading,
    refetch,
  } = useUixQuery<GetMemberRetainerQuery>(GetMemberRetainer, {
    variables: {
      member_id: userDetails.id,
    },
  });

  const [engagement, setEngagement] = useState<Engagement>();
  const [hours, setHours] = useState<number>(0);
  const [days, setDays] = useState<string[]>([]);

  useEffect(() => {
    if (
      data?.members_member_retainer &&
      data.members_member_retainer.length > 0
    ) {
      const retainer = data.members_member_retainer[0];
      setEngagement(
        retainer.retainer_type === 1 ? Engagement.Hourly : Engagement.Monthly,
      );
      setHours(retainer.hours || 0);
      setDays(retainer.days || []);
    }
  }, [data]);

  const updateCache = async (retainerType: number) => {
    await client.cache.updateQuery(
      {
        query: GetMemberRetainer,
        variables: {
          member_id: userDetails.id,
        },
      },
      data => {
        if (!data || !data.members_member_retainer.length) return data;

        return {
          ...data,
          members_member_retainer: [
            {
              ...data.members_member_retainer[0],
              id: data.members_member_retainer[0].id,
              hours: hours,
              days: days,
              retainer_type: retainerType,
              member_retainer_type: {
                name: retainerType === 1 ? 'Hourly' : 'Monthly',
                __typename: 'members_member_retainer_type',
              },
              __typename: 'members_member_retainer',
            },
          ],
        };
      },
    );
  };

  const [updateRetainer, {loading}] = useUixMutation<
    UpdateMemberRetainerMutation,
    UpdateMemberRetainerMutationVariables
  >(UpdateMemberRetainer);

  const handleSubmit = async () => {
    if (!engagement) return;
    try {
      const retainerType = engagement === Engagement.Hourly ? 1 : 2;
      await updateRetainer({
        variables: {
          member_id: userDetails.id,
          hours: hours,
          days: days,
          retainer_type: retainerType,
        },
        onCompleted: async () => {
          await updateCache(retainerType);
          invalidateQueries.all();
          NavigationService.goBack();
        },
      });
    } catch (error) {
      console.error('Error updating retainer:', error);
      refetch();
    }
  };

  const Header = ({title}: {title: string}) => (
    <UixText
      variant="dotsBold"
      capitalize
      style={{
        fontSize: 24,
        lineHeight: 18,
        marginBottom: 12,
        marginTop: 24,
        color: Colors.Teal700,
      }}>
      {title}
    </UixText>
  );

  const RetainerTile = ({e}: {e: Engagement}) => (
    <Pressable
      onPress={() => setEngagement(e)}
      disabled={queryLoading}
      style={{
        flex: 1,
        flexDirection: 'row',
        padding: 16,
        backgroundColor: Colors.Teal975,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
      <UixText
        style={{
          fontSize: 18,
          fontWeight: 'semibold',
          color:
            engagement === e && !queryLoading ? Colors.Yellow : Colors.White,
        }}>
        {e}
      </UixText>
      {engagement === e && !queryLoading ? (
        <RadioCheckedIcon />
      ) : (
        <RadioUncheckedIcon />
      )}
    </Pressable>
  );

  return (
    <ScreenContainer
      name={ScreenNames.Engagement}
      disableBackground
      style={{backgroundColor: Colors.DarkBackground, padding: 16}}
      enableKeyboardScrollView>
      <View>
        <UixButton
          label="Close"
          iconImage={require('@assets/cross.png')}
          onPress={() => NavigationService.goBack()}
          tintColor={'Teal'}
        />
      </View>
      <UixText
        variant="dotsBold"
        capitalize
        style={{
          fontSize: 32,
          lineHeight: 26,
          marginTop: 16,
          color: Colors.Teal,
        }}>
        Engagement
      </UixText>
      <UixText
        variant="titleMedium"
        style={{color: Colors.Teal700, marginBottom: 8}}>
        We’ll use this to match you with the right opportunities.
      </UixText>
      <Header title="Retainer type" />
      <View style={{flexDirection: 'row', gap: 12}}>
        <RetainerTile e={Engagement.Hourly} />
        <RetainerTile e={Engagement.Monthly} />
      </View>
      <Header title={'No of hours in a day'} />
      <TimelogCounter
        hours={hours}
        setHours={setHours}
        color={Colors.Teal975}
        limit={24}
        hourUnit={1}
        disabled={queryLoading}
      />
      <Header title={'Choose days'} />
      <DaysSelector days={days} setDays={setDays} disabled={queryLoading} />
      <RoundedButton
        label="Confirm"
        loading={loading}
        disabled={queryLoading || days.length === 0 || hours === 0}
        onPress={handleSubmit}
        style={{marginTop: 16}}
        center
      />
    </ScreenContainer>
  );
}
