import Svg, {ClipP<PERSON>, Defs, G, Path, Rect, SvgProps} from 'react-native-svg';

import {Colors} from '@/app/theme/colors';

export function CheckBoxEmptyIcon({
  width = 24,
  height = 24,
  color = Colors.White500,
}: {
  width?: number;
  height?: number;
  color?: string;
}) {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" fill="none">
      <Path
        d="M22.86 1.14H21.7125V0H2.2875V1.14H1.14V2.2875H0V21.7125H1.14V22.86H2.2875V24H21.7125V22.86H22.86V21.7125H24V2.2875H22.86V1.14ZM22.86 12.57H21.7125V20.5725H20.5725V21.7125H3.4275V20.5725H2.2875V12.57H1.14V11.43H2.2875V3.4275H3.4275V2.2875H20.5725V3.4275H21.7125V11.43H22.86V12.57Z"
        fill={color}
      />
    </Svg>
  );
}

export function CheckBoxSelectedIcon({
  width = 24,
  height = 24,
  color = Colors.Yellow,
}: {
  width?: number;
  height?: number;
  color?: string;
}) {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" fill="none">
      <G clipPath="url(#clip0_4018_21678)">
        <Path
          d="M22.86 1.14H21.7125V0H2.2875V1.14H1.14V2.2875H0V21.7125H1.14V22.86H2.2875V24H21.7125V22.86H22.86V21.7125H24V2.2875H22.86V1.14ZM22.86 12.57H21.7125V20.5725H20.5725V21.7125H3.4275V20.5725H2.2875V12.57H1.14V11.43H2.2875V3.4275H3.4275V2.2875H20.5725V3.4275H21.7125V11.43H22.86V12.57Z"
          fill={color}
        />
        <Path
          d="M11.3304 12.717H10V11.2924H8.66083V9.85848H7.33042V11.2924H6V12.717H7.33042V14.1415H8.66083V15.5754H10V17H11.3304V15.5754H12.6696V14.1415H14V12.717H15.3304V11.2924H16.6696V9.85848H18V8.43393H16.6696V7H15.3304V8.43393H14V9.85848H12.6696V11.2924H11.3304V12.717Z"
          fill={color}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_4018_21678">
          <Rect width="24" height="24" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export function RadioCheckedIcon({
  width = 24,
  height = 24,
  fill = Colors.Yellow,
  ...props
}: SvgProps & {fill?: string}) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      {...props}>
      <Path
        d="M1.14 17.145H2.28V19.425H3.4275V20.5725H4.5675V21.7125H6.855V22.86H9.1425V24H14.85V22.86H17.1375V21.7125H19.425V20.5725H20.565V19.425H21.7125V17.145H22.8525V14.8575H24V9.1425H22.8525V6.855H21.7125V4.5675H20.565V3.4275H19.425V2.2875H17.1375V1.14H14.85V0H9.1425V1.14H6.855V2.2875H4.5675V3.4275H3.4275V4.5675H2.28V6.855H1.14V9.1425H0V14.8575H1.14V17.145ZM3.4275 10.2825H4.5675V8.0025H5.7075V5.715H7.995V4.5675H10.2825V3.4275H13.71V4.5675H15.9975V5.715H18.285V8.0025H19.425V10.2825H20.565V13.7175H19.425V15.9975H18.285V18.285H15.9975V19.425H13.71V20.5725H10.2825V19.425H7.995V18.285H5.7075V15.9975H4.5675V13.7175H3.4275V10.2825Z"
        fill={fill}
      />
      <Path
        d="M8.38 13.715H8.76V14.475H9.1425V14.8575H9.5225V15.2375H10.285V15.62H11.0475V16H12.95V15.62H13.7125V15.2375H14.475V14.8575H14.855V14.475H15.2375V13.715H15.6175V12.9525H16V11.0475H15.6175V10.285H15.2375V9.5225H14.855V9.1425H14.475V8.7625H13.7125V8.38H12.95V8H11.0475V8.38H10.285V8.7625H9.5225V9.1425H9.1425V9.5225H8.76V10.285H8.38V11.0475H8V12.9525H8.38V13.715Z"
        fill={fill}
      />
    </Svg>
  );
}

export function RadioUncheckedIcon({
  width = 24,
  height = 24,
  fill = Colors.White,
  opacity = 0.3,
  ...props
}: SvgProps & {fill?: string; opacity?: number}) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      {...props}>
      <G opacity={opacity}>
        <Path
          d="M1.14 17.145H2.28V19.425H3.4275V20.5725H4.5675V21.7125H6.855V22.86H9.1425V24H14.85V22.86H17.1375V21.7125H19.425V20.5725H20.565V19.425H21.7125V17.145H22.8525V14.8575H24V9.1425H22.8525V6.855H21.7125V4.5675H20.565V3.4275H19.425V2.2875H17.1375V1.14H14.85V0H9.1425V1.14H6.855V2.2875H4.5675V3.4275H3.4275V4.5675H2.28V6.855H1.14V9.1425H0V14.8575H1.14V17.145ZM3.4275 10.2825H4.5675V8.0025H5.7075V5.715H7.995V4.5675H10.2825V3.4275H13.71V4.5675H15.9975V5.715H18.285V8.0025H19.425V10.2825H20.565V13.7175H19.425V15.9975H18.285V18.285H15.9975V19.425H13.71V20.5725H10.2825V19.425H7.995V18.285H5.7075V15.9975H4.5675V13.7175H3.4275V10.2825Z"
          fill={fill}
          fillOpacity={0.8}
        />
      </G>
    </Svg>
  );
}

export function HourGlassIcon({
  width = 20,
  height = 20,
  fill = Colors.Yellow,
  ...props
}: SvgProps & {fill?: string}) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      {...props}>
      <Path
        d="M12.3814 16.6719V17.6219H14.2814V18.5781H5.7127V17.6219H7.61895V16.6719H5.7127V14.7656H4.7627V18.5781H2.85645V19.5281H17.1439V18.5781H15.2377V14.7656H14.2814V16.6719H12.3814Z"
        fill={fill}
      />
      <Path d="M13.3311 12.8633H14.2811V14.7695H13.3311V12.8633Z" fill={fill} />
      <Path d="M13.3311 5.24219H14.2811V7.14844H13.3311V5.24219Z" fill={fill} />
      <Path d="M12.3818 11.9102H13.3318V12.8602H12.3818V11.9102Z" fill={fill} />
      <Path d="M11.4248 10.9648H12.3811V11.9148H11.4248V10.9648Z" fill={fill} />
      <Path d="M10.4746 15.7188H12.3809V16.675H10.4746V15.7188Z" fill={fill} />
      <Path
        d="M12.3813 9.05469V8.09844H13.3313V7.14844H6.6626V8.09844H7.61885V9.05469H8.56885V10.9609H9.5251V15.7172H10.4751V10.9609H11.4251V9.05469H12.3813Z"
        fill={fill}
      />
      <Path d="M7.61816 15.7188H9.52441V16.675H7.61816V15.7188Z" fill={fill} />
      <Path d="M7.61816 10.9648H8.56816V11.9148H7.61816V10.9648Z" fill={fill} />
      <Path d="M6.6626 11.9102H7.61885V12.8602H6.6626V11.9102Z" fill={fill} />
      <Path d="M5.71289 12.8633H6.66289V14.7695H5.71289V12.8633Z" fill={fill} />
      <Path d="M5.71289 5.24219H6.66289V7.14844H5.71289V5.24219Z" fill={fill} />
      <Path
        d="M5.7127 1.43672H14.2814V5.24297H15.2377V1.43672H17.1439V0.480469H2.85645V1.43672H4.7627V5.24297H5.7127V1.43672Z"
        fill={fill}
      />
    </Svg>
  );
}

export function CalendarIcon({
  width = 21,
  height = 20,
  fill = Colors.Yellow,
  ...props
}: SvgProps & {fill?: string}) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill="none"
      {...props}>
      <G clipPath="url(#clip0_calendar)">
        <Path
          d="M1.00394 4.99216H18.9961V18.9948H20V1.99347H18.9961V0.996094H15.9974V2.99741H15V0.996094H5V2.99741H4.00262V0.996094H1.00394V1.99347H0V18.9948H1.00394V4.99216Z"
          fill={fill}
        />
        <Path
          d="M1.00342 19.0039H18.9955V20.0013H1.00342V19.0039Z"
          fill={fill}
        />
        <Path d="M15 -1H15.9974V1.00131H15V-1Z" fill={fill} />
        <Path
          d="M10.9985 9V12.9961H13.9972V14.9974H10.9985V15.9948H15.0012V11.9987H12.0025V9.99737H15.0012V9H10.9985Z"
          fill={fill}
        />
        <Path
          d="M8.00015 9H7.00278V9.99737H5.00146V11.0013H7.00278V14.9974H5.00146V15.9948H10.0015V14.9974H8.00015V9Z"
          fill={fill}
        />
        <Path d="M4.00391 -1H5.00128V1.00131H4.00391V-1Z" fill={fill} />
      </G>
      <Defs>
        <ClipPath id="clip0_calendar">
          <Rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.5)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
