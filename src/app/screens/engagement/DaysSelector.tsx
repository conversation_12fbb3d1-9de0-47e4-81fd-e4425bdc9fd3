import {Pressable, View} from 'react-native';

import {ALL_DAYS} from './constants';
import {CheckBoxEmptyIcon, CheckBoxSelectedIcon} from './icons';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

export function DaysSelector({
  days,
  setDays,
  disabled = false,
}: {
  days: string[];
  setDays: (days: string[]) => void;
  disabled?: boolean;
}) {
  return (
    <View style={{gap: 12}}>
      {ALL_DAYS.map(day => {
        const isSelected = days.includes(day);
        return (
          <Pressable
            key={day}
            onPress={() => {
              if (isSelected) {
                setDays(days.filter(d => d !== day));
              } else {
                setDays([...days, day]);
              }
            }}
            disabled={disabled}
            style={{
              flexDirection: 'row',
              gap: 16,
              padding: 16,
              borderRadius: 8,
              alignItems: 'center',
              backgroundColor: isSelected ? Colors.Yellow1000 : Colors.Teal975,
            }}>
            {isSelected ? <CheckBoxSelectedIcon /> : <CheckBoxEmptyIcon />}
            <UixText
              variant="dotsBold"
              capitalize
              style={{
                fontSize: 24,
                lineHeight: 18,
                opacity: disabled ? 0.3 : 1,
                color: isSelected ? Colors.Yellow : Colors.White,
              }}>
              {day}
            </UixText>
          </Pressable>
        );
      })}
    </View>
  );
}
