import {RootState} from '@store/store';
import React, {useEffect, useState} from 'react';
import {
  Image,
  Keyboard,
  NativeSyntheticEvent,
  TextInputChangeEventData,
  View,
} from 'react-native';
import {Text} from 'react-native-paper';
import {useSelector} from 'react-redux';

import ReferFormSuccess from './ReferFormSuccess';

import useDisclosure from '@/app/hooks/useDisclosure';
import useUixMutation from '@/app/hooks/useUixMutation';
import {Colors} from '@/app/theme/colors';
import {FullScreenModal} from '@/app/ui/FullScreenModal';
import {LinkedinSelector} from '@/app/ui/linkedInSelector/LinkedInSelector';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {UixTextInput} from '@/app/ui/UixTextInput';
import {sendLeadAcknowledgementEmail} from '@/app/utils/email-integration';
import {notifyNewProjectReferral} from '@/app/utils/slackNotifications';
import {createProjectReferral} from '@/graphQL/referrals';
import {
  CreateProjectReferralMutation,
  CreateProjectReferralMutationVariables,
} from '@/types/__generated__/graphql';

function ReferProjectForm({
  modalDisclosure,
}: {
  modalDisclosure: {
    opened: boolean;
    controls: {
      open: () => void;
      close: () => void;
      toggle: () => void;
    };
  };
}) {
  const icon = require('@assets/diamond.png');
  const userDetails = useSelector((store: RootState) => store.user);
  const [submitDisabled, setSubmitDisabled] = useState(false);
  const [errors, setErrors] = useState<Record<string, boolean>>({
    name: false,
    description: false,
    linkedin_url: false,
    phone_number: false,
  });

  const successScreenDisclosure = useDisclosure();

  const [mutation, {loading}] = useUixMutation<
    CreateProjectReferralMutation,
    CreateProjectReferralMutationVariables
  >(createProjectReferral);

  const [form, setForm] = useState({
    name: '',
    description: '',
    linkedin_url: '',
    phone_number: '',
  });

  async function onSubmit() {
    Keyboard.dismiss();

    if (loading) {
      return;
    }

    if (
      !form.name ||
      !form.description ||
      !form.linkedin_url ||
      !form.phone_number
    ) {
      setSubmitDisabled(true);
      return;
    }

    await mutation({
      variables: {
        ...form,
        first_name: form.name.split(' ')[0],
        last_name: form.name.split(' ').filter(Boolean).slice(1).join(' '),
        phone_number: `+91${form.phone_number}`,
        member_id: userDetails.id,
      },
      onCompleted: data => {
        if (data?.insert_leads_lead_details_one?.id) {
          notifyNewProjectReferral(data.insert_leads_lead_details_one.id);

          sendLeadAcknowledgementEmail({
            id: data?.insert_leads_lead_details_one?.id,
          });
        }

        setForm({
          name: '',
          description: '',
          linkedin_url: '',
          phone_number: '',
        });
        successScreenDisclosure.controls.open();
      },
    });
  }

  useEffect(() => {
    setErrors({
      phone_number: !form.phone_number && submitDisabled,
      linkedin_url: !form.linkedin_url && submitDisabled,
      description: !form.description && submitDisabled,
      name: !form.name && submitDisabled,
    });
    if (
      form.name &&
      form.description &&
      form.linkedin_url &&
      form.phone_number
    ) {
      setSubmitDisabled(false);
    }
  }, [form, submitDisabled]);

  return (
    <FullScreenModal
      isOpen={modalDisclosure.opened}
      onClose={() => {
        setSubmitDisabled(false);
        modalDisclosure.controls.close();
      }}>
      {successScreenDisclosure.opened ? (
        <ReferFormSuccess
          onClose={() => {
            setSubmitDisabled(false);
            successScreenDisclosure.controls.close();
            modalDisclosure.controls.close();
          }}
          icon={icon}
          baseColor={'Teal'}
          text={`THANK YOU\nFOR\nREFERRING`}
        />
      ) : (
        <View style={{flexGrow: 1}}>
          <View style={{padding: 16, flexGrow: 1, gap: 10}}>
            <UixButton
              label="Close"
              iconImage={require('@assets/cross.png')}
              onPress={() => {
                setSubmitDisabled(false);
                modalDisclosure.controls.close();
              }}
              tintColor={'Teal'}
            />
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginTop: 20,
              }}>
              <UixText
                variant="dotsBold"
                style={{fontSize: 32, lineHeight: 32, color: Colors.Teal}}
                capitalize>{`REFER\nNEW PROJECT`}</UixText>
              <Image source={icon} style={{width: 40, height: 40}} />
            </View>
            <View>
              <UixText
                variant="titleMedium"
                style={{
                  fontWeight: '500',
                  color: Colors.Teal700,
                }}>
                Become a Lead Hunter
              </UixText>
              <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
                <Text
                  variant="titleMedium"
                  style={{
                    fontWeight: '500',
                    color: Colors.Teal700,
                  }}>
                  and earn
                  <Text
                    variant="titleMedium"
                    style={{
                      fontWeight: '500',
                      color: Colors.Yellow,
                    }}>
                    {'  5% revenue  '}
                  </Text>
                  for referring a project.
                </Text>
              </View>
            </View>
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 16,
                marginTop: 20,
                color: Colors.Teal,
              }}
              capitalize>{`POC FULL NAME`}</UixText>
            <UixTextInput
              autoFocus
              mode="outlined"
              placeholder="John Doe"
              textContentType="name"
              outlineColor={errors.name ? Colors.Error : Colors.Teal950}
              activeOutlineColor={errors.name ? Colors.Error : Colors.Teal}
              textColor={Colors.Teal}
              value={form.name}
              onChange={({
                nativeEvent: {text},
              }: NativeSyntheticEvent<TextInputChangeEventData>) => {
                setForm({...form, name: text});
              }}
              placeholderTextColor={Colors.Teal800}
              style={{backgroundColor: Colors.Teal950}}
            />
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 16,
                marginTop: 20,
                color: Colors.Teal,
              }}
              capitalize>
              {'Phone Number'}
            </UixText>
            <UixTextInput
              mode="outlined"
              placeholder="XXX-XXX-XXXX"
              textContentType="telephoneNumber"
              outlineColor={errors.phone_number ? Colors.Error : Colors.Teal950}
              activeOutlineColor={
                errors.phone_number ? Colors.Error : Colors.Teal
              }
              keyboardType="numeric"
              inputMode="numeric"
              maxLength={10}
              textColor={Colors.Teal}
              value={form.phone_number}
              onChange={({
                nativeEvent: {text},
              }: NativeSyntheticEvent<TextInputChangeEventData>) => {
                setForm({...form, phone_number: text});
              }}
              placeholderTextColor={Colors.Teal800}
              style={{backgroundColor: Colors.Teal950}}
            />
            <LinkedinSelector
              value={form.name}
              onChange={(text: string) => {
                setForm({...form, linkedin_url: text});
              }}
              color="Teal"
              errored={errors.linkedin_url}
            />
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 16,
                marginTop: 20,
                color: Colors.Teal,
              }}
              capitalize>{`Describe Project in few words`}</UixText>
            <UixTextInput
              mode="outlined"
              placeholder="This project is a game-changer in technology, set to transform the industry. Let’s dive in!"
              outlineColor={errors.description ? Colors.Error : Colors.Teal950}
              activeOutlineColor={
                errors.description ? Colors.Error : Colors.Teal
              }
              textColor={Colors.Teal}
              value={form.description}
              onChange={({
                nativeEvent: {text},
              }: NativeSyntheticEvent<TextInputChangeEventData>) => {
                setForm({...form, description: text});
              }}
              multiline={true}
              placeholderTextColor={Colors.Teal800}
              style={{
                backgroundColor: Colors.Teal950,
                paddingVertical: 16,
              }}
            />
          </View>
          {!successScreenDisclosure.opened && (
            <RoundedButton
              label="Submit"
              disabled={submitDisabled}
              loading={loading}
              onPress={onSubmit}
              style={{padding: 16}}
              center
            />
          )}
        </View>
      )}
    </FullScreenModal>
  );
}

export default ReferProjectForm;
