import ChevronDown from '@assets/chevron-down.svg';
import dayjs from 'dayjs';
import React, {useState} from 'react';
import {Image, Modal, TouchableOpacity, View} from 'react-native';
import {TimerPickerModal} from 'react-native-timer-picker';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

const WEEKDAYS = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];

const ReminderView = () => {
  const [showPicker, setShowPicker] = useState(false);
  const [showWeekdayModal, setShowWeekdayModal] = useState(false);
  const [selectedHour, setSelectedHour] = useState(0);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [selectedWeekdays, setSelectedWeekdays] = useState<number[]>([]);
  const [tempSelectedWeekdays, setTempSelectedWeekdays] = useState<number[]>(
    [],
  );

  const handleTimeConfirm = ({
    hours,
    minutes,
  }: {
    hours: number;
    minutes: number;
    seconds: number;
  }) => {
    setSelectedHour(hours);
    setSelectedMinute(minutes);
    setShowPicker(false);
  };

  const handleWeekdaySelect = (index: number) => {
    setTempSelectedWeekdays(prev => {
      if (prev.includes(index)) {
        return prev.filter(i => i !== index);
      }
      return [...prev, index];
    });
  };

  const handleWeekdaySubmit = () => {
    setSelectedWeekdays(tempSelectedWeekdays);
    setShowWeekdayModal(false);
  };

  const handleOpenWeekdayModal = () => {
    setTempSelectedWeekdays(selectedWeekdays);
    setShowWeekdayModal(true);
  };

  const formattedTime = dayjs()
    .hour(selectedHour)
    .minute(selectedMinute)
    .format('hh:mm A');

  const formattedWeekdays =
    selectedWeekdays.length > 0
      ? selectedWeekdays.map(index => WEEKDAYS[index]).join(', ')
      : 'Select days';

  return (
    <>
      <View
        style={{
          borderWidth: 1,
          borderColor: 'white',
          padding: 16,
          gap: 12,
          backgroundColor: '#FFFFFF1A',
          borderRadius: 12,
        }}>
        <View
          style={{
            flexDirection: 'row',
          }}>
          <View
            style={{
              flex: 1,
              gap: 8,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <View
              style={{
                width: 24,
                height: 24,
                padding: 5,
              }}>
              <Image
                source={require('@assets/notification-bell.png')}
                style={{width: 15, height: 15}}
              />
            </View>
            <UixText variant="dotsBold" style={{fontSize: 20}}>
              UIX LABS
            </UixText>
          </View>
          <View style={{}}>
            <UixText variant="dotsBold" style={{fontSize: 12}}>
              Click to set
            </UixText>
          </View>
        </View>
        <View
          style={{
            backgroundColor: '#DAE9011A',
            flexDirection: 'row',
            padding: 8,
            borderTopLeftRadius: 5,
            borderTopRightRadius: 5,
          }}>
          <TouchableOpacity
            onPress={() => setShowPicker(true)}
            style={{
              flex: 1,
              flexDirection: 'row',
              gap: 4,
              alignItems: 'center',
            }}>
            <View
              style={{
                padding: 4,
              }}>
              <Image
                source={require('@assets/clock.png')}
                style={{width: 16, height: 16}}
              />
            </View>
            <UixText
              variant="dotsBold"
              style={{fontSize: 20, color: Colors.Yellow750, flex: 1}}>
              {formattedTime}
            </UixText>
            <ChevronDown />
          </TouchableOpacity>
        </View>
        <View
          style={{
            backgroundColor: '#DAE9011A',
            flexDirection: 'row',
            padding: 8,
            borderTopLeftRadius: 5,
            borderTopRightRadius: 5,
          }}>
          <TouchableOpacity
            onPress={handleOpenWeekdayModal}
            style={{
              flex: 1,
              flexDirection: 'row',
              gap: 4,
              alignItems: 'center',
            }}>
            <View
              style={{
                padding: 4,
              }}>
              <Image
                source={require('@assets/calendar-default.png')}
                style={{width: 16, height: 16}}
              />
            </View>
            <UixText style={{fontSize: 16, alignItems: 'center', flex: 1}}>
              {formattedWeekdays}
            </UixText>
            <ChevronDown />
          </TouchableOpacity>
        </View>
      </View>
      <TimerPickerModal
        visible={showPicker}
        setIsVisible={setShowPicker}
        onConfirm={handleTimeConfirm}
        modalTitle="Set Reminder"
        onCancel={() => setShowPicker(false)}
        closeOnOverlayPress
        styles={{
          theme: 'light',
        }}
        modalProps={{
          overlayOpacity: 0.2,
        }}
      />
      <Modal
        visible={showWeekdayModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowWeekdayModal(false)}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          activeOpacity={1}
          onPress={() => setShowWeekdayModal(false)}>
          <View
            style={{
              backgroundColor: 'black',
              padding: 20,
              borderRadius: 12,
              width: '80%',
            }}>
            <UixText
              variant="dotsBold"
              style={{fontSize: 18, marginBottom: 16, color: 'white'}}>
              Select Days
            </UixText>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-around',
                marginBottom: 20,
              }}>
              {WEEKDAYS.map((day, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => handleWeekdaySelect(index)}
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: tempSelectedWeekdays.includes(index)
                      ? Colors.Yellow150
                      : '#DAE9011A',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <UixText
                    style={{
                      color: tempSelectedWeekdays.includes(index)
                        ? 'black'
                        : 'white',
                    }}>
                    {day}
                  </UixText>
                </TouchableOpacity>
              ))}
            </View>
            <TouchableOpacity
              onPress={handleWeekdaySubmit}
              style={{
                backgroundColor: Colors.Yellow150,
                padding: 12,
                borderRadius: 8,
                alignItems: 'center',
              }}>
              <UixText
                style={{color: 'black', fontSize: 16, fontWeight: 'bold'}}>
                Confirm
              </UixText>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default ReminderView;
