import {ViewStyle} from 'react-native';
import {Path, Svg} from 'react-native-svg';

export function RightSvg({color, style}: {color?: string; style: ViewStyle}) {
  return (
    <Svg width="10" height="120" viewBox="0 0 10 120" fill="none" style={style}>
      <Path
        d="M7.50385 -1.6641L13 -9.90833V0V120V129.908L7.50385 121.664L3.50385 115.664L3 114.908V114V6V5.09167L3.50385 4.3359L7.50385 -1.6641Z"
        fill={color ? color : '#FFEB00'}
        stroke="black"
        stroke-width="6"
      />
    </Svg>
  );
}

export function LeftSvg({color, style}: {color?: string; style: ViewStyle}) {
  return (
    <Svg width="10" height="120" viewBox="0 0 10 120" fill="none" style={style}>
      <Path
        d="M2.49615 -1.6641L-3 -9.90833V0V120V129.908L2.49615 121.664L6.49615 115.664L7 114.908V114V6V5.09167L6.49615 4.3359L2.49615 -1.6641Z"
        fill={color ? color : '#FFEB00'}
        stroke="black"
        stroke-width="6"
      />
    </Svg>
  );
}
