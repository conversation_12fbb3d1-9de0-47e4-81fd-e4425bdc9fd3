import React from 'react';
import {Image, Linking, Pressable, View} from 'react-native';

import {NavigationService} from '@/app/navigation/NavigationService';
import {RootStackParamsList} from '@/app/navigation/types';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixCornerView} from '@/app/ui/UixCornerView';
import {UixText} from '@/app/ui/UixText';

export enum ActionType {
  'HTTP' = 'HTTP',
  'APP_SCREEN' = 'APP_SCREEN',
}

export default function CarouselTile({
  theme,
  title,
  subTitle,
  actionType,
  actionText,
  actionPayload,
}: {
  theme: string;
  title: string;
  subTitle: string;
  actionText: string;
  actionType: ActionType;
  actionPayload: string;
}) {
  return (
    <Pressable
      onPress={() => {
        switch (actionType) {
          case 'HTTP':
            return Linking.openURL(actionPayload);
          case 'APP_SCREEN':
            return NavigationService.navigate(
              actionPayload as keyof RootStackParamsList,
            );
        }
      }}>
      <View
        style={{
          height: 120,
          width: '100%',
          flexDirection: 'row',
          paddingTop: 12,
          paddingBottom: 16,
          paddingHorizontal: 24,
          position: 'relative',
          backgroundColor: updateColorWithOpacity(
            theme ? theme : Colors.Teal,
            0.1,
          ),
        }}>
        <View>
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 24,
              lineHeight: 20,
              paddingRight: 40,
              color: theme ? theme : Colors.Teal,
            }}
            numberOfLines={1}
            capitalize>
            {title}
          </UixText>
          <UixText
            variant="titleMedium"
            style={{
              marginTop: 8,
              paddingRight: 120,
              color: theme ? theme : Colors.Teal,
            }}
            numberOfLines={2}>
            {subTitle}
          </UixText>
        </View>
        {actionType && actionPayload && (
          <View
            style={{
              position: 'absolute',
              right: 12,
              bottom: 12,
              shadowColor: theme ? theme : Colors.Teal,
              shadowOpacity: 0.4,
              shadowRadius: 40,
              shadowOffset: {width: 0, height: 8},
              zIndex: 3,
              elevation: 12,
              paddingHorizontal: 12,
              borderRadius: 4,
            }}>
            <UixCornerView color={theme ? theme : Colors.Teal}>
              <View
                style={{
                  height: 36,
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                  gap: 4,
                  paddingRight: 12,
                  paddingLeft: 16,
                }}>
                <UixText
                  variant="dotsBold"
                  style={{fontSize: 20, lineHeight: 18, color: Colors.Black}}
                  capitalize>
                  {actionText ? actionText : 'View'}
                </UixText>
                <Image
                  source={require('@assets/rightArrow.png')}
                  style={{
                    width: 24,
                    height: 24,
                  }}
                />
              </View>
            </UixCornerView>
          </View>
        )}
      </View>
    </Pressable>
  );
}
