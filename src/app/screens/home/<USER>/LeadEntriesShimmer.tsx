import {View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {Shimmer} from '@/app/ui/Shimmer';

export const LeadEntriesListShimmer = () => {
  return (
    <View style={{gap: 24}}>
      {Array.from({length: 5}).map((_, index) => (
        <View key={index} style={{gap: 12}}>
          <LeadEntriesShimmer />
        </View>
      ))}
    </View>
  );
};

const LeadEntriesShimmer = () => {
  const {styles} = useStyles(styleSheet);

  return (
    <View style={styles.wrapper}>
      <View style={styles.innerContainer}>
        <View style={styles.container}>
          <View>
            <Shimmer />
            <Shimmer />
          </View>

          <Shimmer style={styles.rightArrow} />
        </View>

        <View style={styles.descriptionContainer}>
          <Shimmer style={styles.descriptionShimmer} />
          <Shimmer style={styles.descriptionShimmer} />
        </View>
      </View>
    </View>
  );
};

const styleSheet = createStyleSheet(() => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },

  innerContainer: {
    borderWidth: 1,
    borderTopColor: 'rgba(0, 102, 84, 0.4)',
    borderTopRightColor: 'rgba(0, 102, 84, 0.4)',
    borderBottomColor: 'rgba(6, 8, 8, 0.0)',
    padding: 20,
    backgroundColor: updateColorWithOpacity(Colors.Teal425, 0.08),
    gap: 12,
    borderRadius: 8,
  },

  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  imageContainer: {
    width: 38,
    height: 38,
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  hoursBlock: {
    width: 120,
    height: 38,
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  statusBlock: {
    width: 36,
    height: 38,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
  },

  rightArrow: {
    width: 24,
    height: 24,
    borderRadius: 4,
  },

  descriptionShimmer: {
    width: '50%',
    height: 50,
    borderRadius: 4,
  },

  descriptionContainer: {
    flexDirection: 'row',
    gap: 8,
  },
}));
