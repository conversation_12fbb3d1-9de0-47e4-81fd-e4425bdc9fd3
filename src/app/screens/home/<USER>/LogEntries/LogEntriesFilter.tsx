import dayjs from 'dayjs';
import React, {useMemo} from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import {useStyles} from 'react-native-unistyles';

import {styleSheet} from './LogEntries.styles';
import {DateRange} from './useLogEntriesFilter';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

interface FilterState {
  selectedStatus: string;
  selectedProjects: string[];
}

interface LogEntriesFilterProps {
  dateFilter?: DateRange | null;
  appliedFilters?: FilterState;
  onDateFilterPress?: () => void;
  onProjectFilterPress?: () => void;
  onClearDateFilter: () => void;
  onClearAllFilters?: () => void;
  hasActiveFilters?: boolean;
}

export const LogEntriesFilter = ({
  dateFilter,
  appliedFilters,
  onDateFilterPress,
  onProjectFilterPress,
  onClearDateFilter,
}: LogEntriesFilterProps) => {
  const {styles} = useStyles(styleSheet);

  const dateButtonText = useMemo(
    () =>
      dateFilter?.startDate && dateFilter?.endDate
        ? `${dayjs(dateFilter.startDate).format('DD MMM')} - ${dayjs(dateFilter.endDate).format('DD MMM')}`
        : 'Select Date',
    [dateFilter?.startDate, dateFilter?.endDate],
  );

  const hasProjectFilters =
    appliedFilters?.selectedStatus !== 'ALL' ||
    (appliedFilters?.selectedProjects.length ?? 0) > 0;

  return (
    <View style={styles.filterContainer}>
      <View style={styles.filterRow}>
        <TouchableOpacity
          style={[
            styles.dateFilterButton,
            dateFilter
              ? styles.dateFilterButtonActive
              : styles.dateFilterButtonInactive,
          ]}
          onPress={onDateFilterPress}>
          <View style={styles.dateFilterContent}>
            <Image
              source={
                dateFilter
                  ? require('@assets/calendar.png')
                  : require('@assets/calendar-default.png')
              }
              style={styles.icon}
            />
            <UixText
              variant="dotsBold"
              style={[
                styles.dateFilterText,
                dateFilter
                  ? styles.dateFilterTextActive
                  : styles.dateFilterTextInactive,
              ]}
              numberOfLines={2}
              capitalize>
              {dateButtonText}
            </UixText>
            {dateButtonText === 'Select Date' && (
              <Image
                source={require('@assets/chevron-down.png')}
                style={styles.icon}
                tintColor={Colors.Teal}
              />
            )}
          </View>
          {dateFilter && (
            <TouchableOpacity
              onPress={onClearDateFilter}
              style={styles.clearButton}>
              <Image
                source={require('@assets/cross.png')}
                style={styles.crossIcon}
              />
            </TouchableOpacity>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.projectFilterButton,
            hasProjectFilters
              ? styles.dateFilterButtonActive
              : styles.dateFilterButtonInactive,
          ]}
          onPress={onProjectFilterPress}>
          <View style={styles.filterButton}>
            <Image
              source={require('@assets/filter.png')}
              style={styles.icon}
              tintColor={hasProjectFilters ? 'black' : Colors.Teal400}
            />

            <Image
              source={require('@assets/chevron-down.png')}
              style={styles.icon}
              tintColor={hasProjectFilters ? 'black' : Colors.Teal400}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};
