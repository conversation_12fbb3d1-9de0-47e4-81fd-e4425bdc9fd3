import React from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Timelogs} from '@/types/__generated__/graphql';

interface IProps {
  data: Members_Member_Timelogs;
  onPress: () => void;
}

export const renderStatusImage = (status: string) => {
  const {styles} = useStyles(styleSheet);

  switch (status) {
    case 'PENDING':
      return (
        <Image
          source={require('@assets/pending.png')}
          style={styles.statusImageSmall}
        />
      );

    case 'APPROVED':
      return (
        <Image
          source={require('@assets/approved.png')}
          style={styles.statusImageLarge}
        />
      );

    case 'PAID':
      return (
        <Image
          source={require('@assets/paid.png')}
          style={styles.statusImageLarge}
        />
      );

    case 'REJECTED':
      return (
        <Image
          source={require('@assets/rejected.png')}
          style={styles.statusImageLarge}
        />
      );

    default:
      return (
        <Image
          source={require('@assets/pending.png')}
          style={styles.statusImageLarge}
        />
      );
  }
};

const LogEntriesView = ({data, onPress}: IProps) => {
  const {styles} = useStyles(styleSheet);

  return (
    <TouchableOpacity style={styles.wrapper} onPress={onPress}>
      <View style={styles.innerContainer}>
        <View style={styles.container}>
          <View style={styles.leftSection}>
            <View style={styles.imageContainer}>
              <Image
                source={{uri: data?.project_detail?.project_logo ?? ''}}
                style={styles.projectLogo}
              />
            </View>

            <View style={styles.hoursBlock}>
              <UixText
                variant="dotsBold"
                style={styles.hoursText}>{`${data?.no_of_hours} HRS`}</UixText>
            </View>

            <View style={styles.statusBlock}>
              {renderStatusImage(data?.status ?? 'PENDING')}
            </View>
          </View>

          <Image
            source={require('@assets/right-arrow.png')}
            tintColor={'white'}
            style={styles.rightArrow}
          />
        </View>

        <UixText style={styles.descriptionBlock}>
          {`${data?.work_description}`}
        </UixText>
      </View>
    </TouchableOpacity>
  );
};

export default LogEntriesView;

const styleSheet = createStyleSheet(() => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },

  innerContainer: {
    borderWidth: 1,
    borderTopColor: 'rgba(0, 102, 84, 0.4)',
    borderTopRightColor: 'rgba(0, 102, 84, 0.4)',
    borderBottomColor: 'rgba(6, 8, 8, 0.0)',
    padding: 16,
    backgroundColor: updateColorWithOpacity(Colors.Teal425, 0.12),
    gap: 12,
    borderRadius: 8,
  },

  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  leftSection: {
    flexDirection: 'row',
    gap: 8,
  },

  imageContainer: {
    padding: 6,
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  projectLogo: {
    width: 32,
    height: 32,
    resizeMode: 'contain',
  },

  hoursBlock: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  hoursText: {
    color: Colors.Yellow150,
    fontSize: 20,
  },

  statusBlock: {
    paddingHorizontal: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
  },

  statusImageSmall: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },

  statusImageLarge: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },

  rightArrow: {
    width: 24,
    height: 24,
  },

  descriptionBlock: {
    fontSize: 16,
    fontWeight: 'regular',
    color: 'white',
  },
}));
