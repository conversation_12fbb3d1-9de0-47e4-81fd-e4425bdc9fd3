import dayjs from 'dayjs';
import {useCallback, useMemo, useState} from 'react';
import {DateType} from 'react-native-ui-datepicker';

const DATE_FORMAT = 'YYYY-MM-DD';

export interface DateRange {
  startDate: DateType;
  endDate: DateType;
}

export interface FilterState {
  selectedStatus: string;
  selectedProjects: string[];
}

const useLogEntriesFilter = () => {
  const [dateFilter, setDateFilter] = useState<DateRange | null>(null);
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    selectedStatus: 'ALL',
    selectedProjects: [],
  });

  const applyDateFilter = useCallback(
    (startDate: DateType, endDate: DateType) => {
      setDateFilter({
        startDate,
        endDate,
      });
    },
    [],
  );

  const clearDateFilter = useCallback(() => {
    setDateFilter(null);
  }, []);

  const applyProjectStatusFilters = useCallback((filters: FilterState) => {
    setAppliedFilters(filters);
  }, []);

  const clearAllFilters = useCallback(() => {
    setDateFilter(null);
    setAppliedFilters({
      selectedStatus: 'ALL',
      selectedProjects: [],
    });
  }, []);

  const hasActiveFilters = useMemo(() => {
    const hasDateFilter = Boolean(dateFilter?.startDate && dateFilter?.endDate);
    const hasStatusFilter = appliedFilters.selectedStatus !== 'ALL';
    const hasProjectFilter = appliedFilters.selectedProjects.length > 0;

    return hasDateFilter || hasStatusFilter || hasProjectFilter;
  }, [dateFilter, appliedFilters]);

  // Convert filters to API parameters
  const filterParams = useMemo(
    () => ({
      startDate: dateFilter?.startDate
        ? dayjs(dateFilter.startDate).format(DATE_FORMAT)
        : null,
      endDate: dateFilter?.endDate
        ? dayjs(dateFilter.endDate).format(DATE_FORMAT)
        : null,
      status:
        appliedFilters.selectedStatus !== 'ALL'
          ? appliedFilters.selectedStatus
          : null,
      projectIds:
        appliedFilters.selectedProjects.length > 0
          ? appliedFilters.selectedProjects
          : null,
    }),
    [dateFilter, appliedFilters],
  );

  return {
    dateFilter,
    appliedFilters,
    applyDateFilter,
    clearDateFilter,
    applyProjectStatusFilters,
    clearAllFilters,
    hasActiveFilters,
    filterParams,
  };
};

export default useLogEntriesFilter;
