import {createStyleSheet} from 'react-native-unistyles';

import {Colors} from '@/app/theme/colors';

export const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },

  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 20,
    gap: 24,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 12,
    backgroundColor: 'black',
  },

  titleContainer: {
    gap: 6,
  },

  title: {
    fontSize: 32,
    lineHeight: 22,
    color: Colors.Teal250,
  },

  subtitle: {
    fontSize: 24,
    lineHeight: 22,
    color: Colors.Yellow150,
  },

  filterContainer: {
    gap: 12,
  },

  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },

  dateFilterButton: {
    flex: 1,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
  },

  dateFilterButtonActive: {
    backgroundColor: Colors.Teal400,
  },

  dateFilterButtonInactive: {
    backgroundColor: Colors.Teal1100,
  },

  dateFilterContent: {
    gap: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    flex: 1,
  },

  dateFilterText: {
    flex: 1,
  },

  dateFilterTextActive: {
    fontSize: 16,
    color: '#070908',
  },

  dateFilterTextInactive: {
    fontSize: 18,
    color: Colors.Teal400,
  },

  projectFilterButton: {
    padding: 16,
    flexDirection: 'row',
    borderRadius: 8,
    gap: 4,
  },

  projectFilterButtonActive: {
    backgroundColor: Colors.Teal400,
  },

  projectFilterButtonInactive: {
    backgroundColor: Colors.Teal1100,
  },

  listContent: {
    gap: 24,
    paddingBottom: 100,
  },

  headerItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  headerText: {
    fontSize: 20,
    color: 'white',
  },

  bottomContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    bottom: 20,
    zIndex: 20,
  },

  bottomContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },

  reminderButton: {
    borderWidth: 1.5,
    borderColor: Colors.Yellow150,
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'black',
  },

  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },

  loadingContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },

  icon: {
    width: 24,
    height: 24,
  },

  clearButton: {
    padding: 4,
    marginLeft: 8,
  },

  crossIcon: {
    width: 16,
    height: 16,
    tintColor: 'black',
  },

  closeButton: {
    marginBottom: 24,
    height: 40,
  },

  timelogIcon: {
    width: 40,
    height: 40,
  },

  shimmerContainer: {
    flex: 1,
    paddingBottom: 100,
  },

  notFoundWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
  },

  notFoundImage: {
    width: 64,
    height: 64,
  },

  notFoundTextWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
  },

  noTimeLogFound: {
    color: Colors.Teal,
    fontSize: 24,
  },

  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
}));
