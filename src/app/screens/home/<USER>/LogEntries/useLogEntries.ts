import {getTimelogs} from '@graphQL/timelog';
import {AppDispatch, RootState} from '@store/store';
import {useCallback, useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';

import {
  GetTimelogsQuery,
  Members_Member_Timelogs,
} from './../../../../../types/__generated__/graphql';
import {
  appendLogEntries,
  resetLogEntries,
  setLogEntries,
  startInitialLoading,
  startLoading,
  stopLoading,
} from './log-entries-slice/LogEntriesSlice';

import useUixQuery from '@/app/hooks/useUixQuery';

export type GroupedTimelogs = {
  name: string;
  totalHours: number;
  items: Members_Member_Timelogs[];
};

export type FlatListHeader = {type: 'header'; date: string; totalHours: number};
export type FlatListLog = {type: 'log'; log: Members_Member_Timelogs};
export type FlatListItem = FlatListHeader | FlatListLog;

export interface UseLogEntriesProps {
  startDate?: string | null;
  endDate?: string | null;
  status?: string | null;
  projectIds?: string[] | null;
}

function groupTimelogsByDate(
  logs: Members_Member_Timelogs[],
): GroupedTimelogs[] {
  const grouped = logs.reduce(
    (acc, log) => {
      const {actual_date, no_of_hours = 0} = log;

      if (!acc[actual_date]) {
        acc[actual_date] = {
          name: actual_date,
          items: [],
          totalHours: 0,
        };
      }

      acc[actual_date].items.push(log);
      acc[actual_date].totalHours += no_of_hours;

      return acc;
    },
    {} as Record<string, GroupedTimelogs>,
  );

  const test = Object.values(grouped).sort(
    (a, b) => new Date(b.name).getTime() - new Date(a.name).getTime(),
  );

  return test;
}

const useLogEntries = (filters: UseLogEntriesProps = {}) => {
  const dispatch = useDispatch<AppDispatch>();
  const userDetails = useSelector((state: RootState) => state.user);
  const logEntriesState = useSelector((state: RootState) => state.logEntries);

  const {
    data,
    loading: queryLoading,
    refetch,
  } = useUixQuery<GetTimelogsQuery>(getTimelogs, {
    variables: {
      id: userDetails.id,
      limit: 20,
      offset: 0,
      startDate: filters.startDate,
      endDate: filters.endDate,
      status: filters.status,
      projectIds: filters.projectIds,
    },
    fetchPolicy: 'cache-and-network', // Get cached data first, then network data
    notifyOnNetworkStatusChange: true,
  });

  useEffect(() => {
    dispatch(resetLogEntries());
  }, [
    filters.startDate,
    filters.endDate,
    filters.status,
    filters.projectIds,
    dispatch,
  ]);

  useEffect(() => {
    if (queryLoading && logEntriesState.allLogs.length === 0) {
      dispatch(startInitialLoading());
    }
  }, [queryLoading, logEntriesState.allLogs.length, dispatch]);

  useEffect(() => {
    if (data?.members_member_timelogs && logEntriesState.allLogs.length === 0) {
      const initialLogs =
        data.members_member_timelogs as Members_Member_Timelogs[];
      dispatch(setLogEntries(initialLogs));
    }
  }, [data, dispatch, logEntriesState.allLogs.length, setLogEntries]);

  const groupedLogEntries = groupTimelogsByDate(logEntriesState.allLogs);

  const handleEndReached = useCallback(async () => {
    if (logEntriesState.loading || !logEntriesState.hasMore || queryLoading) {
      return;
    }

    dispatch(startLoading());

    try {
      const {data: moreData} = await refetch({
        id: userDetails.id,
        limit: 20,
        offset: logEntriesState.offset,
        startDate: filters.startDate,
        endDate: filters.endDate,
        status: filters.status,
        projectIds: filters.projectIds,
      });

      const newLogs = (moreData?.members_member_timelogs ??
        []) as Members_Member_Timelogs[];
      dispatch(
        appendLogEntries({
          logs: newLogs,
          hasMore: newLogs.length === 20,
        }),
      );
    } catch (error) {
      console.error('Failed to load more logs:', error);
      dispatch(stopLoading());
    }
  }, [
    dispatch,
    userDetails.id,
    logEntriesState.offset,
    logEntriesState.loading,
    logEntriesState.hasMore,
    queryLoading,
    refetch,
    filters.startDate,
    filters.endDate,
    filters.status,
    filters.projectIds,
  ]);

  const refetchData = useCallback(async () => {
    try {
      const {data: refreshedData} = await refetch({
        id: userDetails.id,
        limit: 20,
        offset: 0,
        startDate: filters.startDate,
        endDate: filters.endDate,
        status: filters.status,
        projectIds: filters.projectIds,
      });

      if (refreshedData?.members_member_timelogs) {
        const refreshedLogs =
          refreshedData.members_member_timelogs as Members_Member_Timelogs[];
        dispatch(setLogEntries(refreshedLogs));
      }
    } catch (error) {
      console.error('Failed to refresh data:', error);
    }
  }, [
    refetch,
    userDetails.id,
    filters.startDate,
    filters.endDate,
    filters.status,
    filters.projectIds,
    dispatch,
  ]);

  const refetchDataAfterUpdate = useCallback(async () => {
    try {
      // Get the current number of items to maintain pagination state
      const currentLimit = logEntriesState.allLogs.length || 20;

      const {data: refreshedData} = await refetch({
        id: userDetails.id,
        limit: currentLimit, // Maintain current pagination - fetch all currently loaded items
        offset: 0,
        startDate: filters.startDate,
        endDate: filters.endDate,
        status: filters.status,
        projectIds: filters.projectIds,
      });

      if (refreshedData?.members_member_timelogs) {
        const refreshedLogs =
          refreshedData.members_member_timelogs as Members_Member_Timelogs[];
        // Directly update with fresh data - no reset to maintain smooth UX
        dispatch(setLogEntries(refreshedLogs));
      }
    } catch (error) {
      console.error('Failed to refresh data after update:', error);
    }
  }, [
    refetch,
    userDetails.id,
    logEntriesState.allLogs.length,
    filters.startDate,
    filters.endDate,
    filters.status,
    filters.projectIds,
    dispatch,
  ]);

  // Separate function for when new entries are added (like from TimelogBottomModal)
  const refetchDataAfterNewEntry = useCallback(async () => {
    try {
      // For new entries, we might need to fetch one more page to see the new entry
      const currentLimit = Math.max(logEntriesState.allLogs.length + 5, 20);

      const {data: refreshedData} = await refetch({
        id: userDetails.id,
        limit: currentLimit, // Fetch a bit more to ensure new entry is included
        offset: 0,
        startDate: filters.startDate,
        endDate: filters.endDate,
        status: filters.status,
        projectIds: filters.projectIds,
      });

      if (refreshedData?.members_member_timelogs) {
        const refreshedLogs =
          refreshedData.members_member_timelogs as Members_Member_Timelogs[];
        dispatch(setLogEntries(refreshedLogs));
      }
    } catch (error) {
      console.error('Failed to refresh data after new entry:', error);
    }
  }, [
    refetch,
    userDetails.id,
    logEntriesState.allLogs.length,
    filters.startDate,
    filters.endDate,
    filters.status,
    filters.projectIds,
    dispatch,
  ]);

  return {
    groupedLogEntries,
    handleEndReached,
    loading: logEntriesState.loading,
    isInitialLoading:
      logEntriesState.initialLoading ||
      (queryLoading && logEntriesState.allLogs.length === 0),
    hasMoreData: logEntriesState.hasMore,
    refetchData,
    refetchDataAfterUpdate,
    refetchDataAfterNewEntry,
    allLogs: logEntriesState.allLogs,
    offset: logEntriesState.offset,
  };
};

export default useLogEntries;
