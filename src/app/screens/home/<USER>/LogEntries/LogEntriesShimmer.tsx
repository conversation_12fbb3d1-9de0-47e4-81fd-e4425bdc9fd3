import React from 'react';
import {View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {Shimmer} from '@/app/ui/Shimmer';

const LogEntriesShimmer = () => {
  const {styles} = useStyles(styleSheet);

  return (
    <View style={styles.wrapper}>
      <View style={styles.innerContainer}>
        <View style={styles.container}>
          <View style={styles.leftSection}>
            <View style={styles.imageContainer} />

            <View style={styles.hoursBlock} />

            <View style={styles.statusBlock} />
          </View>

          <Shimmer style={styles.rightArrow} />
        </View>

        <Shimmer style={styles.descriptionShimmer} />
        <Shimmer style={styles.descriptionShimmer} />
      </View>
    </View>
  );
};

export const LogEntriesListShimmer = () => {
  return (
    <View style={{gap: 24}}>
      <View style={{flexDirection: 'row', height: 50, gap: 12}}>
        <Shimmer style={{flex: 1, height: 50, borderRadius: 8}} />
        <Shimmer style={{width: 80, height: 50, borderRadius: 8}} />
      </View>

      {Array.from({length: 5}).map((_, index) => (
        <View key={index} style={{gap: 12}}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Shimmer style={{width: 120, height: 20, borderRadius: 4}} />
            <Shimmer style={{width: 80, height: 20, borderRadius: 4}} />
          </View>

          {/* Log entry shimmer */}
          <LogEntriesShimmer />
        </View>
      ))}
    </View>
  );
};

export default LogEntriesShimmer;

const styleSheet = createStyleSheet(() => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },

  innerContainer: {
    borderWidth: 1,
    borderTopColor: 'rgba(0, 102, 84, 0.4)',
    borderTopRightColor: 'rgba(0, 102, 84, 0.4)',
    borderBottomColor: 'rgba(6, 8, 8, 0.0)',
    padding: 16,
    backgroundColor: updateColorWithOpacity(Colors.Teal425, 0.12),
    gap: 12,
    borderRadius: 8,
  },

  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  leftSection: {
    flexDirection: 'row',
    gap: 8,
  },

  imageContainer: {
    width: 38,
    height: 38,
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  hoursBlock: {
    width: 120,
    height: 38,
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },

  statusBlock: {
    width: 36,
    height: 38,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(218, 233, 1, 0.12)',
  },

  rightArrow: {
    width: 24,
    height: 24,
    borderRadius: 4,
  },

  descriptionShimmer: {
    width: '80%',
    height: 16,
    borderRadius: 4,
  },
}));
