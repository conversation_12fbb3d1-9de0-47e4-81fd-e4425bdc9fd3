import {createSlice, PayloadAction} from '@reduxjs/toolkit';

import {Members_Member_Timelogs} from '@/types/__generated__/graphql';

interface LogEntriesState {
  allLogs: Members_Member_Timelogs[];
  offset: number;
  hasMore: boolean;
  loading: boolean;
  initialLoading: boolean;
  totalLoggedTime: number;
  previousOffset: number; // Store offset before applying filters
}

const initialState: LogEntriesState = {
  allLogs: [],
  offset: 0,
  hasMore: true,
  loading: false,
  initialLoading: false,
  totalLoggedTime: 0,
  previousOffset: 0,
};

interface AppendLogEntriesPayload {
  logs: Members_Member_Timelogs[];
  hasMore: boolean;
}

const logEntriesSlice = createSlice({
  name: 'logEntries',
  initialState,
  reducers: {
    setLogEntries: (
      state,
      action: PayloadAction<Members_Member_Timelogs[]>,
    ) => {
      state.allLogs = action.payload;
      state.offset = action.payload.length;
      state.hasMore = action.payload.length >= 20;
      state.loading = false;
      state.initialLoading = false;
    },

    appendLogEntries: (
      state,
      action: PayloadAction<AppendLogEntriesPayload>,
    ) => {
      state.allLogs = [...state.allLogs, ...action.payload.logs];
      state.offset = state.allLogs.length;
      state.hasMore = action.payload.hasMore;
      state.loading = false;
    },

    startLoading: state => {
      state.loading = true;
    },

    startInitialLoading: state => {
      state.initialLoading = true;
    },

    stopLoading: state => {
      state.loading = false;
      state.initialLoading = false;
    },

    resetLogEntries: state => {
      state.allLogs = [];
      state.offset = 0;
      state.hasMore = true;
      state.loading = false;
      state.initialLoading = false;
    },

    savePreviousOffset: state => {
      state.previousOffset = state.offset;
    },

    restorePreviousOffset: state => {
      state.offset = state.previousOffset;
    },

    setHasMore: (state, action: PayloadAction<boolean>) => {
      state.hasMore = action.payload;
    },

    setOffset: (state, action: PayloadAction<number>) => {
      state.offset = action.payload;
    },

    setTotalLoggedTime: (state, action: PayloadAction<number>) => {
      state.totalLoggedTime = action.payload;
    },
  },
});

export const {
  setLogEntries,
  appendLogEntries,
  startLoading,
  startInitialLoading,
  stopLoading,
  resetLogEntries,
  savePreviousOffset,
  restorePreviousOffset,
  setHasMore,
  setOffset,
  setTotalLoggedTime,
} = logEntriesSlice.actions;

export default logEntriesSlice.reducer;
