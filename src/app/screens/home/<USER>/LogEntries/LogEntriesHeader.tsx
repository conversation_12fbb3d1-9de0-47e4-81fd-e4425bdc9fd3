import React from 'react';
import {Image, View} from 'react-native';
import {useStyles} from 'react-native-unistyles';

import {styleSheet} from './LogEntries.styles';

import {UixText} from '@/app/ui/UixText';

interface LogEntriesHeaderProps {
  todaysLoggedTime?: number;
}

export const LogEntriesHeader = ({todaysLoggedTime}: LogEntriesHeaderProps) => {
  const {styles} = useStyles(styleSheet);

  return (
    <>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <UixText variant="dotsBold" style={styles.title} capitalize>
            Time Logs
          </UixText>
          {todaysLoggedTime && (
            <UixText variant="dots" style={styles.subtitle} capitalize>
              {`${todaysLoggedTime?.toFixed(2)} Hours`}
            </UixText>
          )}
        </View>

        <Image
          source={require('@assets/timelog.png')}
          style={styles.timelogIcon}
        />
      </View>
    </>
  );
};
