import {useFocusEffect, useRoute} from '@react-navigation/native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import dayjs from 'dayjs';
import React, {useCallback, useState} from 'react';
import {FlatList, Image, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useStyles} from 'react-native-unistyles';

import CalendarBottomSheet, {
  DateRange,
} from '../CalendarBottomSheet/CalendarBottomSheet';
import ProjectsBottomSheet from '../ProjectsBottomSheet/ProjectsBottomSheet';
import {TimelogBottomModal} from '../TimelogBottomModal';
import UpdateBottomSheet from '../UpdateBottomSheet/UpdateBottomSheet';
import {styleSheet} from './LogEntries.styles';
import {LogEntriesBottom} from './LogEntriesBottom';
import {LogEntriesFilter} from './LogEntriesFilter';
import {LogEntriesHeader} from './LogEntriesHeader';
import LogEntriesShimmer, {LogEntriesListShimmer} from './LogEntriesShimmer';
import LogEntriesView from './LogEntriesView';
import useLogEntries from './useLogEntries';
import useLogEntriesFilter from './useLogEntriesFilter';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {RootStackParamsList} from '@/app/navigation/types';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Timelogs} from '@/types/__generated__/graphql';

interface FilterState {
  selectedStatus: string;
  selectedProjects: string[];
}

type NavigationProps = NativeStackScreenProps<
  RootStackParamsList,
  ScreenNames.LogEntries
>;

const LogEntries = () => {
  const {styles} = useStyles(styleSheet);

  const [showCalendarBottomSheet, setShowCalendarBottomSheet] = useState(false);
  const [showProjectsBottomSheet, setShowProjectsBottomSheet] = useState(false);
  const [showTimelogsModal, setShowTimelogsModal] = useState(false);

  const [showUpdateBottomSheet, setShowUpdateBottomSheet] = useState(false);
  const [selectedLogEntry, setSelectedLogEntry] =
    useState<Members_Member_Timelogs | null>(null);

  const route = useRoute<NavigationProps['route']>();

  const totalLoggedTime = route.params?.totalLoggedTime;

  const insets = useSafeAreaInsets();

  const {
    dateFilter,
    appliedFilters,
    applyDateFilter,
    clearDateFilter,
    applyProjectStatusFilters,
    clearAllFilters,
    hasActiveFilters,
    filterParams,
  } = useLogEntriesFilter();

  const {
    groupedLogEntries,
    handleEndReached,
    loading,
    hasMoreData,
    isInitialLoading,
    refetchData,
    refetchDataAfterUpdate,
    refetchDataAfterNewEntry,
  } = useLogEntries(filterParams);

  // Refresh data when screen comes back into focus (skip initial focus)
  useFocusEffect(
    useCallback(() => {
      refetchData();
    }, [refetchData]),
  );

  const handleDateFilterPress = useCallback(() => {
    setShowCalendarBottomSheet(true);
  }, []);

  const handleProjectFilterPress = useCallback(() => {
    setShowProjectsBottomSheet(true);
  }, []);

  const handleClearDateFilter = useCallback(() => {
    clearDateFilter();
  }, [clearDateFilter]);

  const handleClearAllFilters = useCallback(() => {
    clearAllFilters();
  }, [clearAllFilters]);

  const handleUpdateBottomSheet = useCallback(
    (logEntry: Members_Member_Timelogs) => {
      setSelectedLogEntry(logEntry);
      setShowUpdateBottomSheet(true);
    },
    [],
  );

  const handleApplyDateFilter = useCallback(
    (dateRange: DateRange | null) => {
      if (dateRange?.startDate && dateRange?.endDate) {
        applyDateFilter(dateRange.startDate, dateRange.endDate);
      } else {
        clearDateFilter();
      }
      setShowCalendarBottomSheet(false);
    },
    [applyDateFilter, clearDateFilter],
  );

  const handleApplyProjectFilters = useCallback(
    (filters: FilterState) => {
      applyProjectStatusFilters(filters);
      setShowProjectsBottomSheet(false);
    },
    [applyProjectStatusFilters],
  );

  const handleCancelProjectFilters = useCallback(() => {
    setShowProjectsBottomSheet(false);
  }, []);

  const handleUpdateCompleted = useCallback(async () => {
    // Use refreshDataAfterUpdate to maintain current pagination position
    // This fetches the same number of items that were loaded before the update
    setShowUpdateBottomSheet(false);
    await refetchDataAfterUpdate();
    setSelectedLogEntry(null);
  }, [refetchDataAfterUpdate]);

  const renderFooter = useCallback(() => {
    if (loading && hasMoreData) {
      return (
        <View style={{gap: 12}}>
          {Array.from({length: 20}).map((_, index) => (
            <View key={index} style={{gap: 12}}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Shimmer style={{width: 120, height: 20, borderRadius: 4}} />
                <Shimmer style={{width: 80, height: 20, borderRadius: 4}} />
              </View>

              <LogEntriesShimmer />
            </View>
          ))}
        </View>
      );
    }
  }, [loading, hasMoreData]);

  const renderEmptyContent = useCallback(() => {
    return (
      <View style={styles.notFoundWrapper}>
        <Image
          source={require('@assets/not-found.png')}
          style={styles.notFoundImage}
        />
        <View style={styles.notFoundTextWrapper}>
          <UixText variant="dotsBold" style={styles.noTimeLogFound}>
            {'No Time log Found'}
          </UixText>

          <UixText style={{fontWeight: 400, fontSize: 14, color: '#D8F0ECB8'}}>
            {'Log Time to get started'}
          </UixText>
        </View>
      </View>
    );
  }, [hasActiveFilters]);

  const renderHeader = useCallback(() => {
    return (
      <>
        <LogEntriesFilter
          dateFilter={dateFilter}
          appliedFilters={appliedFilters}
          onDateFilterPress={handleDateFilterPress}
          onProjectFilterPress={handleProjectFilterPress}
          onClearDateFilter={handleClearDateFilter}
          onClearAllFilters={handleClearAllFilters}
        />
      </>
    );
  }, [
    dateFilter,
    appliedFilters,
    handleDateFilterPress,
    handleProjectFilterPress,
    handleClearDateFilter,
    handleClearAllFilters,
  ]);

  return (
    <View style={styles.container}>
      <View style={[styles.content, {paddingTop: insets.top + 20}]}>
        <View>
          <UixButton
            label="Back"
            iconImage={require('@assets/left.png')}
            onPress={() => NavigationService.goBack()}
            tintColor={'Teal'}
          />
        </View>
        <LogEntriesHeader todaysLoggedTime={totalLoggedTime} />

        {isInitialLoading ? (
          <View style={styles.shimmerContainer}>
            <LogEntriesListShimmer />
          </View>
        ) : (
          <FlatList
            data={groupedLogEntries}
            keyExtractor={group => group.name}
            renderItem={({item: group}) => (
              <View key={group.name} style={{gap: 10}}>
                <View style={styles.headerItem}>
                  <UixText variant="dotsBold" style={styles.headerText}>
                    {dayjs(group.name).isSame(dayjs(), 'day')
                      ? 'Today'
                      : dayjs(group.name).format('DD MMM')}
                  </UixText>
                  <UixText variant="dotsBold" style={styles.headerText}>
                    {`${group.totalHours.toFixed(2).toString()} HRS`}
                  </UixText>
                </View>
                {group.items.map(item => (
                  <LogEntriesView
                    key={item.id}
                    data={item}
                    onPress={() => handleUpdateBottomSheet(item)}
                  />
                ))}
              </View>
            )}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.5}
            removeClippedSubviews={false}
            initialNumToRender={5}
            contentContainerStyle={[
              styles.listContent,
              groupedLogEntries.length === 0 && {flex: 1},
            ]}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={renderEmptyContent}
            ListHeaderComponent={renderHeader}
          />
        )}
      </View>

      <LogEntriesBottom onLogTimePress={() => setShowTimelogsModal(true)} />

      {showTimelogsModal && (
        <TimelogBottomModal
          isOpen={showTimelogsModal}
          toggleModal={() => setShowTimelogsModal(false)}
          onCompleted={() => {
            refetchDataAfterNewEntry(); // Use specific function for new entries
            setShowTimelogsModal(false);
          }}
        />
      )}

      {showCalendarBottomSheet && (
        <CalendarBottomSheet
          isVisible={showCalendarBottomSheet}
          onBottomSheetDown={() => setShowCalendarBottomSheet(false)}
          onApplyDateFilter={handleApplyDateFilter}
          currentDateRange={dateFilter}
          onClose={() => setShowCalendarBottomSheet(false)}
        />
      )}

      {showProjectsBottomSheet && (
        <ProjectsBottomSheet
          isOpen={showProjectsBottomSheet}
          currentFilters={appliedFilters}
          onApplyFilters={handleApplyProjectFilters}
          onCancel={handleCancelProjectFilters}
          onClose={handleCancelProjectFilters}
        />
      )}

      {selectedLogEntry && (
        <UpdateBottomSheet
          isVisible={showUpdateBottomSheet}
          data={selectedLogEntry}
          onClose={() => setShowUpdateBottomSheet(false)}
          onUpdateCompleted={handleUpdateCompleted}
        />
      )}
    </View>
  );
};

export default LogEntries;
