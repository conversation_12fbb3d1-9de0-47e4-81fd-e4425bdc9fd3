import React from 'react';
import {View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import {styleSheet as logEntriesStyleSheet} from './LogEntries.styles';

import {RoundedButton} from '@/app/ui/RoundedButton';

interface LogEntriesBottomProps {
  onLogTimePress: () => void;
}

export const LogEntriesBottom = ({onLogTimePress}: LogEntriesBottomProps) => {
  const {styles: logEntriesStyles} = useStyles(logEntriesStyleSheet);
  const {styles} = useStyles(styleSheet);

  const handleLogTimePress = async () => {
    onLogTimePress();
  };

  return (
    <View style={logEntriesStyles.bottomContainer}>
      <View style={logEntriesStyles.bottomContent}>
        <RoundedButton
          label="Log Time +"
          center
          capitalize={false}
          style={styles.logTimeButton}
          onPress={handleLogTimePress}
        />

        {/* TODO: Add reminder button */}
        {/* <TouchableOpacity
          onPress={onReminderPress}
          style={logEntriesStyles.reminderButton}>
          <Image
            source={require('@assets/bell-icon.png')}
            style={styles.bellIcon}
          />
        </TouchableOpacity> */}
      </View>
    </View>
  );
};

const styleSheet = createStyleSheet(() => ({
  logTimeButton: {
    flex: 1,
  },
  bellIcon: {
    width: 32,
    height: 32,
  },
}));
