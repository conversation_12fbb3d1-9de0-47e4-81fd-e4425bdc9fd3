import React, {useCallback, useMemo, useState} from 'react';
import {Pressable, View} from 'react-native';
import Carousel, {ICarouselInstance} from 'react-native-reanimated-carousel';

import CarouselTile, {ActionType} from './CarouselTile';
import {LeftSvg, RightSvg} from './icons';

import {usePageBuilder} from '@/app/context/PageBuilderContext';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import Dimension from '@/app/utils/dimension';
import {
  App_Ui_Banner_Action_Types_Enum,
  HomeQueryQuery,
} from '@/types/__generated__/graphql';

function useCarouselBanners({
  data,
  loading,
  newbiesData,
  newbiesLoading,
}: {
  data?: HomeQueryQuery;
  loading: boolean;
  newbiesData?: HomeQueryQuery['recently_joined_members'];
  newbiesLoading: boolean;
}) {
  return useMemo(() => {
    const baseBanners = (data?.app_ui_homepage_banners || [])
      .sort((a, b) => a.banner_order - b.banner_order)
      .map(banner => ({
        theme: banner.banner_color,
        component: (
          <CarouselTile
            key={banner.id}
            theme={banner.banner_color}
            title={banner.banner_title}
            subTitle={banner.banner_description}
            actionText={
              banner.action_type === App_Ui_Banner_Action_Types_Enum.AppScreen
                ? 'View'
                : 'Go'
            }
            actionType={
              banner.action_type === App_Ui_Banner_Action_Types_Enum.AppScreen
                ? ActionType.APP_SCREEN
                : ActionType.HTTP
            }
            actionPayload={banner.action_url}
          />
        ),
      }));

    const shouldShowDrops =
      loading || (data?.drops_drop_details_aggregate.aggregate?.count ?? 0) > 0;
    const dropsBannerData = shouldShowDrops
      ? [
          {
            theme: Colors.Green,
            component: (
              <CarouselTile
                theme={Colors.Green}
                title={`${data?.drops_drop_details_aggregate.aggregate?.count ?? 0} New Drops`}
                subTitle={'Hurry drops are expiring'}
                actionText={'View'}
                actionType={ActionType.APP_SCREEN}
                actionPayload={'Drops'}
              />
            ),
          },
        ]
      : [];

    const shouldShowNewbies = (data?.recently_joined_members?.length ?? 0) > 0;
    const newbiesBannerData = shouldShowNewbies
      ? [
          {
            theme: Colors.Yellow,
            component: (
              <CarouselTile
                theme={Colors.Yellow}
                title={`${data?.recently_joined_members?.length ?? 0} New Members`}
                subTitle={`${data?.recently_joined_members
                  .filter(detail => Boolean(detail.alias))
                  .map(detail => detail.alias)
                  .join(' , ')}`}
                actionText={'View'}
                actionType={ActionType.APP_SCREEN}
                actionPayload={'Members'}
              />
            ),
          },
        ]
      : [];

    const finalBannersData = [
      ...baseBanners,
      ...dropsBannerData,
      ...newbiesBannerData,
    ];

    return finalBannersData.map((bannerData, index) => ({
      ...bannerData,
      id: index,
    }));
  }, [data, loading, newbiesData, newbiesLoading]);
}

export function FeaturedCarousel() {
  const {data, loading} = usePageBuilder();
  const ref = React.useRef<ICarouselInstance>(null);

  const [selectedIndex, setSelectedIndex] = useState<number>(0);

  const onProgressChange = useCallback(
    (_: number, absoluteProgress: number) =>
      setSelectedIndex(Math.round(absoluteProgress)),
    [],
  );

  const renderItem = useCallback(
    ({item}: {item: {component: React.JSX.Element}}) => item.component,
    [],
  );

  const banners = useCarouselBanners({
    data,
    loading,
    newbiesData: data?.recently_joined_members,
    newbiesLoading: loading,
  });

  if (banners.length === 0) return null;

  const selectedTheme =
    (banners[selectedIndex] ?? banners[0])?.theme ?? Colors.Black;

  return (
    <View style={{position: 'relative'}}>
      <Carousel
        loop={banners.length > 1}
        autoPlay={banners.length > 1}
        autoPlayInterval={5000}
        ref={ref}
        width={Dimension.SCREEN_WIDTH}
        onProgressChange={onProgressChange}
        height={120}
        data={banners}
        renderItem={renderItem}
      />
      <LeftSvg
        style={{
          position: 'absolute',
          left: -3,
          top: 0,
          width: 8,
          height: 120,
        }}
        color={selectedTheme}
      />
      <RightSvg
        style={{
          position: 'absolute',
          right: -3,
          top: 0,
          width: 8,
          height: 120,
        }}
        color={selectedTheme}
      />
      <View
        style={{
          position: 'absolute',
          display: 'flex',
          flexDirection: 'row',
          paddingVertical: 16,
          paddingHorizontal: 24,
          bottom: 0,
        }}>
        {banners.map((banner, index) => (
          <Pressable
            onPress={() => {
              if (ref.current) ref.current.scrollTo({index, animated: true});
            }}
            key={`dots_${banner.id}`}
            style={{
              width: 8,
              height: 8,
              marginRight: 6,
              backgroundColor: updateColorWithOpacity(
                selectedTheme,
                index === selectedIndex ? 1 : 0.1,
              ),
            }}
          />
        ))}
      </View>
    </View>
  );
}
