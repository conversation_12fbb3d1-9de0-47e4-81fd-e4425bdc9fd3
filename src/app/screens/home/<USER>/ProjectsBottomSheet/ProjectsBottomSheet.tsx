import {MemberProject} from '@graphQL/member';
import {RootState} from '@store/store';
import React, {useEffect, useState} from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';
import {useSelector} from 'react-redux';

import BottomSheet from '@/app/baseComponents/BottomSheet';
import useUixQuery from '@/app/hooks/useUixQuery';
import {Colors} from '@/app/theme/colors';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {MemberProjectQuery} from '@/types/__generated__/graphql';

interface FilterState {
  selectedStatus: string;
  selectedProjects: string[];
}

interface IProps {
  isOpen: boolean;
  currentFilters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  onCancel: () => void;
  onClose: () => void;
}

const status = [
  {id: 'ALL', name: 'AL<PERSON>', image: null},
  {
    id: 'PENDING',
    name: 'PENDING',
    disabledImage: require('@assets/disabled-pending.png'),
  },
  // {
  //   id: 'PAID',
  //   name: 'PAID',
  //   disabledImage: require('@assets/disabled-paid.png'),
  // },
  {
    id: 'APPROVED',
    name: 'APPROVED',
    disabledImage: require('@assets/disabled-approved.png'),
  },
  {
    id: 'REJECTED',
    name: 'REJECTED',
    disabledImage: require('@assets/disabled-rejected.png'),
  },
];

const getStatusColors = (statusId: string, isSelected: boolean) => {
  if (!isSelected) {
    return {
      backgroundColor: '#66CCBA1A',
      textColor: '#66807B',
    };
  }

  switch (statusId) {
    case 'PENDING':
      return {
        backgroundColor: 'rgba(218, 233, 1,0.20)',
        textColor: Colors.Yellow150,
        color: 'rgba(218, 233, 1, 1)',
      };
    // case 'PAID':
    //   return {
    //     backgroundColor: 'rgba(0, 255, 211, 0.20)',
    //     textColor: Colors.Teal425,
    //     color: 'rgba(0, 255, 211, 1)',
    //   };
    case 'APPROVED':
      return {
        backgroundColor: 'rgba(0, 255, 211, 0.20)',
        textColor: Colors.Teal425,
        color: 'rgba(0, 255, 211, 1)',
      };
    case 'REJECTED':
      return {
        backgroundColor: 'rgba(255, 128, 128, 0.20)',
        textColor: Colors.Logout,
        color: 'rgba(255, 128, 128, 1)',
      };

    case 'ALL':
      return {
        backgroundColor: 'rgba(0, 255, 209, 1)',
        textColor: 'rgba(7,9,8,1)',
      };
    default:
      return {
        backgroundColor: '#00FFD31A',
        textColor: '#00FFD3',
      };
  }
};

const ProjectsBottomSheet = ({
  isOpen,
  currentFilters,
  onApplyFilters,
  onCancel,
  onClose,
}: IProps) => {
  const userDetails = useSelector((store: RootState) => store.user);

  const {styles} = useStyles(styleSheet);

  const [tempSelectedStatus, setTempSelectedStatus] = useState<string>(
    currentFilters.selectedStatus,
  );
  const [tempSelectedProjects, setTempSelectedProjects] = useState<string[]>(
    currentFilters.selectedProjects,
  );

  useEffect(() => {
    setTempSelectedStatus(currentFilters.selectedStatus);
    setTempSelectedProjects(currentFilters.selectedProjects);
  }, [currentFilters]);

  const {data, loading, error} = useUixQuery<MemberProjectQuery>(
    MemberProject,
    {
      variables: {id: userDetails?.id},
    },
  );

  const projects = data?.members_member_projects ?? [];

  const handleStatusSelect = (statusId: string) => {
    setTempSelectedStatus(statusId);
  };

  const handleProjectSelect = (projectId: string) => {
    setTempSelectedProjects(prev => {
      if (prev.includes(projectId)) {
        return prev.filter(id => id !== projectId);
      } else {
        return [...prev, projectId];
      }
    });
  };

  const handleApply = async () => {
    const newFilters: FilterState = {
      selectedStatus: tempSelectedStatus,
      selectedProjects: tempSelectedProjects,
    };
    onApplyFilters(newFilters);
  };

  const handleCancel = () => {
    setTempSelectedStatus(currentFilters.selectedStatus);
    setTempSelectedProjects(currentFilters.selectedProjects);
    onCancel();
  };

  return (
    <BottomSheet
      isVisible={isOpen}
      style={styles.bottomSheet}
      possibleHeights={['50%']}
      onClose={onClose}>
      <View style={styles.container}>
        <UixButton
          label="BACK"
          iconImage={require('@assets/left.png')}
          tintColor="Teal"
          viewStyle={styles.backButton}
          onPress={handleCancel}
        />

        <View style={styles.section}>
          <UixText variant="dotsBold" style={styles.sectionTitle} capitalize>
            Select Status
          </UixText>

          <View style={styles.statusContainer}>
            {status.map(item => {
              const isSelected = tempSelectedStatus === item.id;
              const {backgroundColor, textColor, color} = getStatusColors(
                item.id,
                isSelected,
              );
              return (
                <TouchableOpacity
                  key={item.id}
                  onPress={() => handleStatusSelect(item.id)}
                  style={[
                    styles.statusWrapper,
                    {
                      backgroundColor: backgroundColor,
                    },
                  ]}>
                  {item?.disabledImage && (
                    <Image
                      source={item?.disabledImage}
                      tintColor={isSelected ? color : undefined}
                      style={styles.statusIcon}
                    />
                  )}
                  <UixText
                    variant="dotsBold"
                    style={[styles.statusText, {color: textColor}]}>
                    {item.name}
                  </UixText>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        <View>
          <UixText variant="dotsBold" style={styles.sectionTitle} capitalize>
            Select Project
          </UixText>
        </View>

        {loading && (
          <UixText style={styles.loadingText}>Loading projects...</UixText>
        )}
        {error && (
          <UixText style={styles.errorText}>Error loading projects</UixText>
        )}
        <View style={styles.projectsContainer}>
          {projects.map((project, index) => {
            const projectId = project?.project_detail?.id;
            const isSelected = tempSelectedProjects.includes(projectId);

            return (
              <TouchableOpacity
                key={index}
                onPress={() => handleProjectSelect(projectId)}
                style={[
                  styles.projectWrapper,
                  {
                    backgroundColor: isSelected
                      ? 'rgba(7,36,31,1)'
                      : Colors.White1000,
                  },
                ]}>
                <View style={styles.projectContent}>
                  <Image
                    source={{uri: project?.project_detail?.project_logo ?? ''}}
                    style={styles.projectLogo}
                  />
                  <UixText
                    style={[
                      styles.projectText,
                      {color: isSelected ? 'rgba(0,255,209,1)' : 'white'},
                    ]}>
                    {`${project?.project_detail?.project_name}`}
                  </UixText>
                </View>

                <Image
                  source={
                    isSelected
                      ? require('@assets/circle-green.png')
                      : require('@assets/circle.png')
                  }
                  style={styles.selectionIcon}
                />
              </TouchableOpacity>
            );
          })}
        </View>

        <View style={styles.buttonsContainer}>
          <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
            <UixText
              capitalize
              variant="dotsBold"
              style={styles.cancelButtonText}>
              CANCEL
            </UixText>
          </TouchableOpacity>

          <RoundedButton
            label="APPLY"
            style={styles.applyButton}
            center
            onPress={handleApply}
          />
        </View>
      </View>
    </BottomSheet>
  );
};

export default ProjectsBottomSheet;

const styleSheet = createStyleSheet(() => ({
  bottomSheet: {
    backgroundColor: 'black',
  },
  container: {
    padding: 16,
    gap: 32,
  },
  backButton: {
    marginBottom: 24,
    height: 40,
  },
  section: {
    gap: 16,
  },
  sectionTitle: {
    fontSize: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusWrapper: {
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    textAlignVertical: 'center',
  },
  statusIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  statusText: {
    fontSize: 20,
  },
  loadingText: {
    color: 'white',
  },
  errorText: {
    color: 'red',
  },
  projectsContainer: {
    gap: 12,
  },
  projectWrapper: {
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  projectContent: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  projectLogo: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  projectText: {
    fontSize: 18,
  },
  selectionIcon: {
    width: 24,
    height: 24,
  },
  buttonsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    borderWidth: 1.5,
    borderColor: Colors.Yellow150,
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
  },
  cancelButtonText: {
    color: Colors.Yellow150,
    fontSize: 20,
    textAlign: 'center',
  },
  applyButton: {
    flex: 1,
  },
}));
