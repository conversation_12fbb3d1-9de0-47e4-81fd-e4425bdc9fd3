import { useQuery } from '@apollo/client';
import { getLeads } from '@graphQL/leads';
import { RootState } from '@store/store';
import { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  GetLeadsQuery,
  Leads_Lead_Details,
  Leads_Lead_Status_Enum,
} from '@/types/__generated__/graphql';

export type GroupedLeads = {
  title: string;
  date: string;
  data: Leads_Lead_Details[];
};

const PAGE_LIMIT = 10;

const useLeadsEntries = () => {
  const userDetails = useSelector((state: RootState) => state.user);
  const [localInitialLoading, setLocalInitialLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<Leads_Lead_Status_Enum | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [leads, setLeads] = useState<Leads_Lead_Details[]>([]);

  const {
    data,
    loading: queryLoading,
    fetchMore,
  } = useQuery<GetLeadsQuery>(getLeads, {
    variables: {
      id: userDetails.id,
      limit: PAGE_LIMIT,
      offset: 0,
      status: selectedStatus,
    },
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: true,
    onCompleted: () => setLocalInitialLoading(false),
    onError: () => setLocalInitialLoading(false),
  });

  useEffect(() => {
    if (data?.members_member_details_by_pk?.lead_dealmaker_hunters) {
      const initialLeads = data.members_member_details_by_pk.lead_dealmaker_hunters.map(
        (lead) => lead.lead_detail
      ) as Leads_Lead_Details[];
      setLeads(initialLeads);
      setLocalInitialLoading(false);
    }
  }, [data]);

  const handleEndReached = useCallback(() => {
    // Don't fetch more if we're already loading or if there's no data
    if (isLoadingMore || queryLoading || !data?.members_member_details_by_pk?.lead_dealmaker_hunters) {
      return;
    }

    const currentLength = leads.length;

    // Only fetch more if we have exactly PAGE_LIMIT items in the current page
    // This ensures we only load more when there's likely more data
    if (currentLength > 0 && data.members_member_details_by_pk.lead_dealmaker_hunters.length === PAGE_LIMIT) {
      setIsLoadingMore(true);
      
      fetchMore({
        variables: {
          id: userDetails.id,
          limit: PAGE_LIMIT,
          offset: currentLength,
          status: selectedStatus,
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          setIsLoadingMore(false);
          
          if (!fetchMoreResult?.members_member_details_by_pk?.lead_dealmaker_hunters || 
              fetchMoreResult.members_member_details_by_pk.lead_dealmaker_hunters.length === 0) {
            return prev;
          }

          // Make sure prev.members_member_details_by_pk exists
          if (!prev.members_member_details_by_pk) {
            return fetchMoreResult;
          }

          // Create a new object with the combined results
          const result = {
            ...prev,
            members_member_details_by_pk: {
              ...prev.members_member_details_by_pk,
              lead_dealmaker_hunters: [
                ...prev.members_member_details_by_pk.lead_dealmaker_hunters || [],
                ...fetchMoreResult.members_member_details_by_pk.lead_dealmaker_hunters.filter(newItem => {
                  // Filter out duplicates
                  return !prev.members_member_details_by_pk?.lead_dealmaker_hunters.some(
                    existingItem => existingItem.lead_detail.id === newItem.lead_detail.id
                  );
                })
              ],
              // Preserve the aggregate count if it exists
              lead_dealmaker_hunters_aggregate: 
                fetchMoreResult.members_member_details_by_pk.lead_dealmaker_hunters_aggregate || 
                prev.members_member_details_by_pk.lead_dealmaker_hunters_aggregate
            }
          };

          return result;
        }
      }).catch(() => {
        setIsLoadingMore(false);
      });
    }
  }, [data, queryLoading, fetchMore, leads.length, userDetails.id, selectedStatus, isLoadingMore]);

  return {
    count: data?.members_member_details_by_pk?.lead_dealmaker_hunters_aggregate?.aggregate?.count ?? 0,
    leads,
    handleEndReached,
    loading: queryLoading,
    isInitialLoading: localInitialLoading,
    isLoadingMore,
    hasMoreData: (data?.members_member_details_by_pk?.lead_dealmaker_hunters?.length ?? 0) >= PAGE_LIMIT,
    setSelectedStatus,
    selectedStatus,
  };
};

export default useLeadsEntries;

