import {View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

export function DummyStoryGroupItem({text}: {text: string}) {
  return (
    <View
      style={{
        height: 96,
        width: 96,
        borderRadius: 92,
        backgroundColor: Colors.White900,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.White500,
        borderStyle: 'dashed',
      }}>
      <UixText
        variant="bodyMedium"
        style={{
          width: 80,
          textAlign: 'center',
          color: Colors.White500,
        }}>
        {text}
      </UixText>
    </View>
  );
}
