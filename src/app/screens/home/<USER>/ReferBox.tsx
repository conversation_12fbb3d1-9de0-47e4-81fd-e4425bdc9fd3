import ArrowRight from '@assets/arrowRight.svg';
import {FC} from 'react';
import {Pressable, View} from 'react-native';
import {SvgProps} from 'react-native-svg';

import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixText} from '@/app/ui/UixText';

export const ReferBox = (props: {
  label: string;
  color: string;
  Icon: FC<SvgProps>;
  onPress: () => void;
}) => {
  const {color, label, Icon, onPress} = props;

  return (
    <Pressable
      style={{
        flex: 1,
        padding: 20,
        borderColor: color,
        borderBottomWidth: 2,
        backgroundColor: updateColorWithOpacity(color, 0.1),
        borderRadius: 8,
      }}
      onPress={onPress}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <Icon width={32} height={32} />
        <ArrowRight fill={color} />
      </View>
      <UixText
        variant="dotsBold"
        style={{fontSize: 20, lineHeight: 24, marginTop: 16, color: color}}
        capitalize>
        {label}
      </UixText>
    </Pressable>
  );
};
