import dayjs from 'dayjs';
import React, {useCallback, useMemo, useState} from 'react';
import {FlatList, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useStyles} from 'react-native-unistyles';

import LeadDetailsBottomSheet from './LeadDetailsBottomSheet';
import {LeadEntriesListShimmer} from './LeadEntriesShimmer';
import {LeadsFilter} from './LeadFilters';
import {LeadItem} from './LeadItem';
import {styleSheet} from './LeadScreen.styles';
import {LeadsHeader} from './LeadsHeader';
import useLeadsEntries, {GroupedLeads} from './useLeadsEntries';

import {NavigationService} from '@/app/navigation/NavigationService';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {
  Leads_Lead_Details,
  Leads_Lead_Status_Enum,
} from '@/types/__generated__/graphql';

function groupLeadsByDate(leads: Leads_Lead_Details[]): GroupedLeads[] {
  const grouped = leads.reduce(
    (acc, lead) => {
      const date = dayjs(lead.created_at).format('YYYY-MM-DD');
      if (!acc[date]) {
        acc[date] = {title: date, date, data: []};
      }
      acc[date].data.push(lead);
      return acc;
    },
    {} as Record<string, GroupedLeads>,
  );

  return Object.values(grouped).sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
  );
}

export default function LeadScreen() {
  const {styles} = useStyles(styleSheet);
  const insets = useSafeAreaInsets();

  const {
    leads,
    handleEndReached,
    isInitialLoading,
    setSelectedStatus,
    selectedStatus,
    count,
    isLoadingMore,
  } = useLeadsEntries();

  const groupedLeads = useMemo(() => groupLeadsByDate(leads), [leads]);

  const [showLeadDetails, setShowLeadDetails] = useState(false);
  const [selectedLead, setSelectedLead] = useState<Leads_Lead_Details | null>(
    null,
  );
  const [isChangingFilter, setIsChangingFilter] = useState(false);

  const handleLeadPress = (lead: Leads_Lead_Details) => {
    setSelectedLead(lead);
    setShowLeadDetails(true);
  };

  const handleStatusChange = useCallback(
    (status: Leads_Lead_Status_Enum | null) => {
      if (status !== selectedStatus) {
        setIsChangingFilter(true);
        setSelectedStatus(status);
        setIsChangingFilter(false);
      }
    },
    [selectedStatus, setSelectedStatus],
  );

  const handleBackPress = () => {
    setSelectedLead(null);
    setShowLeadDetails(false);
    NavigationService.goBack();
  };

  const formatDate = useCallback((date: string) => {
    return dayjs(date).isSame(dayjs(), 'day')
      ? 'Today'
      : dayjs(date).format('DD MMM YYYY');
  }, []);

  const renderItem = useCallback(
    ({item: group}: {item: GroupedLeads}) => (
      <View key={`group-${group.date}`} style={{gap: 10, marginBottom: 10}}>
        <View style={styles.dateContainer}>
          <UixText style={styles.dateText} variant="dotsBold">
            {formatDate(group.date)}
          </UixText>
          <UixText style={styles.dateText} variant="dotsBold">
            {group.data.length}
          </UixText>
        </View>
        {group.data.map((item, index) => (
          <LeadItem
            key={`${item.id}-${index}`}
            lead={item}
            onPress={handleLeadPress}
          />
        ))}
      </View>
    ),
    [formatDate, handleLeadPress, styles.dateContainer, styles.dateText],
  );

  return (
    <View style={styles.container}>
      <View style={[styles.content, {paddingTop: insets.top + 20}]}>
        <View>
          <UixButton
            label="Back"
            iconImage={require('@assets/left.png')}
            onPress={handleBackPress}
            tintColor={'Teal'}
          />
        </View>
        <LeadsHeader filterLeadsStatus={selectedStatus} count={count} />
        <LeadsFilter
          selectedStatus={selectedStatus}
          onStatusChange={handleStatusChange}
        />

        {isInitialLoading || isChangingFilter ? (
          <LeadEntriesListShimmer />
        ) : groupedLeads.length === 0 ? (
          <UixText variant="dotsBold" style={styles.emptyText}>
            You don&apos;t have any leads...
          </UixText>
        ) : (
          <FlatList
            data={groupedLeads}
            renderItem={renderItem}
            keyExtractor={(group, index) => `${group.date}-${index}`}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.5}
            initialNumToRender={10}
            removeClippedSubviews={false}
            contentContainerStyle={[
              styles.listContent,
              groupedLeads.length === 0 && {flex: 1},
            ]}
            ListEmptyComponent={
              groupedLeads.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <UixText variant="dots" style={styles.emptyText}>
                    No leads found
                  </UixText>
                </View>
              ) : null
            }
            ListFooterComponent={() =>
              isLoadingMore ? <LeadEntriesListShimmer /> : null
            }
          />
        )}
      </View>
      {selectedLead && (
        <LeadDetailsBottomSheet
          isVisible={showLeadDetails}
          onClose={() => setShowLeadDetails(false)}
          lead={selectedLead}
        />
      )}
    </View>
  );
}
