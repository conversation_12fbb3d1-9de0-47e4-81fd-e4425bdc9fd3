import {useCallback} from 'react';
import {FlatList} from 'react-native';
import {View} from 'react-native';
import {StyleSheet, TouchableOpacity} from 'react-native';

import {UixText} from '@/app/ui/UixText';
import {Leads_Lead_Status_Enum} from '@/types/__generated__/graphql';

interface FilterItem {
  id: string;
  label: string;
  status: Leads_Lead_Status_Enum | null;
}

interface LeadsFilterProps {
  selectedStatus: Leads_Lead_Status_Enum | null;
  onStatusChange: (status: Leads_Lead_Status_Enum | null) => void;
}

const leadStatuses: FilterItem[] = [
  {
    id: 'all',
    label: 'All',
    status: null,
  },
  {
    id: 'active',
    label: 'Active',
    status: Leads_Lead_Status_Enum.Active,
  },
  {
    id: 'created',
    label: 'Created',
    status: Leads_Lead_Status_Enum.Created,
  },
  {
    id: 'unassigned',
    label: 'Unassigned',
    status: Leads_Lead_Status_Enum.Unassigned,
  },
  {
    id: 'stale',
    label: 'Stale',
    status: Leads_Lead_Status_Enum.Stale,
  },
  {
    id: 'dealClosed',
    label: 'Deal Closed',
    status: Leads_Lead_Status_Enum.DealClosed,
  },
  {
    id: 'notHappening',
    label: 'Not Happening',
    status: Leads_Lead_Status_Enum.NotHappening,
  },
];

export const LeadsFilter = ({
  selectedStatus,
  onStatusChange,
}: LeadsFilterProps) => {
  const renderFilterItem = useCallback(
    ({item}: {item: FilterItem}) => {
      const isSelected = selectedStatus === item.status;

      return (
        <TouchableOpacity
          style={[
            filterStyles.filterItem,
            isSelected && filterStyles.selectedFilterItem,
          ]}
          onPress={() => onStatusChange(item.status)}>
          <UixText
            variant="dotsBold"
            style={[
              filterStyles.filterText,
              isSelected && filterStyles.selectedFilterText,
            ]}>
            {item.label}
          </UixText>
        </TouchableOpacity>
      );
    },
    [selectedStatus, onStatusChange],
  );

  return (
    <View style={filterStyles.container}>
      <FlatList
        data={leadStatuses}
        renderItem={renderFilterItem}
        keyExtractor={item => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={filterStyles.listContent}
        removeClippedSubviews={false}
      />
    </View>
  );
};

const filterStyles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  listContent: {
    paddingVertical: 8,
  },
  filterItem: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 30,
    marginRight: 8,
    backgroundColor: 'rgba(102, 204, 186, 0.1)',
  },
  selectedFilterItem: {
    backgroundColor: 'rgba(0, 255, 209, 1)',
  },
  filterText: {
    color: 'rgba(102, 128, 123, 1)',
    fontSize: 20,
  },
  selectedFilterText: {
    color: 'rgba(7, 9, 8, 1)',
  },
});
