import {DocumentPickerResponse} from '@react-native-documents/picker';
import {RootState} from '@store/store';
import React, {useEffect, useState} from 'react';
import {
  Image,
  Keyboard,
  NativeSyntheticEvent,
  TextInputChangeEventData,
  View,
} from 'react-native';
import {useSelector} from 'react-redux';

import ResumeUploader from '../../../ui/resumeUploader/ResumeUploader';
import ReferFormSuccess from './ReferFormSuccess';

import {UIX_MEMBER_ID} from '@/app/constants';
import useDisclosure from '@/app/hooks/useDisclosure';
import useUixMutation from '@/app/hooks/useUixMutation';
import {Colors} from '@/app/theme/colors';
import {FullScreenModal} from '@/app/ui/FullScreenModal';
import {LinkedinSelector} from '@/app/ui/linkedInSelector/LinkedInSelector';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {UixTextInput} from '@/app/ui/UixTextInput';
import attachResume from '@/app/utils/addResume';
import {sendMemberReferralEmail} from '@/app/utils/email-integration';
import isValidEmail from '@/app/utils/isValidEmail';
import {splitFullName} from '@/app/utils/nameUtils';
import {notifyNewMemberReferral} from '@/app/utils/slackNotifications';
import {createMemberReferral} from '@/graphQL/referrals';
import {
  CreateMemberReferralMutation,
  CreateMemberReferralMutationVariables,
} from '@/types/__generated__/graphql';
interface FormState {
  values: {
    name: string;
    phone: string;
    linkedin: string;
    description: string;
    email: string;
    resume_link: string;
    referred_by: string;
  };
  errors: {
    name: boolean;
    description: boolean;
    linkedin: boolean;
    email: boolean;
    phone: boolean;
    resume_link: boolean;
  };
  emailError: string;
  isSubmitting: boolean;
  isUploading: boolean;
  resumeFile: DocumentPickerResponse | undefined;
}

export function ReferMemberForm({
  modalDisclosure,
  onboarding = false,
  showUploadResume = true,
}: {
  modalDisclosure: {
    opened: boolean;
    controls: {
      open: () => void;
      close: () => void;
      toggle: () => void;
    };
  };
  onboarding?: boolean;
  showUploadResume?: boolean;
}) {
  const icon = require('@assets/memberTitle.png');
  const userDetails = useSelector((store: RootState) => store.user);
  const successScreenDisclosure = useDisclosure();
  const [mutation, {loading}] = useUixMutation<
    CreateMemberReferralMutation,
    CreateMemberReferralMutationVariables
  >(createMemberReferral);

  const initialState = {
    values: {
      name: '',
      phone: '',
      linkedin: '',
      description: '',
      email: '',
      resume_link: '',
      referred_by: onboarding ? UIX_MEMBER_ID : userDetails.id,
    },
    errors: {
      name: false,
      description: false,
      linkedin: false,
      email: false,
      phone: false,
      resume_link: false,
    },
    emailError: '',
    isSubmitting: false,
    isUploading: false,
    resumeFile: undefined,
  };

  const [formState, setFormState] = useState<FormState>(initialState);

  async function onSubmit() {
    Keyboard.dismiss();
    if (loading) return;

    // Reset emailError at the beginning of a new submission attempt
    setFormState(prev => ({...prev, emailError: ''}));

    // Check if email contains uixlabs domain
    if (formState.values.email.toLowerCase().includes('@uixlabs')) {
      setFormState(prev => ({
        ...prev,
        emailError: `You can't onboard or refer an existing community member.`,
        isSubmitting: true,
        errors: {...prev.errors, email: true},
      }));
      return;
    }

    const formErrors = {
      phone: !formState.values.phone,
      linkedin: !formState.values.linkedin,
      description: !formState.values.description,
      name: !formState.values.name,
      email: !isValidEmail(formState.values.email),
      resume_link: !formState.resumeFile,
    };

    if (Object.values(formErrors).some(error => error)) {
      setFormState(prev => ({
        ...prev,
        isSubmitting: true,
        errors: formErrors,
      }));
      return;
    }

    try {
      setFormState(prev => ({...prev, isUploading: true}));
      const {firstName, lastName} = splitFullName(formState.values.name);

      if (!formState.resumeFile) {
        return;
      }

      const resumeUploadResponse = await attachResume(formState.resumeFile);

      if (!resumeUploadResponse?.secure_url) {
        setFormState(prev => ({
          ...prev,
          errors: {...prev.errors, resume_link: true},
          isUploading: false,
        }));
        return;
      }

      await mutation({
        variables: {
          description: formState.values.description,
          linkedin_url: formState.values.linkedin,
          first_name: firstName,
          last_name: lastName,
          phone_number: `+91${formState.values.phone}`,
          email: formState.values.email.toLowerCase(),
          resume_link: resumeUploadResponse.secure_url,
          referred_by: formState.values.referred_by ?? UIX_MEMBER_ID,
        },
        onCompleted: data => {
          if (data?.insert_members_member_referrals_one?.id) {
            notifyNewMemberReferral(
              data.insert_members_member_referrals_one.id,
            );
            sendMemberReferralEmail({
              id: data?.insert_members_member_referrals_one?.id,
            });
          }

          setFormState(initialState);
          successScreenDisclosure.controls.open();
        },
        onError: error => {
          if (error.message.includes('member_referrals_email_key')) {
            setFormState(prev => ({
              ...prev,
              emailError: 'This email address has already been registered.',
              isUploading: false,
            }));
          } else {
            setFormState(prev => ({...prev, isUploading: false}));
          }
        },
      });
    } catch (e) {
      console.error('Error in outer form submission try-catch:', e);
      setFormState(prev => ({...prev, isUploading: false}));
    }
  }

  useEffect(() => {
    setFormState(prev => ({
      ...prev,
      errors: {
        phone: !formState.values.phone && formState.isSubmitting,
        linkedin: !formState.values.linkedin && formState.isSubmitting,
        description: !formState.values.description && formState.isSubmitting,
        name: !formState.values.name && formState.isSubmitting,
        email:
          (!formState.values.email || !isValidEmail(formState.values.email)) &&
          formState.isSubmitting,
        resume_link: !formState.resumeFile && formState.isSubmitting,
      },
    }));

    if (
      formState.values.name &&
      formState.values.description &&
      formState.values.linkedin &&
      formState.values.phone &&
      formState.values.email &&
      isValidEmail(formState.values.email) &&
      formState.resumeFile
    ) {
      setFormState(prev => ({...prev, isSubmitting: false}));
    }
  }, [formState.values, formState.isSubmitting, formState.resumeFile]);

  return (
    <FullScreenModal
      isOpen={modalDisclosure.opened}
      onClose={() => {
        setFormState(prev => ({...prev, isSubmitting: false, emailError: ''}));
        modalDisclosure.controls.close();
      }}>
      {successScreenDisclosure.opened ? (
        <ReferFormSuccess
          onClose={() => {
            setFormState(prev => ({
              ...prev,
              isSubmitting: false,
              emailError: '',
            }));
            successScreenDisclosure.controls.close();
            modalDisclosure.controls.close();
          }}
          icon={icon}
          baseColor={'Orange'}
          text={`THANK YOU\nFOR\n${onboarding ? 'REQUESTING\nAN INVITE' : 'REFERRING'}`}
        />
      ) : (
        <View style={{flexGrow: 1}}>
          <View style={{padding: 16, flexGrow: 1, gap: 10}}>
            <UixButton
              label="Close"
              iconImage={require('@assets/cross.png')}
              onPress={() => {
                setFormState(prev => ({
                  ...prev,
                  isSubmitting: false,
                  emailError: '',
                }));
                modalDisclosure.controls.close();
              }}
              tintColor={'Orange'}
            />
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginTop: 20,
              }}>
              <UixText
                variant="dotsBold"
                style={{fontSize: 32, lineHeight: 32, color: Colors.Orange}}
                capitalize>
                {onboarding ? `REQUEST\nAN INVITE` : `REFER\nNEW MEMBER`}
              </UixText>
              <Image source={icon} style={{width: 40, height: 40}} />
            </View>
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 16,
                marginTop: 20,
                color: Colors.Orange,
              }}
              capitalize>{`${onboarding ? 'Your' : 'Member'} FULL NAME`}</UixText>
            <UixTextInput
              mode="outlined"
              placeholder="John Doe"
              textContentType="name"
              outlineColor={
                formState.errors.name ? Colors.Error : Colors.Orange1000
              }
              activeOutlineColor={
                formState.errors.name ? Colors.Error : Colors.Orange
              }
              textColor={Colors.Orange}
              value={formState.values.name}
              onChange={({
                nativeEvent: {text},
              }: NativeSyntheticEvent<TextInputChangeEventData>) => {
                setFormState(prev => ({
                  ...prev,
                  values: {...prev.values, name: text},
                }));
              }}
              placeholderTextColor={Colors.Orange900}
              style={{backgroundColor: Colors.Orange1000}}
            />
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 16,
                marginTop: 20,
                color: Colors.Orange,
              }}
              capitalize>{`Phone Number`}</UixText>
            <UixTextInput
              mode="outlined"
              placeholder="XXX-XXX-XXXX"
              textContentType="telephoneNumber"
              outlineColor={
                formState.errors.phone ? Colors.Error : Colors.Orange1000
              }
              activeOutlineColor={
                formState.errors.phone ? Colors.Error : Colors.Orange
              }
              textColor={Colors.Orange}
              keyboardType="numeric"
              inputMode="numeric"
              maxLength={10}
              value={formState.values.phone}
              onChange={({
                nativeEvent: {text},
              }: NativeSyntheticEvent<TextInputChangeEventData>) => {
                setFormState(prev => ({
                  ...prev,
                  values: {...prev.values, phone: text},
                }));
              }}
              placeholderTextColor={Colors.Orange900}
              style={{backgroundColor: Colors.Orange1000}}
            />
            <LinkedinSelector
              value={formState.values.name}
              onChange={(text: string) => {
                setFormState(prev => ({
                  ...prev,
                  values: {...prev.values, linkedin: text},
                }));
              }}
              color="Orange"
              errored={formState.errors.linkedin}
            />
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 16,
                marginTop: 20,
                color: Colors.Orange,
              }}
              capitalize>{`Email`}</UixText>

            <UixTextInput
              mode="outlined"
              placeholder="<EMAIL>"
              keyboardType="email-address"
              autoCapitalize="none"
              outlineColor={
                formState.errors.email || formState.emailError
                  ? Colors.Error
                  : Colors.Orange1000
              }
              activeOutlineColor={
                formState.errors.email || formState.emailError
                  ? Colors.Error
                  : Colors.Orange
              }
              textColor={Colors.Orange}
              value={formState.values.email}
              onChange={({
                nativeEvent: {text},
              }: NativeSyntheticEvent<TextInputChangeEventData>) => {
                setFormState(prev => ({
                  ...prev,
                  values: {...prev.values, email: text},
                  emailError: '', // Clear error when user types
                }));
              }}
              placeholderTextColor={Colors.Orange900}
              style={{backgroundColor: Colors.Orange1000}}
            />
            {formState.emailError ? (
              <UixText
                variant="dotsBold"
                style={{
                  fontSize: 14,
                  color: Colors.Error,
                  marginTop: 4,
                }}>
                {formState.emailError}
              </UixText>
            ) : null}

            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 16,
                marginTop: 20,
                color: Colors.Orange,
              }}
              capitalize>
              {onboarding
                ? `Few words about yourself`
                : `Describe member in few words`}
            </UixText>
            <UixTextInput
              mode="outlined"
              placeholder="A fintech expert shaping the next-gen app. Excited about challenges and rewards!"
              outlineColor={
                formState.errors.description ? Colors.Error : Colors.Orange1000
              }
              activeOutlineColor={
                formState.errors.description ? Colors.Error : Colors.Orange
              }
              textColor={Colors.Orange}
              value={formState.values.description}
              onChange={({
                nativeEvent: {text},
              }: NativeSyntheticEvent<TextInputChangeEventData>) => {
                setFormState(prev => ({
                  ...prev,
                  values: {...prev.values, description: text},
                }));
              }}
              multiline={true}
              placeholderTextColor={Colors.Orange900}
              style={{
                backgroundColor: Colors.Orange1000,
                paddingVertical: 16,
              }}
            />
            {showUploadResume && (
              <View style={{display: 'flex', rowGap: 8}}>
                <UixText
                  variant="dotsBold"
                  style={{
                    fontSize: 20,
                    lineHeight: 16,
                    marginTop: 20,
                    color: Colors.Orange,
                  }}
                  capitalize>
                  UPLOAD RESUME
                </UixText>

                <ResumeUploader
                  onFileChange={file => {
                    setFormState(prev => ({
                      ...prev,
                      resumeFile: file,
                    }));
                  }}
                  errored={formState.errors.resume_link}
                  color={formState.resumeFile ? Colors.Orange : undefined}
                />
              </View>
            )}
          </View>

          {!successScreenDisclosure.opened && (
            <RoundedButton
              label="Submit"
              loading={loading || formState.isUploading}
              disabled={formState.isSubmitting}
              onPress={onSubmit}
              style={{padding: 16}}
              center
            />
          )}
        </View>
      )}
    </FullScreenModal>
  );
}
