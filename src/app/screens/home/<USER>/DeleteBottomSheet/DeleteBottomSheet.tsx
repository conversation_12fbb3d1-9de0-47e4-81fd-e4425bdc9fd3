import {deleteTimelogs} from '@graphQL/timelog';
import {useCallback, useMemo, useRef} from 'react';
import React from 'react';
import {ActivityIndicator, Image, TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {ToastOptions, useToast} from 'react-native-toast-notifications';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import BottomSheet from '@/app/baseComponents/BottomSheet';
import {SuccessToast} from '@/app/baseComponents/Toast/Toast';
import useUixMutation from '@/app/hooks/useUixMutation';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';

interface IProps {
  isVisible: boolean;
  onClose?: () => void;
  timelogId?: string;
  onDeleteCompleted?: () => void;
}

const DeleteBottomSheet = ({
  isVisible,
  onClose,
  timelogId,
  onDeleteCompleted,
}: IProps) => {
  const [deleteTimelog, {loading}] = useUixMutation(deleteTimelogs);

  const {styles} = useStyles(styleSheet);
  const toastIdRef = useRef('');
  const toast = useToast();
  const insets = useSafeAreaInsets();

  const toastOptions: ToastOptions = useMemo(() => {
    return {
      placement: 'top',
      duration: 3000,
      animationType: 'slide-in',
      textStyle: {
        fontSize: 16,
        color: Colors.Yellow750,
      },
      style: {
        backgroundColor: '#192608',
        padding: 16,
        borderRadius: 8,
        marginHorizontal: 16,
        zIndex: 9999,
        marginTop: insets.top + 20,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
      },
    };
  }, []);

  const showToast = useCallback(
    (message: string) => {
      if (toastIdRef.current) {
        toast.hide(toastIdRef.current);
      }
      setTimeout(() => {
        toastIdRef.current = toast.show(
          <SuccessToast message={message} />,
          toastOptions,
        );
      }, 100);
    },
    [toast, toastOptions],
  );

  const handleDelete = async () => {
    try {
      await deleteTimelog({
        variables: {
          id: timelogId,
        },
        onCompleted: () => {
          onDeleteCompleted?.();
          showToast('Timelog deleted successfully');
        },
      });
    } catch (error) {
      console.error('Error deleting timelog:', error);
    }
  };

  return (
    <BottomSheet
      isVisible={isVisible}
      onClose={onClose}
      style={{backgroundColor: 'black'}}>
      <View style={styles.container}>
        <UixButton
          label="BACK"
          iconImage={require('@assets/left.png')}
          tintColor="Teal"
          viewStyle={styles.backButton}
          onPress={onClose}
        />
        <View style={styles.deleteContainer}>
          <UixText style={styles.descriptionHeading} variant="dotsBold">
            DELTE TIME LOG
          </UixText>

          <UixText style={styles.descriptionText}>
            Are you sure you want to delete this timelog?
          </UixText>
        </View>

        <TouchableOpacity
          style={styles.deleteWrapper}
          onPress={() => {
            handleDelete();
          }}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={Colors.Red100} />
          ) : (
            <>
              <Image
                source={require('@assets/delete-default.png')}
                style={{width: 24, height: 24}}
              />
              <UixText variant="dotsBold" style={styles.deleteText}>
                DELETE
              </UixText>
            </>
          )}
        </TouchableOpacity>
      </View>
    </BottomSheet>
  );
};

export default DeleteBottomSheet;

const styleSheet = createStyleSheet(() => ({
  deleteWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.Logout,
    padding: 14,
    justifyContent: 'center',
    borderRadius: 8,
    gap: 8,
  },

  deleteText: {
    fontSize: 20,
    color: '#02161F',
    textAlign: 'center',
  },

  descriptionText: {
    fontSize: 16,
  },

  deleteContainer: {
    gap: 24,
  },

  descriptionHeading: {
    fontSize: 28,
  },

  container: {
    padding: 16,
    gap: 24,
  },

  backButton: {
    marginBottom: 24,
    height: 40,
  },
}));
