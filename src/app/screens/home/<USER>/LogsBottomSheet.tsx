import React from 'react';
import {Image, View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {FullScreenModal} from '@/app/ui/FullScreenModal';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Timelogs} from '@/types/__generated__/graphql';

export const LogsBottomSheet = ({
  isVisible,
  onClose,
  todaysTimelogs,
}: {
  isVisible: boolean;
  onClose: () => void;
  todaysTimelogs: Members_Member_Timelogs[] | undefined;
}) => {
  return (
    <FullScreenModal isOpen={isVisible} onClose={onClose}>
      <View style={{padding: 16, flexGrow: 1}}>
        <UixButton
          label="Close"
          iconImage={require('@assets/cross.png')}
          onPress={onClose}
          tintColor={'White'}
          viewStyle={{marginBottom: 24, height: 40}}
        />
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 26,
          }}>
          <UixText
            variant="dotsBold"
            style={{fontSize: 32, lineHeight: 22}}
            capitalize>
            Time Logs
          </UixText>
          <Image
            source={require('@assets/timelog.png')}
            style={{width: 40, height: 40}}
          />
        </View>

        {todaysTimelogs?.map((timelog, index) => (
          <View
            key={timelog?.id || `timelog-${index}`}
            style={{
              backgroundColor: Colors.Teal950,
              padding: 16,
              marginBottom: 10,
              borderRadius: 8,
            }}>
            <UixText
              variant="dotsBold"
              style={{fontSize: 32, lineHeight: 22}}
              capitalize>
              {`${timelog?.no_of_hours} hrs`}
            </UixText>
            <View style={{height: 8}} />
            <UixText
              variant="titleMedium"
              style={{color: Colors.Teal}}
              numberOfLines={2}>
              {`${timelog?.work_description}`}
            </UixText>
            <View style={{height: 8}} />
            <UixText style={{color: Colors.Teal700}}>
              {new Date(timelog?.created_at).toLocaleDateString()}
            </UixText>
          </View>
        ))}
      </View>
    </FullScreenModal>
  );
};
