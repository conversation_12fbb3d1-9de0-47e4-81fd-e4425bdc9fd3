import Clipboard from '@react-native-clipboard/clipboard';
import dayjs from 'dayjs';
import React, {useMemo} from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import BottomSheet from '@/app/baseComponents/BottomSheet';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {useToastUtils} from '@/app/utils/toast';
import {Leads_Lead_Details} from '@/types/__generated__/graphql';

interface IProps {
  isVisible: boolean;
  onClose: () => void;
  lead: Leads_Lead_Details;
}

const LeadDetailsBottomSheet = ({isVisible, onClose, lead}: IProps) => {
  const {styles} = useStyles(styleSheet);
  const {showToast} = useToastUtils();

  const formattedDate = useMemo(() => {
    return `CREATED ON ${dayjs(lead.created_at).format('DD-MM-YYYY')}`;
  }, [lead.created_at]);

  const handleCopy = (text: string, type: string) => {
    Clipboard.setString(text);
    showToast(`${type} copied to clipboard`);
  };

  if (!lead) {
    return null;
  }

  return (
    <>
      <BottomSheet
        isVisible={isVisible}
        onClose={onClose}
        style={styles.bottomSheet}>
        <View style={styles.container}>
          <UixButton
            label="BACK"
            iconImage={require('@assets/left.png')}
            tintColor="Teal"
            viewStyle={styles.backButton}
            onPress={onClose}
          />

          <View style={styles.infoContainer}>
            <View style={styles.infoRowContainer}>
              {(lead.first_name || lead.last_name) && (
                <View style={styles.infoRow}>
                  <UixText
                    variant="dotsBold"
                    style={styles.infoText}
                    capitalize>
                    {`${lead.first_name || ''} ${lead.last_name || ''}`}
                  </UixText>
                </View>
              )}

              <View style={[styles.infoRow, styles.logos]}>
                {lead.phone_number && (
                  <TouchableOpacity
                    onPress={() =>
                      handleCopy(lead.phone_number || '', 'Phone number')
                    }>
                    <Image source={require('@assets/mobileIcon.png')} />
                  </TouchableOpacity>
                )}
                {lead.linkedin_url && (
                  <TouchableOpacity
                    onPress={() =>
                      handleCopy(lead.linkedin_url || '', 'LinkedIn URL')
                    }>
                    <Image source={require('@assets/LinkedinLogo.png')} />
                  </TouchableOpacity>
                )}
                {lead.email && (
                  <TouchableOpacity
                    onPress={() => handleCopy(lead.email || '', 'Email')}>
                    <Image source={require('@assets/Email.png')} />
                  </TouchableOpacity>
                )}
              </View>

              <View style={styles.sourceContainer}>
                {lead.source && (
                  <View style={[styles.infoRow, styles.sourceItem]}>
                    <UixText variant="dotsBold" style={styles.infoText}>
                      {lead.source.split('_').join(' ') || ''}
                    </UixText>
                  </View>
                )}
              </View>
            </View>

            {/* Description */}
            <View style={styles.descriptionContainer}>
              {lead.description && lead.lead_company && (
                <>
                  <UixText style={styles.leadCompany}>
                    {lead.lead_company || ''}
                  </UixText>
                  <UixText style={styles.description}>
                    {lead.description || ''}
                  </UixText>
                </>
              )}

              {lead.created_at && (
                <UixText style={styles.createdDate} capitalize>
                  {formattedDate}
                </UixText>
              )}
            </View>
          </View>

          <View style={styles.buttonsContainer}>
            <RoundedButton
              disabled={true}
              icon={require('@assets/PlusLogo.png')}
              label="NOTES"
              iconPosition="left"
              center
              style={styles.notesButton}
              onPress={async () => {
                // TODO: Handle edit in the future
              }}
            />

            <TouchableOpacity style={styles.editButton}>
              <Image
                style={styles.editIcon}
                source={require('@assets/editIcon.png')}
              />
            </TouchableOpacity>
          </View>
        </View>
      </BottomSheet>
    </>
  );
};

export default LeadDetailsBottomSheet;

const styleSheet = createStyleSheet(() => ({
  bottomSheet: {
    backgroundColor: 'black',
  },
  container: {
    padding: 16,
    gap: 24,
  },
  backButton: {
    marginBottom: 24,
    height: 40,
  },
  infoContainer: {
    gap: 24,
    borderBottomWidth: 2,
    borderBottomColor: Colors.Teal400,
    padding: 16,
    borderRadius: 8,
    backgroundColor: updateColorWithOpacity(Colors.Teal425, 0.07),
  },
  infoRowContainer: {
    gap: 8,
  },
  infoRow: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: updateColorWithOpacity(Colors.Teal425, 0.08),
    gap: 8,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: Colors.Teal425,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.Teal425,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initials: {
    fontSize: 16,
    color: Colors.White,
    fontWeight: 'bold',
  },
  infoText: {
    fontSize: 20,
    color: Colors.Teal425,
  },
  linkedinText: {
    fontSize: 20,
    color: Colors.Teal425,
    flex: 1,
    flexWrap: 'wrap',
  },
  description: {
    fontSize: 16,
    color: Colors.text,
  },
  createdDate: {
    fontSize: 12,
    color: updateColorWithOpacity(Colors.text, 0.56),
    fontWeight: 'bold',
    marginTop: 8,
  },
  buttonsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  notesButton: {
    flex: 1,
  },
  editIcon: {
    tintColor: Colors.White,
    opacity: 0.5,
  },
  editButton: {
    borderWidth: 1,
    borderColor: updateColorWithOpacity(Colors.White, 0.5),
    padding: 14,
    borderRadius: 8,
  },
  descriptionContainer: {
    gap: 12,
  },
  sourceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
    gap: 8,
  },
  sourceItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  logos: {
    gap: 20,
  },
  leadCompany: {
    fontSize: 20,
    color: Colors.headerText,
    fontWeight: 500,
  },
}));
