import React from 'react';
import {Image, ImageSourcePropType, View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';

interface ReferFormSuccessProps {
  onClose: () => void;
  icon: ImageSourcePropType;
  baseColor: 'Teal' | 'Orange';
  text: string;
}

function ReferFormSuccess({
  onClose,
  icon,
  baseColor,
  text,
}: ReferFormSuccessProps) {
  return (
    <View
      style={{
        flexGrow: 1,
        padding: 16,
        justifyContent: 'space-between',
      }}>
      <UixButton
        label="Close"
        iconImage={require('@assets/cross.png')}
        onPress={onClose}
        tintColor={baseColor}
      />
      <View style={{gap: 32, paddingHorizontal: 16}}>
        <Image
          source={icon}
          style={{
            width: 40,
            height: 40,
          }}
        />
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 40,
            lineHeight: 40,
            color: baseColor === 'Teal' ? Colors.Teal : Colors.Orange,
          }}>
          {text}
        </UixText>
      </View>
      <RoundedButton
        onPress={async () => onClose()}
        label={'Go Back to Home'}
        icon={require('@assets/arrow_right_dotted.png')}
      />
    </View>
  );
}

export default ReferFormSuccess;
