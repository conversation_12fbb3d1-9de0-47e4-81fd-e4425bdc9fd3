import React, {useCallback, useMemo} from 'react';
import {FlatList} from 'react-native';
import Animated, {LinearTransition} from 'react-native-reanimated';

import {DummyStoryGroupItem} from './DummyStoriesGroupItem';
import StoryGroupItem from './StoryGroupItem';
import {getStoryGroups} from './utils';

import {usePageBuilder} from '@/app/context/PageBuilderContext';
import {Colors} from '@/app/theme/colors';
import {Shimmer} from '@/app/ui/Shimmer';
import Dimension from '@/app/utils/dimension';
import {
  Stories_Story_Details,
  Stories_Story_Groups,
} from '@/types/__generated__/graphql';

export default function StoriesGroup() {
  const {data, loading} = usePageBuilder();
  const groups = useMemo(() => {
    return data && data?.stories_story_groups && !loading
      ? getStoryGroups(data.stories_story_groups as Stories_Story_Groups[])
      : [];
  }, [data]);

  const renderItem = useCallback(
    (
      item: {
        isDummy: boolean;
        title: string;
        count?: number;
        story?: Stories_Story_Details;
      },
      index: number,
    ) => {
      if (item.isDummy) return <DummyStoryGroupItem text={item.title} />;

      return (
        <StoryGroupItem
          index={index}
          title={item.title}
          story={item.story!}
          count={item.count!}
        />
      );
    },
    [data],
  );

  if (groups.length != 0) {
    return (
      <Animated.View
        style={{width: '100%', backgroundColor: Colors.DarkBackground}}
        layout={LinearTransition}>
        {loading ? (
          <Shimmer height={128} width={Dimension.SCREEN_WIDTH} />
        ) : (
          <FlatList
            horizontal
            data={groups}
            renderItem={({item, index}) => renderItem(item, index)}
            keyExtractor={(item, index) =>
              item.title && !item.isDummy ? item.title : `story-group-${index}`
            }
            contentContainerStyle={{columnGap: 16, padding: 16}}
            showsHorizontalScrollIndicator={false}
            nestedScrollEnabled
          />
        )}
      </Animated.View>
    );
  }
}
