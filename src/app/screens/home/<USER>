import React from 'react';

import {PageBuilderProvider} from '@/app/context/PageBuilderContext';
import {HomeQueryQuery} from '@/types/__generated__/graphql';

interface PageBuilderProps {
  children: React.ReactNode;
  data: HomeQueryQuery | undefined;
  loading: boolean;
}

interface PageBuilderHeaderProps {
  children: React.ReactNode;
}

interface PageBuilderContentProps {
  children: React.ReactNode;
}

interface PageBuilderComponent extends React.FC<PageBuilderProps> {
  Header: React.FC<{children: React.ReactNode}>;
  Content: React.FC<{children: React.ReactNode}>;
}

const PageBuilder: PageBuilderComponent = ({
  children,
  data,
  loading,
}: PageBuilderProps) => {
  return (
    <PageBuilderProvider data={data} loading={loading}>
      {children}
    </PageBuilderProvider>
  );
};

PageBuilder.displayName = 'PageBuilder';

PageBuilder.Header = ({children}: PageBuilderHeaderProps) => {
  return <>{children}</>;
};

PageBuilder.Header.displayName = 'PageBuilder.Header';

PageBuilder.Content = ({children}: PageBuilderContentProps) => {
  return <>{children}</>;
};

PageBuilder.Content.displayName = 'PageBuilder.Content';

export {PageBuilder};
