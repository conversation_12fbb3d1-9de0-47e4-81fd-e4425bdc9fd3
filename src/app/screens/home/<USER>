import {HomeQuery} from '@graphQL/home';
import {RootState} from '@store/store';
import dayjs from 'dayjs';
import React, {useMemo, useRef, useState} from 'react';
import {View} from 'react-native';
import {useSelector} from 'react-redux';

import {FeaturedCarousel} from './carousel/FeaturedCarousel';
import {HeaderProfile} from './header/HeaderProfile';
import LeadsSection from './Leads/LeadsSection';
import {PageBuilder} from './PageBuilder';
import PlaybookInfo from './PlaybookInfo';
import {ReferSection} from './refer/ReferSection';
import StoriesGroup from './storiesGroup/StoriesGroup';
import {TimelogsSection} from './timelogs/TimelogSection';

import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {HomeQueryQuery} from '@/types/__generated__/graphql';

enum UserType {
  Member = 'Member',
  Partner = 'Partner',
  Default = 'default',
}

const widgetConfig = {
  [UserType.Member]: {
    StoriesGroup: true,
    FeaturedCarousel: true,
    TimelogsSection: true,
    PlaybookInfo: true,
    ReferSection: true,
    LeadsSection: true,
  },
  [UserType.Partner]: {
    StoriesGroup: true,
    FeaturedCarousel: true,
    TimelogsSection: false,
    PlaybookInfo: true,
    ReferSection: true,
    LeadsSection: true,
  },
  [UserType.Default]: {
    StoriesGroup: true,
    FeaturedCarousel: true,
    TimelogsSection: true,
    PlaybookInfo: true,
    ReferSection: true,
    LeadsSection: true,
  },
} as const;

export default function HomeScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const now = useRef(dayjs().toISOString());
  const userDetails = useSelector((store: RootState) => store.user);

  const {data, loading, refetch} = useUixQuery<HomeQueryQuery>(HomeQuery, {
    variables: {
      dropDetailcurrentDate: now.current,
      member_id: userDetails?.id,
      joiningStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    },
  });

  const userType = useMemo(() => {
    const type = data?.members_member_details?.[0]?.type?.name;
    switch (type) {
      case 'Member':
        return UserType.Member;
      case 'Partner':
        return UserType.Partner;
      default:
        return UserType.Default;
    }
  }, [data?.members_member_details]);

  const enabledWidgets = useMemo(() => {
    return widgetConfig[userType];
  }, [userType]);

  const renderWidgets = () => (
    <>
      {enabledWidgets.StoriesGroup && <StoriesGroup />}
      {enabledWidgets.FeaturedCarousel && <FeaturedCarousel />}
      {enabledWidgets.TimelogsSection && <TimelogsSection />}
      {enabledWidgets.LeadsSection && <LeadsSection />}
      {enabledWidgets.PlaybookInfo && <PlaybookInfo />}
      {enabledWidgets.ReferSection && <ReferSection />}
    </>
  );

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <ScreenContainer
      name={ScreenNames.Home}
      tabScreen
      enableKeyboardScrollView
      onRefresh={handleRefresh}
      refresh={refreshing}>
      <View style={{flexDirection: 'column', gap: 12}}>
        <PageBuilder data={data} loading={loading}>
          <PageBuilder.Header>
            <HeaderProfile />
          </PageBuilder.Header>

          <PageBuilder.Content>{renderWidgets()}</PageBuilder.Content>
        </PageBuilder>
      </View>
    </ScreenContainer>
  );
}
