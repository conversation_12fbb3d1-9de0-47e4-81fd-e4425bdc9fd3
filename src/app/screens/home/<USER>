import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {Image, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import ReminderView from './timelogs/ReminderView/ReminderView';

import {ScreenNames} from '@/app/navigation/constants';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';

const ReminderScreen = () => {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const {styles} = useStyles(styleSheet);

  return (
    <View style={[styles.container, {paddingTop: insets.top + 20}]}>
      <UixButton
        label="Close"
        iconImage={require('@assets/cross.png')}
        tintColor={'White'}
        viewStyle={styles.closeButton}
        onPress={() => navigation.navigate(ScreenNames.App as never)}
      />

      <View style={styles.headerRow}>
        <View style={styles.titleContainer}>
          <View>
            <UixText variant="dotsBold" style={styles.titleText}>
              ADD
            </UixText>
            <UixText variant="dotsBold" style={styles.titleText}>
              REMINDER
            </UixText>
          </View>
        </View>

        <View style={styles.iconContainer}>
          <Image
            source={require('@assets/notification-bell.png')}
            style={styles.bellIcon}
          />
        </View>
      </View>

      <View style={styles.descriptionContainer}>
        <UixText style={styles.descriptionText}>
          Set up a reminder so you never miss logging your time—remember that
          you can only add entries for the
        </UixText>
        <UixText style={[styles.descriptionText, styles.highlightText]}>
          past two days
        </UixText>
        <UixText style={styles.descriptionText}>.</UixText>
      </View>
      <ReminderView />
    </View>
  );
};

const styleSheet = createStyleSheet(() => ({
  container: {
    paddingHorizontal: 16,
    backgroundColor: 'black',
    flex: 1,
    gap: 24,
  },
  closeButton: {
    marginBottom: 24,
    height: 40,
  },
  headerRow: {
    flexDirection: 'row',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingLeft: 16,
  },
  titleText: {
    fontSize: 32,
    paddingTop: 12,
  },
  iconContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingRight: 16,
  },
  bellIcon: {
    width: 52,
    height: 52,
  },
  descriptionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-end',
  },
  descriptionText: {
    fontSize: 16,
  },
  highlightText: {
    color: Colors.Yellow,
  },
}));

export default ReminderScreen;
