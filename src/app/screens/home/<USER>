import RightArrow from '@assets/arrowRight.svg';
import React from 'react';
import {Image, Pressable, View} from 'react-native';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixText} from '@/app/ui/UixText';

export default function PlaybookInfo() {
  const onPress = () => {
    NavigationService.navigate(ScreenNames.PlayBook);
  };

  return (
    <Pressable
      style={{
        marginHorizontal: 10,
        height: 122,
        flexDirection: 'row',
        backgroundColor: updateColorWithOpacity(Colors.Teal, 0.1),
        borderRadius: 8,
      }}
      onPress={onPress}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'center',
          padding: 20,
          paddingRight: 0,
          gap: 10,
        }}>
        <UixText
          variant="dotsBold"
          style={{fontSize: 20, color: Colors.Teal}}
          capitalize>
          Read Our PLAYBOOK
        </UixText>
        <RightArrow />
      </View>
      <Image
        source={require('@assets/playbook.png')}
        style={{
          width: '100%',
          height: '100%',
          resizeMode: 'contain',
          flex: 1,
        }}
      />
    </Pressable>
  );
}
