import StoriesIcon from '@assets/stories.svg';
import {RootState} from '@store/store';
import {useMemo} from 'react';
import {Pressable, View} from 'react-native';
import {useSelector} from 'react-redux';

import {Tracker} from '@/app/analytics/tracker';
import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixText} from '@/app/ui/UixText';
import {HomeStoriesQuery} from '@/graphQL/home';
import {HomeStoriesQueryQuery} from '@/types/__generated__/graphql';

export default function HomeStoriesWidget() {
  const userDetails = useSelector((store: RootState) => store.user);
  const {data} = useUixQuery<HomeStoriesQueryQuery>(HomeStoriesQuery, {
    variables: {
      member_id: userDetails.id,
    },
  });

  const numUnseenStories = useMemo(() => {
    return (
      data?.stories_story_groups.filter(storyGroup =>
        storyGroup.story_details.some(
          story => !story.seen_by_aggregate.aggregate?.count,
        ),
      ).length ?? 0
    );
  }, [data]);

  const onPress = () => {
    Tracker.track('Home_Stories_Clicked', {
      unseen_stories_count: numUnseenStories,
    });
    if (numUnseenStories === 0) {
      NavigationService.navigate(ScreenNames.StoryTimeline);
    } else {
      NavigationService.navigate(ScreenNames.Stories);
    }
  };

  return (
    <Pressable onPress={onPress}>
      <View
        style={{
          borderWidth: 1,
          width: 44,
          height: 44,
          borderColor: numUnseenStories ? Colors.Yellow : Colors.White800,
          borderRadius: 24,
          position: 'relative',
          backgroundColor: updateColorWithOpacity('#000000', 0.5),
        }}>
        <StoriesIcon />
        {numUnseenStories > 0 && (
          <View
            style={{
              backgroundColor: Colors.Black,
              padding: 4,
              position: 'absolute',
              right: 26,
              borderRadius: 30,
              bottom: 0,
            }}>
            <View
              style={{
                backgroundColor: Colors.Yellow,
                paddingBottom: 1,
                borderRadius: 30,
                paddingHorizontal: 4,
              }}>
              <UixText
                variant="dotsBold"
                style={{
                  color: 'black',
                  textAlign: 'center',
                  fontSize: 12,
                  lineHeight: 12,
                }}>
                {`${numUnseenStories}`}
              </UixText>
            </View>
          </View>
        )}
      </View>
    </Pressable>
  );
}
