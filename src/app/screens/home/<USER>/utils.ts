import { Colors } from "@/app/theme/colors";
import { updateColorWithOpacity } from "@/app/theme/utils";

export const leadStatusIcons = {
    ACTIVE: require('@assets/approved.png'),
    CREATED: require('@assets/created.png'),
    STALE: require('@assets/pending.png'),
    NOT_HAPPENING: require('@assets/Cross-default.png'),
    DEAL_CLOSED: require('@assets/dealClosed.png'),
    UNASSIGNED: require('@assets/not-found.png'),
  };
  
  export const leadStatusColors = {
    ACTIVE: {
      background: updateColorWithOpacity(Colors.Teal400, 0.1),
      text: Colors.Teal425,
    },
    CREATED: {
      background: updateColorWithOpacity(Colors.CreatedBg, 0.2),
      text: Colors.headerText,
    },
    STALE: {
      background: updateColorWithOpacity(Colors.Yellow150, 0.08),
      text: Colors.Yellow150,
    },
    NOT_HAPPENING: {
      background: updateColorWithOpacity(Colors.Logout, 0.08),
      text: Colors.Logout,
    },
    DEAL_CLOSED: {
      background: updateColorWithOpacity(Colors.Teal400, 0.1),
      text: Colors.Teal425,
    },
    UNASSIGNED: {
      background: updateColorWithOpacity(Colors.CreatedBg, 0.2),
      text: Colors.headerText,
    },
  };