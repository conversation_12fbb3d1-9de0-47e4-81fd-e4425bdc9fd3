import {
  Stories_Story_Details,
  Stories_Story_Groups,
} from '@/types/__generated__/graphql';

export const getStoryGroups = (data: Stories_Story_Groups[]) => {
  const groupMap = new Map<
    string,
    {count: number; story: Stories_Story_Details}
  >();

  for (const group of data) {
    const title = group?.title;
    if (!title) continue;
    const unseenStories = group.story_details.filter(
      story => !story.seen_by_aggregate.aggregate?.count,
    );
    if (unseenStories.length) {
      groupMap.set(title, {
        count: unseenStories.length,
        story: unseenStories[0],
      });
    }
  }

  if (groupMap.size === 0) return [];

  const titles = Array.from(groupMap.keys());

  return Array.from({length: Math.max(titles.length, 3)}, (_, index) => {
    if (index < titles.length) {
      const title = titles[index];
      const {count, story} = groupMap.get(title)!;
      return {
        isDummy: false,
        title,
        count,
        story,
      };
    } else {
      return {
        isDummy: true,
        title: 'Stay tuned\nfor more',
      };
    }
  });
};
