import {addTimelogs} from '@graphQL/timelog';
import {updateTimelogs} from '@graphQL/timelog';
import {RootState} from '@store/store';
import dayjs from 'dayjs';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  Image,
  ImageBackground,
  Keyboard,
  Pressable,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {ToastOptions, useToast} from 'react-native-toast-notifications';
import {createStyleSheet, useStyles} from 'react-native-unistyles';
import {useSelector} from 'react-redux';

import {ProjectsBottomSheet} from './ProjectsBottomSheet';
import {TimelogCounter} from './TimelogCounter';

import {Tracker} from '@/app/analytics/tracker';
import Seperator from '@/app/baseComponents/Seperator/Seperator';
import {SuccessToast} from '@/app/baseComponents/Toast/Toast';
import useUixMutation from '@/app/hooks/useUixMutation';
import {Colors} from '@/app/theme/colors';
import {FullScreenModal} from '@/app/ui/FullScreenModal';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {UixTextInput} from '@/app/ui/UixTextInput';
import {
  Members_Member_Projects,
  Members_Member_Timelogs,
} from '@/types/__generated__/graphql';

type SelectedDay = 'today' | 'yesterday';

interface TimelogEntry {
  id: string;
  hours: number;
  description: string;
}

interface TimelogBottomModalProps {
  isOpen: boolean;
  toggleModal: () => void;
  onCompleted: () => void;
  initialTimelog?: Members_Member_Timelogs | null;
}

export function TimelogBottomModal({
  isOpen,
  toggleModal,
  onCompleted,
  initialTimelog,
}: TimelogBottomModalProps) {
  const toastIdRef = useRef('');
  const [selectedItem, setSelectedItem] =
    useState<Members_Member_Projects | null>(null);
  const [selectedDay, setSelectedDay] = useState<SelectedDay>('today');
  const [timelogEntries, setTimelogEntries] = useState<TimelogEntry[]>([
    {id: '1', hours: 0, description: ''},
  ]);

  const {styles} = useStyles(stylesheet);

  const insets = useSafeAreaInsets();
  const toast = useToast();
  const [error, setError] = useState<string>('');
  const [isProjectSelectorVisible, setProjectSelectorVisible] = useState(false);

  const userDetails = useSelector((store: RootState) => store.user);
  const [addTimelog, {loading: addLoading}] = useUixMutation(addTimelogs);
  const [updateTimelog, {loading: updateLoading}] =
    useUixMutation(updateTimelogs);

  const loading = addLoading || updateLoading;

  // Initialize data when modal opens or initialTimelog changes
  useEffect(() => {
    if (isOpen && initialTimelog) {
      // Set project from initialTimelog
      if (initialTimelog.project_detail) {
        setSelectedItem({
          project_detail: {
            id: initialTimelog.project_detail.id,
            project_name: initialTimelog.project_detail.project_name,
            // Add other required fields if needed
          },
        } as Members_Member_Projects);
      }

      // Set day based on actual_date
      if (initialTimelog.actual_date) {
        const timelogDate = dayjs(initialTimelog.actual_date);
        const today = dayjs();
        const yesterday = dayjs().subtract(1, 'day');

        if (timelogDate.isSame(today, 'day')) {
          setSelectedDay('today');
        } else if (timelogDate.isSame(yesterday, 'day')) {
          setSelectedDay('yesterday');
        } else {
          // If it's neither today nor yesterday, default to today
          setSelectedDay('today');
        }
      }

      // Set timelog entry
      setTimelogEntries([
        {
          id: initialTimelog.id?.toString() || '1',
          hours: initialTimelog.no_of_hours || 0,
          description: initialTimelog.work_description || '',
        },
      ]);
    } else if (isOpen && !initialTimelog) {
      // Reset to default state when opening for new entry
      setSelectedItem(null);
      setSelectedDay('today');
      setTimelogEntries([{id: '1', hours: 0, description: ''}]);
      setError('');
    }
  }, [isOpen, initialTimelog]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedItem(null);
      setSelectedDay('today');
      setTimelogEntries([{id: '1', hours: 0, description: ''}]);
      setError('');
    }
  }, [isOpen]);

  const addNewTimelogEntry = () => {
    setTimelogEntries(prev => [
      ...prev,
      {
        id: `${Date.now()}`,
        hours: 0,
        description: '',
      },
    ]);
    setError('');
  };

  const updateTimelogEntry = (
    id: string,
    key: 'hours' | 'description',
    value: number | string,
  ) => {
    setTimelogEntries(prev =>
      prev.map(entry => (entry.id === id ? {...entry, [key]: value} : entry)),
    );
    if (error === 'entries') {
      setError('');
    }
  };

  const toastOptions: ToastOptions = useMemo(() => {
    return {
      placement: 'top',
      duration: 3000,
      animationType: 'slide-in',
      textStyle: {
        fontSize: 16,
        color: Colors.Yellow750,
      },
      style: {
        backgroundColor: '#192608',
        padding: 16,
        borderRadius: 8,
        marginHorizontal: 16,
        zIndex: 9999,
        marginTop: insets.top + 20,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
      },
    };
  }, []);

  const showToast = useCallback(
    (message: string) => {
      if (toastIdRef.current) {
        toast.hide(toastIdRef.current);
      }
      setTimeout(() => {
        toastIdRef.current = toast.show(
          <SuccessToast message={message} />,
          toastOptions,
        );
      }, 100);
    },
    [toast, toastOptions],
  );

  const hasEntriesToSubmit = timelogEntries.some(
    entry => entry.hours > 0 && entry.description.trim().length > 0,
  );

  const isEditMode = Boolean(initialTimelog);

  const submitTimelog = async () => {
    Keyboard.dismiss();

    const validEntries = timelogEntries.filter(
      entry => entry.hours > 0 && entry.description.trim().length > 0,
    );

    if (!selectedItem?.project_detail?.id) {
      setError('project');
      return;
    }

    if (validEntries.length === 0) {
      setError('entries');
      return;
    }

    const actualDate =
      selectedDay === 'today'
        ? dayjs().format('YYYY-MM-DD')
        : dayjs().subtract(1, 'day').format('YYYY-MM-DD');

    try {
      // Separate entries for update vs create
      const entriesToUpdate = validEntries.filter(
        entry => isEditMode && entry.id === initialTimelog?.id?.toString(),
      );
      const entriesToCreate = validEntries.filter(
        entry => !(isEditMode && entry.id === initialTimelog?.id?.toString()),
      );

      // Handle updates (still individual calls as update API)
      for (const entry of entriesToUpdate) {
        await updateTimelog({
          variables: {
            id: entry.id,
            work_description: entry.description,
            no_of_hours: entry.hours,
          },
        });

        Tracker.track('Timelog_Update', {
          timelog_id: entry.id,
          member_id: userDetails?.id,
          alias: userDetails?.alias || '',
          project_id: selectedItem.project_detail?.id,
          no_of_hours: entry.hours,
        });
      }

      // Handle new entries
      if (entriesToCreate.length > 0) {
        const newEntriesData = entriesToCreate.map(entry => ({
          actual_date: actualDate,
          member_id: userDetails?.id,
          no_of_hours: entry.hours,
          project_id: selectedItem.project_detail?.id,
          work_description: entry.description,
          status: 'PENDING',
        }));

        await addTimelog({
          variables: {
            data: newEntriesData,
          },
        });

        // Track each new entry
        entriesToCreate.forEach(entry => {
          Tracker.track('Timelog_Addition', {
            actual_date: actualDate,
            member_id: userDetails?.id,
            alias: userDetails?.alias || '',
            project_id: selectedItem.project_detail?.id,
            no_of_hours: entry.hours,
          });
        });
      }

      showToast(
        isEditMode
          ? 'Timelog has been updated successfully'
          : 'Timelog has been added successfully',
      );

      toggleModal?.();
      onCompleted?.();
    } catch (error) {
      console.error('Error submitting timelogs:', error);
    }
  };

  return (
    <FullScreenModal isOpen={isOpen} onClose={toggleModal}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <UixButton
          label="Close"
          iconImage={require('@assets/cross.png')}
          onPress={toggleModal}
          tintColor={'White'}
        />

        <View style={styles.headerContainer}>
          <UixText variant="dotsBold" style={styles.headerText} capitalize>
            {`${isEditMode ? 'EDIT' : 'ADD'} ${selectedDay === 'today' ? 'TODAY' : 'YESTERDAY'}\nTIMELOG`}
          </UixText>

          <Image
            style={styles.headerImage}
            source={require('@assets/timelog.png')}
          />
        </View>

        {/* Project Selector */}
        <UixText variant="dotsBold" style={styles.sectionTitle} capitalize>
          SELECT PROJECT
        </UixText>

        <Pressable onPress={() => setProjectSelectorVisible(true)}>
          <View
            style={[
              styles.projectSelector,
              error === 'project' && styles.projectSelectorError,
            ]}>
            <UixText variant="titleMedium">
              {selectedItem?.project_detail?.project_name ?? 'Select Project'}
            </UixText>
            <Image
              source={require('@assets/right-arrow.png')}
              tintColor={'white'}
              style={styles.arrowIcon}
            />
          </View>
        </Pressable>

        <UixText variant="dotsBold" style={styles.sectionTitle} capitalize>
          SELECT DAY
        </UixText>

        <View style={styles.daySelector}>
          {(['today', 'yesterday'] as const).map(day => (
            <TouchableOpacity
              key={day}
              onPress={() => setSelectedDay(day)}
              style={[
                styles.dayButton,
                {
                  backgroundColor:
                    selectedDay === day ? Colors.Yellow : Colors.White950,
                },
              ]}>
              <UixText
                style={[
                  styles.dayButtonText,
                  {color: selectedDay === day ? Colors.Black : Colors.White300},
                ]}>
                {day.charAt(0).toUpperCase() + day.slice(1)}
              </UixText>
              <Image
                source={
                  selectedDay === day
                    ? require('@assets/radio-true.png')
                    : require('@assets/radioFalse.png')
                }
                style={styles.radioIcon}
              />
            </TouchableOpacity>
          ))}
        </View>

        <UixText variant="dotsBold" style={styles.sectionTitle} capitalize>
          ADD HOURS & DESCRIPTION
        </UixText>

        {timelogEntries.map(entry => (
          <ImageBackground
            key={entry.id}
            style={styles.timelogEntry}
            imageStyle={styles.timelogEntryImage}
            source={require('@assets/background-bottom.png')}>
            <TimelogCounter
              hours={entry.hours}
              setHours={val => updateTimelogEntry(entry.id, 'hours', val)}
            />
            <UixTextInput
              keyboardAppearance="dark"
              mode="outlined"
              value={entry.description}
              onChange={({nativeEvent: {text}}) =>
                updateTimelogEntry(entry.id, 'description', text)
              }
              style={styles.textInput}
              multiline
              numberOfLines={4}
              placeholder="Tell us what you worked on today…"
              placeholderTextColor={Colors.White600}
            />
            <Seperator />
          </ImageBackground>
        ))}

        {/* Show "Add Another Log" button in both create and edit modes */}
        <TouchableOpacity
          onPress={addNewTimelogEntry}
          style={styles.addAnotherButton}>
          <UixText
            variant="dotsBold"
            style={styles.addAnotherButtonText}
            capitalize>
            + ADD ANOTHER LOG
          </UixText>
        </TouchableOpacity>

        {error === 'entries' && (
          <UixText style={styles.errorText}>
            Please add at least one complete timelog entry
          </UixText>
        )}
      </ScrollView>

      <RoundedButton
        label={isEditMode ? 'UPDATE' : 'SUBMIT'}
        loading={loading}
        disabled={!hasEntriesToSubmit}
        onPress={submitTimelog}
        style={[styles.submitButton, {paddingBottom: insets.bottom + 20}]}
        center
      />

      <ProjectsBottomSheet
        isOpen={isProjectSelectorVisible}
        toggleModal={() => setProjectSelectorVisible(false)}
        selectedProject={selectedItem}
        setSelectedProject={setSelectedItem}
      />
    </FullScreenModal>
  );
}

const stylesheet = createStyleSheet(() => ({
  scrollContainer: {
    padding: 16,
    gap: 16,
    paddingBottom: 32,
  },
  headerContainer: {
    flexDirection: 'row',
    marginTop: 16,
    justifyContent: 'space-between',
  },
  headerText: {
    color: Colors.White,
    fontSize: 32,
    lineHeight: 28,
  },
  headerImage: {
    width: 40,
    height: 40,
    objectFit: 'contain',
  },
  sectionTitle: {
    fontSize: 20,
    color: Colors.White300,
  },
  projectSelector: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: Colors.White950,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  projectSelectorError: {
    borderWidth: 2,
    borderColor: Colors.Red100,
  },
  arrowIcon: {
    width: 24,
    height: 24,
  },
  daySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dayButton: {
    borderRadius: 8,
    width: '48%',
    paddingVertical: 18,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayButtonText: {
    fontSize: 18,
  },
  radioIcon: {
    width: 22,
    height: 22,
    objectFit: 'contain',
  },
  timelogEntry: {
    gap: 16,
  },
  timelogEntryImage: {
    opacity: 0.5,
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    color: Colors.White,
    fontSize: 18,
    lineHeight: 24,
    height: 120,
    fontWeight: 'medium',
  },
  addAnotherButton: {
    paddingVertical: 12,
    backgroundColor: Colors.White950,
    borderRadius: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addAnotherButtonText: {
    fontSize: 20,
    color: Colors.White300,
  },
  errorText: {
    color: Colors.Red100,
    textAlign: 'center',
  },
  submitButton: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    backgroundColor: Colors.Black,
  },
}));
