import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootState} from '@store/store';
import React, {useMemo, useState} from 'react';
import {View} from 'react-native';
import {useSelector} from 'react-redux';

import {LogsBottomSheet} from './LogsBottomSheet';
import {TimelogBottomModal} from './TimelogBottomModal';

import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {RootStackParamsList} from '@/app/navigation/types';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {ButtonType, RoundedButton} from '@/app/ui/RoundedButton';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixText} from '@/app/ui/UixText';
import {getTimelogs} from '@/graphQL/timelog';
import {
  GetTimelogsQuery,
  Members_Member_Timelogs,
} from '@/types/__generated__/graphql';

type NavigationProp = NativeStackNavigationProp<RootStackParamsList>;

export function TimelogsSection() {
  const userDetails = useSelector((state: RootState) => state.user);
  const [isTimeLogModalVisible, setLogTimeModalVisible] = useState(false);
  const [isBottomSheetExpanded, setIsBottomSheetExpanded] = useState(false);

  const navigation = useNavigation<NavigationProp>();

  const timelogsResponse = useUixQuery<GetTimelogsQuery>(getTimelogs, {
    variables: {
      id: userDetails?.id,
    },
  });

  const todaysTimelogs = useMemo(
    () => timelogsResponse?.data?.members_member_timelogs,
    [timelogsResponse],
  );

  const toggleModal = async () => {
    setLogTimeModalVisible(!isTimeLogModalVisible);
  };

  const todaysLoggedTime: number = Math.max(
    todaysTimelogs
      ?.map(timelog => timelog?.no_of_hours)
      ?.reduce((partialSum, a) => partialSum + a, 0),
    0,
  );

  const ShowLogsButton = (
    <RoundedButton
      color={Colors.Yellow}
      type={ButtonType.SECONDARY}
      label={'Show Logs'}
      innerStyle={{height: 48}}
      onPress={async () =>
        navigation.navigate(ScreenNames.LogEntries, {
          totalLoggedTime: todaysLoggedTime,
        })
      }
    />
  );

  return (
    <View
      style={{
        marginHorizontal: 10,
        borderRadius: 8,
        backgroundColor: updateColorWithOpacity(Colors.Yellow, 0.1),
      }}>
      <View style={{gap: 20, padding: 10}}>
        <View style={{flexDirection: 'row'}}>
          <View
            style={{
              alignSelf: 'flex-start',
              alignItems: 'flex-start',
              justifyContent: 'space-between',
              flex: 1,
            }}>
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 20,
                lineHeight: 18,
                color: Colors.Yellow,
              }}
              capitalize>
              Total Hours
            </UixText>
            <View style={{flex: 1}} />
            {timelogsResponse.loading ? (
              <Shimmer
                height={24}
                width={100}
                shimmerColors={[
                  Colors.Yellow950,
                  updateColorWithOpacity(Colors.Yellow, 0.11),
                ]}
              />
            ) : (
              <UixText
                variant="dotsBold"
                style={{
                  fontSize: 32,
                  lineHeight: 28,
                  color: Colors.Yellow,
                }}>
                {todaysLoggedTime?.toFixed(1)}
              </UixText>
            )}
          </View>
          {ShowLogsButton}
        </View>
        <RoundedButton
          center
          color={Colors.Yellow}
          onPress={toggleModal}
          label="log time +"
        />
      </View>
      <LogsBottomSheet
        isVisible={isBottomSheetExpanded}
        onClose={() => setIsBottomSheetExpanded(false)}
        todaysTimelogs={todaysTimelogs as Members_Member_Timelogs[]}
      />
      <TimelogBottomModal
        isOpen={isTimeLogModalVisible}
        toggleModal={toggleModal}
        onCompleted={() => {
          navigation.navigate(ScreenNames.LogEntries, {
            totalLoggedTime: todaysLoggedTime,
          });
        }}
      />
    </View>
  );
}
