import {deleteLead} from '@graphQL/leads';
import React from 'react';
import {ActivityIndicator, Image, TouchableOpacity, View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import BottomSheet from '@/app/baseComponents/BottomSheet';
import useUixMutation from '@/app/hooks/useUixMutation';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {useToastUtils} from '@/app/utils/toast';

interface IProps {
  isVisible: boolean;
  onClose?: () => void;
  leadId: string;
  onDeleteCompleted?: () => void;
}

const DeleteLeadBottomSheet = ({
  isVisible,
  onClose,
  leadId,
  onDeleteCompleted,
}: IProps) => {
  const [deleteLeadMutation, {loading}] = useUixMutation(deleteLead);
  const {styles} = useStyles(styleSheet);
  const {showToast} = useToastUtils();

  const handleDelete = async () => {
    try {
      await deleteLeadMutation({
        variables: {
          id: leadId,
        },
        onCompleted: () => {
          onDeleteCompleted?.();
          showToast('Lead deleted successfully');
        },
      });
    } catch (error) {
      console.error('Error deleting lead:', error);
    }
  };

  return (
    <BottomSheet
      isVisible={isVisible}
      onClose={onClose}
      style={{backgroundColor: 'black'}}>
      <View style={styles.container}>
        <UixButton
          label="BACK"
          iconImage={require('@assets/left.png')}
          tintColor="Teal"
          viewStyle={styles.backButton}
          onPress={onClose}
        />
        <View style={styles.deleteContainer}>
          <UixText style={styles.descriptionHeading} variant="dotsBold">
            DELETE LEAD
          </UixText>

          <UixText style={styles.descriptionText}>
            Are you sure you want to delete this lead?
          </UixText>
        </View>

        <TouchableOpacity
          style={styles.deleteWrapper}
          onPress={() => {
            handleDelete();
          }}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={Colors.Red100} />
          ) : (
            <>
              <Image
                source={require('@assets/delete-default.png')}
                style={{width: 24, height: 24}}
              />
              <UixText variant="dotsBold" style={styles.deleteText}>
                DELETE
              </UixText>
            </>
          )}
        </TouchableOpacity>
      </View>
    </BottomSheet>
  );
};

export default DeleteLeadBottomSheet;

const styleSheet = createStyleSheet(() => ({
  deleteWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.Logout,
    padding: 14,
    justifyContent: 'center',
    borderRadius: 8,
    gap: 8,
  },

  deleteText: {
    fontSize: 20,
    color: '#02161F',
    textAlign: 'center',
  },

  descriptionText: {
    fontSize: 16,
    color: Colors.White,
  },

  deleteContainer: {
    gap: 24,
  },

  descriptionHeading: {
    fontSize: 28,
    color: Colors.White,
  },

  container: {
    padding: 16,
    gap: 24,
  },

  backButton: {
    marginBottom: 24,
    height: 40,
  },
}));
