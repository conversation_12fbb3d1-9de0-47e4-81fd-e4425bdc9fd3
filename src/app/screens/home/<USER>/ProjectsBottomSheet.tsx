import {RootState} from '@store/store';
import {useMemo} from 'react';
import React from 'react';
import {ActivityIndicator, Image, Pressable, View} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import Modal from 'react-native-modal';
import {useSelector} from 'react-redux';

import {ProjectListItem} from './ProjectListItem';

import useUixQuery from '@/app/hooks/useUixQuery';
import {UixText} from '@/app/ui/UixText';
import {MemberProject} from '@/graphQL/member';
import {
  MemberProjectQuery,
  Members_Member_Projects,
} from '@/types/__generated__/graphql';

interface ProjectsBottomSheetProps {
  isOpen: boolean;
  toggleModal: () => void;
  selectedProject?: Members_Member_Projects | null;
  setSelectedProject: React.Dispatch<
    React.SetStateAction<Members_Member_Projects | null>
  >;
}

export function ProjectsBottomSheet({
  isOpen,
  toggleModal,
  selectedProject,
  setSelectedProject,
}: ProjectsBottomSheetProps) {
  const userDetails = useSelector((store: RootState) => store.user);

  const {data, loading} = useUixQuery<MemberProjectQuery>(MemberProject, {
    variables: {
      id: userDetails?.id,
    },
  });

  if (loading) {
    return (
      <Modal isVisible={isOpen} onBackdropPress={toggleModal}>
        <ActivityIndicator size="large" />
      </Modal>
    );
  }

  return (
    <Modal
      isVisible={isOpen}
      onBackdropPress={() => {
        toggleModal();
      }}
      style={{
        margin: 0,
        justifyContent: 'flex-end',
      }}>
      <FlatList
        removeClippedSubviews={false}
        keyExtractor={item => item.project_detail?.id || ''}
        contentContainerStyle={{
          padding: 16,
          paddingBottom: 32,
          flexGrow: 1,
          backgroundColor: 'black',
        }}
        style={{flexGrow: 0}}
        ListHeaderComponent={() =>
          useMemo(
            () => (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                <Pressable onPress={toggleModal}>
                  <Image
                    style={{
                      width: 20,
                      height: 15,
                      resizeMode: 'contain',
                      marginRight: 15,
                    }}
                    source={require('@assets/leftArrow.png')}
                  />
                </Pressable>
                <UixText
                  variant="dotsBold"
                  style={{fontSize: 28, lineHeight: 24}}>
                  SELECT PROJECT
                </UixText>
              </View>
            ),
            [],
          )
        }
        data={data?.members_member_projects || []}
        renderItem={({item}) => {
          return (
            <ProjectListItem
              data={item}
              selected={
                item?.project_detail?.id === selectedProject?.project_detail?.id
              }
              setSelectedItem={setSelectedProject}
              toggleModal={toggleModal}
            />
          );
        }}
      />
    </Modal>
  );
}
