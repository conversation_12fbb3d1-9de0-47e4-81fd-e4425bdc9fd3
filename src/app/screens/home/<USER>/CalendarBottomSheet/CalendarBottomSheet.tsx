import dayjs from 'dayjs';
import React, {useCallback, useMemo, useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import DateTimePicker, {
  DateType,
  useDefaultStyles,
} from 'react-native-ui-datepicker';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import BottomSheet from '@/app/baseComponents/BottomSheet';
import Seperator from '@/app/baseComponents/Seperator/Seperator';
import {Colors} from '@/app/theme/colors';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';

export interface DateRange {
  startDate: DateType;
  endDate: DateType;
}

interface IProps {
  isVisible: boolean;
  onBottomSheetDown: () => void;
  onClose?: () => void;
  onApplyDateFilter?: (dateRange: DateRange | null) => void;
  currentDateRange?: DateRange | null;
}

const CalendarBottomSheet = ({
  isVisible,
  onClose,
  onApplyDateFilter,
  currentDateRange,
  onBottomSheetDown,
}: IProps) => {
  const [selected, setSelected] = useState<DateRange>({
    startDate: currentDateRange?.startDate,
    endDate: currentDateRange?.endDate,
  });

  const {styles} = useStyles(styleSheet);

  const defaultStyles = useDefaultStyles('dark');

  const formattedDates = useMemo(() => {
    const formatDate = (date: DateType) => {
      return date ? dayjs(date).format('DD/MM/YYYY') : '';
    };

    return {
      startDate: formatDate(selected.startDate),
      endDate: formatDate(selected.endDate),
    };
  }, [selected.startDate, selected.endDate]);

  const handleCancel = useCallback(() => {
    setSelected({
      startDate: currentDateRange?.startDate,
      endDate: currentDateRange?.endDate,
    });
    onClose?.();
  }, [currentDateRange?.startDate, currentDateRange?.endDate, onClose]);

  const handleApply = useCallback(async () => {
    if (selected.startDate && selected.endDate) {
      onApplyDateFilter?.(selected);
    } else {
      onApplyDateFilter?.(null);
    }
    onClose?.();
  }, [selected.startDate, selected.endDate, onApplyDateFilter, onClose]);

  return (
    <BottomSheet
      isVisible={isVisible}
      style={styles.bottomSheet}
      onClose={onBottomSheetDown}>
      <View style={styles.mainContainer}>
        <UixButton
          label="BACK"
          tintColor="Teal"
          onPress={onClose}
          iconImage={require('@assets/left.png')}
        />

        <View style={styles.container}>
          {selected.startDate && selected.endDate && (
            <>
              <View style={styles.dateLabelsContainer}>
                <View style={styles.labelContainer}>
                  <UixText variant="dotsBold" style={styles.labelText}>
                    FROM
                  </UixText>
                </View>
                <View style={styles.labelContainer}>
                  <UixText variant="dotsBold" style={styles.labelText}>
                    TO
                  </UixText>
                </View>
              </View>
              <View style={styles.dateWrapper}>
                <View style={styles.startDateContainer}>
                  <UixText style={styles.dateText}>
                    {formattedDates.startDate}
                  </UixText>
                </View>
                <View style={styles.dateSeparator}>
                  <UixText style={styles.dateText}>-</UixText>
                </View>
                <View style={styles.endDateContainer}>
                  <UixText style={styles.dateText}>
                    {formattedDates.endDate}
                  </UixText>
                </View>
              </View>
              <Seperator tintColor={'rgba(4,44,39,1)'} style={{height: 2}} />
            </>
          )}

          <DateTimePicker
            mode="range"
            styles={{
              ...defaultStyles,
              button_next: styles.navButton,
              button_next_image: styles.navButtonImage,
              button_prev: styles.navButton,
              button_prev_image: styles.navButtonImage,
              range_middle: styles.rangeMiddle,
              selected: styles.selectedDate,
              today: styles.todayDate,
              selected_label: styles.selectedLabel,
              day: styles.dayText,
            }}
            startDate={selected.startDate}
            endDate={selected.endDate}
            onChange={({startDate, endDate}) =>
              setSelected({startDate, endDate})
            }
          />
        </View>

        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.cancelButtonWrapper}
            onPress={handleCancel}>
            <UixText
              capitalize
              variant="dotsBold"
              style={styles.cancelButtonText}>
              CANCEL
            </UixText>
          </TouchableOpacity>

          <RoundedButton
            label="APPLY"
            style={styles.applyButton}
            center
            onPress={handleApply}
          />
        </View>
      </View>
    </BottomSheet>
  );
};

export default CalendarBottomSheet;

const styleSheet = createStyleSheet(() => ({
  bottomSheet: {
    backgroundColor: 'black',
  },
  mainContainer: {
    padding: 16,
    gap: 32,
  },
  container: {
    gap: 16,
    borderWidth: 1,
    backgroundColor: 'rgba(0, 255, 211, 0.12)',
    paddingHorizontal: 12,
    paddingVertical: 16,
    borderRadius: 8,
    borderBottomColor: 'rgba(0, 255, 211, 1)',
  },
  dateLabelsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    gap: 16,
  },
  labelContainer: {
    flex: 1,
  },
  labelText: {
    fontSize: 20,
    color: 'gray',
  },
  dateWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  startDateContainer: {
    flex: 1,
    backgroundColor: '#263230',
    paddingVertical: 13,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 4,
  },
  dateSeparator: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  endDateContainer: {
    flex: 1,
    backgroundColor: '#263230',
    paddingVertical: 13,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginLeft: 4,
  },
  dateText: {
    fontSize: 16,
    color: 'white',
  },
  navButton: {
    backgroundColor: 'transparent',
    padding: 8,
  },
  navButtonImage: {
    tintColor: 'white',
    width: 12,
    height: 12,
  },
  rangeMiddle: {
    backgroundColor: 'rgba(153, 224, 0, 0.10)',
  },
  selectedDate: {
    backgroundColor: Colors.Yellow750,
    borderRadius: 8,
    color: 'black',
    fontWeight: '600',
  },
  todayDate: {
    color: 'black',
  },
  selectedLabel: {
    color: 'white',
  },
  dayText: {
    color: 'black',
    fontSize: 16,
    fontWeight: '500',
  },
  buttonsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButtonWrapper: {
    borderWidth: 1.5,
    borderColor: Colors.Yellow150,
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
  },
  cancelButtonText: {
    color: Colors.Yellow150,
    fontSize: 20,
    textAlign: 'center',
  },
  applyButton: {
    flex: 1,
  },
}));
