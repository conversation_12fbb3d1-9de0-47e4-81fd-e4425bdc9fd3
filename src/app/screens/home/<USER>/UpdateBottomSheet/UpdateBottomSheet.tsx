import dayjs from 'dayjs';
import React, {useState} from 'react';
import {Image, TouchableOpacity, View} from 'react-native';
import {createStyleSheet, useStyles} from 'react-native-unistyles';

import DeleteBottomSheet from '../DeleteBottomSheet/DeleteBottomSheet';
import {renderStatusImage} from '../LogEntries/LogEntriesView';
import {TimelogBottomModal} from '../TimelogBottomModal';

import BottomSheet from '@/app/baseComponents/BottomSheet';
import {Colors} from '@/app/theme/colors';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Timelogs} from '@/types/__generated__/graphql';

interface IProps {
  isVisible: boolean;
  onClose?: () => void;
  data: Members_Member_Timelogs;
  onUpdateCompleted?: (updatedTimelog?: Members_Member_Timelogs) => void;
}

const UpdateBottomSheet = ({
  isVisible,
  onClose,
  data,
  onUpdateCompleted,
}: IProps) => {
  const [showTimelog, setShowTimelog] = useState(false);
  const [showDelete, setShowDelete] = useState(false);

  const {styles} = useStyles(styleSheet);

  const handleTimelogCompleted = (updatedTimelog?: Members_Member_Timelogs) => {
    setShowTimelog(false);
    onUpdateCompleted?.(updatedTimelog);
  };

  const handleDeleteCompleted = () => {
    setShowDelete(false);
    onClose?.();
    onUpdateCompleted?.();
  };

  return (
    <BottomSheet
      isVisible={isVisible}
      onClose={onClose}
      style={styles.bottomSheet}>
      <View style={styles.container}>
        <UixButton
          label="BACK"
          iconImage={require('@assets/left.png')}
          tintColor="Teal"
          viewStyle={styles.backButton}
          onPress={onClose}
        />

        <View style={styles.infoContainer}>
          <View style={styles.infoRowContainer}>
            <View style={styles.infoRow}>
              <View style={styles.projectIconContainer}>
                <Image
                  source={{uri: data?.project_detail?.project_logo ?? ''}}
                  style={styles.icon}
                  resizeMode="contain"
                />
              </View>

              <UixText variant="dotsBold" style={styles.infoText} capitalize>
                {`${data?.project_detail?.project_name}`}
              </UixText>
            </View>
            <View style={styles.infoRow}>
              <Image
                source={require('@assets/calendar-default.png')}
                style={styles.icon}
              />

              <UixText variant="dotsBold" style={styles.infoText} capitalize>
                {`${dayjs(data?.actual_date).format('DD MMMM YYYY')}`}
              </UixText>
            </View>
            <View style={styles.hoursStatusRow}>
              <View style={styles.hoursContainer}>
                <Image
                  source={require('@assets/clock.png')}
                  style={styles.clockIcon}
                />

                <UixText variant="dotsBold" style={styles.infoText} capitalize>
                  {`${data?.no_of_hours} HRS`}
                </UixText>
              </View>

              <View style={styles.statusContainer}>
                <View style={styles.statusIconContainer}>
                  {renderStatusImage(data?.status ?? 'PENDING')}
                </View>

                <UixText variant="dotsBold" style={styles.infoText} capitalize>
                  {`${data?.status}`}
                </UixText>
              </View>
            </View>
          </View>
          <View style={styles.descriptionContainer}>
            <UixText style={styles.description}>
              {`${data?.work_description}`}
            </UixText>

            <UixText style={styles.createdDate} capitalize>
              {`CREATED ON ${dayjs(data?.created_at).format('DD-MM-YYYY')}`}
            </UixText>
          </View>
        </View>

        <View style={styles.buttonsContainer}>
          <RoundedButton
            icon={require('@assets/edit-icon.png')}
            label="EDIT"
            iconPosition="left"
            center
            style={styles.editButton}
            onPress={async () => {
              setShowTimelog(true);
            }}
          />
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => setShowDelete(true)}>
            <Image source={require('@assets/delete.png')} style={styles.icon} />
          </TouchableOpacity>
        </View>
      </View>

      <TimelogBottomModal
        isOpen={showTimelog}
        toggleModal={() => setShowTimelog(false)}
        onCompleted={handleTimelogCompleted}
        initialTimelog={data}
      />

      <DeleteBottomSheet
        isVisible={showDelete}
        onClose={() => setShowDelete(false)}
        timelogId={data?.id ?? ''}
        onDeleteCompleted={handleDeleteCompleted}
      />
    </BottomSheet>
  );
};

const styleSheet = createStyleSheet(() => ({
  bottomSheet: {
    backgroundColor: 'black',
  },
  container: {
    padding: 16,
    gap: 24,
  },
  backButton: {
    marginBottom: 24,
    height: 40,
  },
  infoContainer: {
    gap: 24,
    borderBottomWidth: 2,
    borderBottomColor: Colors.Teal400,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,255,211,0.12)',
  },
  infoRowContainer: {
    gap: 8,
  },
  infoRow: {
    paddingHorizontal: 8,
    paddingVertical: 12,
    backgroundColor: '#00FFD314',
    gap: 8,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
  },
  hoursStatusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 6,
  },
  hoursContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#00FFD314',
    padding: 8,
    borderRadius: 6,
  },
  statusContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#00FFD314',
    padding: 8,
    borderRadius: 6,
    marginLeft: 6,
  },
  statusIconContainer: {
    marginRight: 8,
    borderRadius: 6,
  },
  icon: {
    width: 28,
    height: 28,
  },
  clockIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  infoText: {
    fontSize: 20,
    color: Colors.Teal425,
  },
  description: {
    fontSize: 16,
    color: 'white',
  },
  createdDate: {
    fontSize: 12,
    color: '#B8E5DE8F',
    fontWeight: 'bold',
  },
  buttonsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  editButton: {
    flex: 1,
  },
  deleteButton: {
    borderWidth: 1,
    borderColor: Colors.Logout,
    padding: 14,
    borderRadius: 8,
  },
  projectIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 6,
    backgroundColor: 'rgba(218,233,1,0.08)',
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },

  descriptionContainer: {
    gap: 24,
  },
}));

export default UpdateBottomSheet;
