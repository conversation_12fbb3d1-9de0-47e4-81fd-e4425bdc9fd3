import React from 'react';
import {Pressable} from 'react-native';
import Svg, {Circle} from 'react-native-svg';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {StoryMedia} from '@/app/screens/storyTimeline/components/StoryMedia';
import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import {Stories_Story_Details} from '@/types/__generated__/graphql';

interface Props {
  index: number;
  count: number;
  title: string;
  story: Stories_Story_Details;
}

export const COLORS = [
  Colors.Teal,
  Colors.Orange,
  Colors.Pink,
  Colors.Green,
  Colors.Blue,
  Colors.Yellow,
  Colors.Purple,
];

function StoryGroupItem(props: Props) {
  const {index = 0, count, story} = props;

  const color = COLORS[index % COLORS.length];

  return (
    <Pressable
      onPress={() => {
        NavigationService.navigate(ScreenNames.Stories, {
          id: story.id,
        });
      }}
      style={{
        height: 96,
        width: 96,
        overflow: 'hidden',
        borderRadius: 92,
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <Svg
        viewBox="0 0 100 100"
        strokeDashoffset={-5}
        strokeDasharray={
          count < 2 ? undefined : [(2 * 3.14 * 50) / count - 10, 10]
        }
        style={{
          backgroundColor: 'transparent',
          width: '100%',
          height: '100%',
          position: 'absolute',
        }}>
        <Circle stroke={color} strokeWidth={5} r={50} cx={50} cy={50} />
      </Svg>
      <UixText
        variant="dotsBold"
        style={{
          zIndex: 3,
          width: 80,
          fontSize: 16,
          lineHeight: 18,
          textAlign: 'center',
          color,
        }}
        capitalize>
        {props.title}
      </UixText>
      {story.cover_media && story.media_type && (
        <StoryMedia
          type={story.media_type}
          source={story.cover_media}
          resizeMode="contain"
          muted={true}
          repeat={true}
          style={{
            width: '80%',
            height: '80%',
            opacity: 0.5,
            position: 'absolute',
            zIndex: 2,
          }}
        />
      )}
    </Pressable>
  );
}

export default React.memo(StoryGroupItem);
