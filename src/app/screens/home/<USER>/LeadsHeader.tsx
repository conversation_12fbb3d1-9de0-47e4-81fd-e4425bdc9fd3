import React from 'react';
import {Image, View} from 'react-native';
import {useStyles} from 'react-native-unistyles';

import {styleSheet} from '../timelogs/LogEntries/LogEntries.styles';

import {UixText} from '@/app/ui/UixText';
import {Leads_Lead_Status_Enum} from '@/types/__generated__/graphql';

export const LeadsHeader = ({
  filterLeadsStatus,
  count,
}: {
  filterLeadsStatus: Leads_Lead_Status_Enum | null;
  count: number;
}) => {
  const {styles} = useStyles(styleSheet);

  return (
    <View style={styles.header}>
      <View style={styles.titleContainer}>
        <UixText variant="dotsBold" style={styles.title} capitalize>
          Leads
        </UixText>
        <View style={styles.subtitleContainer}>
          <UixText variant="dots" style={styles.subtitle}>
            {count?.toString().padStart(2, '0')}
          </UixText>
          <UixText variant="dots" style={styles.subtitle} capitalize>
            {filterLeadsStatus || 'ALL'}
          </UixText>
        </View>
      </View>

      <Image
        source={require('@assets/LeadsPage.png')}
        style={styles.timelogIcon}
      />
    </View>
  );
};
