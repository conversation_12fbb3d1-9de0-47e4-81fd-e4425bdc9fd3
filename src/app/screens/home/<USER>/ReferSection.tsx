import NewMember from '@assets/newMember.svg';
import NewProject from '@assets/star.svg';
import {useRoute} from '@react-navigation/native';
import React, {useEffect} from 'react';
import {Dimensions, View} from 'react-native';

import {ReferBox} from './ReferBox';
import ReferProjectForm from './ReferProjectForm';

import {usePageBuilder} from '@/app/context/PageBuilderContext';
import useDisclosure from '@/app/hooks/useDisclosure';
import {ReferMemberForm} from '@/app/screens/home/<USER>/ReferMemberForm';
import {Colors} from '@/app/theme/colors';
import {Shimmer} from '@/app/ui/Shimmer';

export function ReferSection() {
  const route = useRoute();
  const memberFormDisclosure = useDisclosure();
  const projectFormDisclosure = useDisclosure();

  useEffect(() => {
    const params = route.params as {screen?: string} | undefined;
    if (params?.screen === 'refer-member') {
      memberFormDisclosure.controls.open();
    } else if (params?.screen === 'refer-project') {
      projectFormDisclosure.controls.open();
    }
  }, [route.params]);
  const {data, loading} = usePageBuilder();
  const screenWidth = Dimensions.get('window').width;

  if (loading) {
    return (
      <View style={{marginHorizontal: 10}}>
        <Shimmer width={screenWidth - 20} height={100} />
      </View>
    );
  }

  return (
    <View style={{marginHorizontal: 10}}>
      <View style={{flexDirection: 'row', gap: 10}}>
        <ReferBox
          color={Colors.Purple}
          label={`Refer\nNew Project`}
          Icon={NewProject}
          onPress={() => {
            projectFormDisclosure.controls.open();
          }}
        />

        {data?.members_member_details?.[0]?.type?.name !== 'Partner' && (
          <ReferBox
            color={Colors.Orange}
            label={`Refer\nNew Member`}
            Icon={NewMember}
            onPress={() => {
              memberFormDisclosure.controls.open();
            }}
          />
        )}
      </View>
      <ReferMemberForm modalDisclosure={memberFormDisclosure} />
      <ReferProjectForm modalDisclosure={projectFormDisclosure} />
    </View>
  );
}
