import {RootState} from '@store/store';
import React, {useMemo} from 'react';
import {Image, Pressable, StyleSheet, View} from 'react-native';
import {useSelector} from 'react-redux';

import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixCornerView} from '@/app/ui/UixCornerView';
import {UixText} from '@/app/ui/UixText';
import {getLeadStatusCount} from '@/graphQL/leads';
import {GetLeadStatusCountQuery} from '@/types/__generated__/graphql';

export default function LeadsSection() {
  const onPress = () => {
    NavigationService.navigate(ScreenNames.Leads);
  };

  const userDetails = useSelector((state: RootState) => state.user);

  const {data} = useUixQuery<GetLeadStatusCountQuery>(getLeadStatusCount, {
    fetchPolicy: 'cache-and-network',
    variables: {
      id: userDetails.id,
      status: 'ACTIVE',
    },
  });

  const activeLeadsCount =
    data?.members_member_details_by_pk?.lead_dealmaker_hunters_aggregate
      ?.aggregate?.count || 0;

  const formattedCount = useMemo(() => {
    return activeLeadsCount.toString().padStart(2, '0');
  }, [activeLeadsCount]);

  return (
    <Pressable style={styles.container} onPress={onPress}>
      <View style={styles.parentCountContainer}>
        <View style={styles.countContainer}>
          <UixText variant="dotsBold" style={styles.countText}>
            {formattedCount}
          </UixText>
        </View>
      </View>

      <View style={styles.textContainer}>
        <UixText variant="dotsBold" style={styles.text}>
          ACTIVE
        </UixText>
        <UixText variant="dotsBold" style={styles.text}>
          LEADS
        </UixText>
      </View>

      <View style={styles.viewButtonContainer}>
        <UixCornerView color={Colors.Yellow750}>
          <View style={styles.viewButton}>
            <UixText
              variant="dotsBold"
              style={styles.viewButtonText}
              capitalize>
              View
            </UixText>
            <Image
              style={styles.arrowIcon}
              source={require('@assets/rightArrow.png')}
            />
          </View>
        </UixCornerView>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 76,
    flexDirection: 'row',
    backgroundColor: '#0C1A0E',
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 10,
    marginVertical: 8,
  },
  parentCountContainer: {
    padding: 8,
    backgroundColor: updateColorWithOpacity(Colors.Yellow750, 0.08),
    borderRadius: 8,
    marginLeft: 10,
  },
  viewButtonContainer: {
    marginRight: 10,
  },
  countContainer: {
    width: 72,
    height: 44,
    backgroundColor: updateColorWithOpacity(Colors.Yellow750, 0.1),
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  countText: {
    fontSize: 48,
    color: Colors.Yellow750,
    lineHeight: 40,
  },
  textContainer: {
    flexDirection: 'column',
    marginLeft: 12,
    flex: 1,
  },
  text: {
    fontSize: 20,
    color: Colors.Yellow750,
    lineHeight: 24,
  },
  viewButton: {
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
    paddingRight: 12,
    paddingLeft: 16,
  },
  viewText: {
    fontSize: 16,
    color: '#0C1A0E',
    marginRight: 4,
  },
  arrowIcon: {
    width: 24,
    height: 24,
    tintColor: '#0C1A0E',
  },
  viewButtonText: {
    fontSize: 20,
    lineHeight: 18,
    color: Colors.Black,
  },
});
