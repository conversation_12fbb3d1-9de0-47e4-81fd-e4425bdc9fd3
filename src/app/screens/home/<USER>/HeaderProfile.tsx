import ArrowRight from '@assets/arrowRight.svg';
import React from 'react';
import {Image, Pressable, View} from 'react-native';

import {createDynamicUrl} from '../../utils';

import {usePageBuilder} from '@/app/context/PageBuilderContext';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {AvailabilityMarquee} from '@/app/ui/AvailabilityMarquee';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixCornerView} from '@/app/ui/UixCornerView';
import {UixImage} from '@/app/ui/uixImage/UixImage';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Details} from '@/types/__generated__/graphql';

export function HeaderProfile() {
  const {data, loading} = usePageBuilder();
  const memberData = data?.members_member_details?.[0];
  const isMemberActive = (memberData?.member_status ?? 0) % 2 === 1;

  return (
    <Pressable
      onPress={() => {
        if (!memberData) {
          return;
        }
        NavigationService.navigate(ScreenNames.MemberDetail, {
          id: memberData?.id,
          member: memberData as Members_Member_Details,
        });
      }}
      style={{
        height: 118,
        overflow: 'hidden',
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: Colors.Black,
      }}>
      <View style={{flex: 1}}>
        <UixText
          variant="titleMedium"
          style={{
            fontWeight: 'medium',
            paddingHorizontal: 10,
            color: Colors.White700,
          }}>
          Welcome back,
        </UixText>

        {loading ? (
          <Shimmer width={100} height={32} style={{margin: 10}} />
        ) : (
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 40,
              padding: 16,
              paddingLeft: 10,
              lineHeight: 28,
              textShadowColor: updateColorWithOpacity(Colors.Teal, 0.7),
              textShadowOffset: {width: 0, height: 0}, // No shadow displacement
              textShadowRadius: 24,
              color: Colors.Teal,
            }}
            capitalize>
            {`${memberData?.alias}`}
          </UixText>
        )}
        <View
          style={{
            paddingHorizontal: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            shadowColor: Colors.Teal,
            shadowOpacity: 1,
            shadowRadius: 60,
            shadowOffset: {width: 0, height: 8},
            backgroundColor: 'transparent',
            zIndex: 3,
            elevation: 12,
          }}>
          {!loading
            ? memberData?.member_badges?.slice(0, 3).map((badgeObj, index) => {
                return (
                  <View key={'badge-' + index} style={{marginRight: 12}}>
                    <Image
                      style={{width: 32, height: 32, borderRadius: 16}}
                      source={{uri: badgeObj?.badge_detail?.badge_media ?? ''}}
                    />
                  </View>
                );
              })
            : [1, 2, 3].map((data, i) => {
                return (
                  <View key={'badge-loader-' + i} style={{marginRight: 12}}>
                    <Shimmer
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                      }}
                    />
                  </View>
                );
              })}
          {!loading && (memberData?.member_badges.length ?? 0) > 3 && (
            <View
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: Colors.Teal950,
              }}>
              <ArrowRight />
            </View>
          )}
        </View>
      </View>
      <UixCornerView
        viewStyle={{marginRight: 10}}
        cut={0.15}
        background={
          <View>
            {loading ? (
              <Shimmer style={{width: 118, height: 118}} />
            ) : (
              <View>
                {memberData?.member_photo ? (
                  <UixImage
                    size={118}
                    source={{
                      uri: createDynamicUrl(
                        memberData?.member_photo,
                        isMemberActive ? '00FFD1' : 'transparent',
                      ),
                    }}
                    style={{width: 118, height: 118}}
                  />
                ) : (
                  <UixImage
                    size={118}
                    source={
                      isMemberActive
                        ? require('@assets/avatars/default.png')
                        : require('@assets/avatars/defaultGrayscale.png')
                    }
                    style={{width: 118, height: 118}}
                  />
                )}
              </View>
            )}
            {!loading && <AvailabilityMarquee isActive={isMemberActive} />}
          </View>
        }>
        <View style={{width: 118, height: 118}} />
      </UixCornerView>
    </Pressable>
  );
}
