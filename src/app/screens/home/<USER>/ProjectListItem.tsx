import React from 'react';
import {Image, Pressable} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Projects} from '@/types/__generated__/graphql';

type ProjectDetail = {
  __typename?: 'projects_project_details';
  project_name: string;
  id: string;
  project_logo?: string | null;
};

type PartialMemberProject = {
  __typename?: 'members_member_projects';
  project_detail?: ProjectDetail | null;
};

export function ProjectListItem({
  data,
  selected,
  setSelectedItem,
  toggleModal,
}: {
  data?: PartialMemberProject;
  selected: boolean;
  setSelectedItem: React.Dispatch<
    React.SetStateAction<Members_Member_Projects | null>
  >;
  toggleModal: () => void;
}) {
  return (
    <Pressable
      style={{
        padding: 20,
        backgroundColor: Colors.White1000,
        marginBottom: 12,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderRadius: 8,
      }}
      onPress={() => {
        if (data) {
          setSelectedItem(data as Members_Member_Projects);
        }
        toggleModal();
      }}>
      <UixText
        variant="titleMedium"
        style={{
          color: selected ? Colors.Yellow : Colors.White300,
          fontWeight: '500',
        }}>
        {`${data?.project_detail?.project_name}`}
      </UixText>
      {selected ? (
        <Image
          source={require('@assets/radioTrue.png')}
          style={{width: 22, height: 22, objectFit: 'contain'}}
        />
      ) : (
        <Image
          source={require('@assets/radioFalse.png')}
          style={{width: 22, height: 22, objectFit: 'contain'}}
        />
      )}
    </Pressable>
  );
}
