import MinusIcon from '@assets/minus.svg';
import PlusIcon from '@assets/plus.svg';
import {useEffect, useState} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {UixTextInput} from '@/app/ui/UixTextInput';

interface TimelogCounterProps {
  hours: number;
  hourUnit?: number;
  limit?: number;
  disabled?: boolean;
  setHours: (hours: number) => void;
}

const MAX_DECIMAL_PLACES = 2;
const DEFAULT_HOUR_UNIT = 0.25;
const DEFAULT_LIMIT = 2;

export function TimelogCounter({
  hours,
  hourUnit = DEFAULT_HOUR_UNIT,
  setHours,
  limit = DEFAULT_LIMIT,
  disabled = false,
}: TimelogCounterProps) {
  const [minusDisabled, setMinusDisabled] = useState(false);
  const [plusDisabled, setPlusDisabled] = useState(false);
  const [inputValue, setInputValue] = useState(`${hours}`);

  useEffect(() => {
    setMinusDisabled(hours <= 0);
    setPlusDisabled(hours >= limit);
    setInputValue(`${hours}`);
  }, [hours, limit]);

  const handleDecrement = () => {
    setHours(Math.max(0, hours - hourUnit));
  };

  const handleIncrement = () => {
    setHours(Math.min(limit, hours + hourUnit));
  };

  const validateAndUpdateInput = (text: string) => {
    // Allow empty input for backspacing
    if (text === '') {
      setInputValue('');
      setHours(0);
      return;
    }

    // Validate input format (numbers and one decimal point)
    const isValidFormat = /^\d*\.?\d*$/.test(text);
    if (!isValidFormat) return;

    // Check decimal places
    const decimalPlaces = text.split('.')[1]?.length || 0;
    if (decimalPlaces > MAX_DECIMAL_PLACES) return;

    // Validate number range
    const num = parseFloat(text);
    if (!isNaN(num) && num > limit) return;

    // Update states
    setInputValue(text);
    if (text !== '.') {
      setHours(num);
    }
  };

  return (
    <View style={styles.container}>
      <Pressable
        disabled={disabled || minusDisabled}
        style={styles.button}
        onPress={handleDecrement}>
        <MinusIcon opacity={minusDisabled || disabled ? 0.3 : 1} />
      </Pressable>

      <View style={styles.inputContainer}>
        <UixTextInput
          style={[styles.input]}
          value={inputValue}
          keyboardType="numeric"
          onChangeText={validateAndUpdateInput}
          mode="outlined"
        />
      </View>

      <Pressable
        disabled={disabled || plusDisabled}
        style={styles.button}
        onPress={handleIncrement}>
        <PlusIcon opacity={plusDisabled || disabled ? 0.3 : 1} />
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 12,
    gap: 16,
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 60,
    width: 60,
    borderRadius: 12,
    backgroundColor: Colors.Yellow,
  },
  inputContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.White950,
    borderRadius: 12,
  },
  input: {
    backgroundColor: Colors.White950,
    color: Colors.White,
    fontSize: 32,
    lineHeight: 36,
    textAlign: 'center',
    width: '100%',
    fontWeight: 'semibold',
    textAlignVertical: 'center',
  },
});
