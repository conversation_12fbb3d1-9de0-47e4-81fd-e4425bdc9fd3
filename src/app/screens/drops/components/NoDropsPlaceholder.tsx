import NewMember from '@assets/newMember.svg';
import React from 'react';
import {View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

export function NodropsPlaceholder({
  selectedFilter,
  loading,
}: {
  selectedFilter: string;
  loading: boolean;
}) {
  if (loading) return undefined;
  return (
    <View style={{padding: 16, paddingTop: 64}}>
      <NewMember height={40} width={40} />
      <View style={{height: 12}} />
      <UixText variant="titleLarge" style={{color: Colors.White400}}>
        {`There are no drops for “${selectedFilter.charAt(0) + selectedFilter.slice(1).toLowerCase()}”`}
      </UixText>
      <UixText variant="titleLarge" style={{color: Colors.White400}}>
        at the moment.
      </UixText>
    </View>
  );
}
