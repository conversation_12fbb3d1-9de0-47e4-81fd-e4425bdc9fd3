import {Shimmer} from '@/app/ui/Shimmer';
import {Pressable, ScrollView, View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

export function DropsHeader({
  loading,
  selectedFilter,
  filters,
  dropsCount,
  setSelectedFilter,
}: {
  loading: boolean;
  selectedFilter: string;
  filters: string[];
  dropsCount: number;
  setSelectedFilter: (filter: string) => void;
}) {
  return (
    <View style={{marginBottom: 20}}>
      <UixText
        variant="dotsBold"
        style={{
          fontSize: 32,
          lineHeight: 24,
          color: Colors.Teal,
          padding: 8,
          paddingBottom: 4,
        }}
        capitalize>
        {`${selectedFilter} drops`}
      </UixText>
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        {loading ? (
          <Shimmer style={{height: 36, width: 250, borderRadius: 6}} />
        ) : (
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 32,
              lineHeight: 20,
              color: Colors.Yellow,
              textShadowColor: Colors.Yellow700,
              textShadowOffset: {width: 0, height: 0},
              textShadowRadius: 10,
              padding: 8,
            }}
            capitalize>{`${dropsCount} New DropS`}</UixText>
        )}
      </View>
      <ScrollView
        horizontal
        nestedScrollEnabled
        showsHorizontalScrollIndicator={false}>
        <View style={{flexDirection: 'row', gap: 8, marginTop: 16}}>
          {filters.map(filter => (
            <Pressable
              key={filter}
              style={{
                padding: 8,
                paddingHorizontal: 16,
                borderRadius: 18,
                backgroundColor:
                  selectedFilter === filter
                    ? filter === 'INTERESTED'
                      ? Colors.Yellow
                      : Colors.Teal
                    : filter === 'INTERESTED'
                      ? Colors.Yellow950
                      : Colors.Teal950,
              }}
              onPress={() => setSelectedFilter(filter)}>
              <UixText
                variant="dotsBold"
                style={{
                  fontSize: 20,
                  lineHeight: 18,
                  color:
                    selectedFilter === filter
                      ? 'black'
                      : filter === 'INTERESTED'
                        ? '#FFD600'
                        : '#00FFD1',
                }}>
                {filter}
              </UixText>
            </Pressable>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}
