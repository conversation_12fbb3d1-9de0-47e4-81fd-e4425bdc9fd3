import Svg, {Path, SvgProps} from 'react-native-svg';

export const HourIcon = ({fill = '#00FFD3', ...props}: SvgProps) => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.7897 2V2.68943H15.1002H14.4108H13.7214H13.032H12.3426H11.6531H10.9637H10.2743H9.58485H8.89542H8.20601V2H7.51658H6.82715V2.68943V3.37884H7.51658V4.06827V4.75768V5.44711V6.13655V6.82948V7.51891V8.20834V8.89775V9.58718H8.20601H8.89542V8.89775V8.20834V7.51891V6.82948V6.13655V5.44711V4.75768H9.58485H10.2743H10.9637H11.6531H12.3426H13.032H13.7214H14.4108H15.1002V5.44711V6.13655V6.82948V7.51891V8.20834V8.89775V9.58718H15.7897H16.4791V8.89775V8.20834V7.51891V6.82948V6.13655V5.44711V4.75768V4.06827V3.37884H17.172V2.68943V2H16.4791H15.7897ZM15.1002 15.7945V16.4839V17.1733V17.8627V18.5522V19.2416H14.4108V18.5522H13.7214V19.2416H13.032V18.5527H13.7212V17.8633H13.0317V18.5522H12.3428V17.8633H11.6533V18.5527H12.3426V19.2416H11.6531V18.5522H10.9638V17.8633H10.2744V18.5527H10.9637V19.2416H10.2743V18.5522H9.58485V19.2416H8.89542V18.5522V17.8627V17.1733V16.4839V15.7945V15.1051V14.4121H8.20601H7.51658V15.1051V15.7945V16.4839V17.1733V17.8627V18.5522V19.2416V19.931V20.6204H6.82715V21.3099V21.9993H7.51658H8.20601V21.3099H8.89542H9.58485H10.2743H10.9637H11.6531H12.3426H13.032H13.7214H14.4108H15.1002H15.7897V21.9993H16.4791H17.172V21.3099V20.6204H16.4791V19.931V19.2416V18.5522V17.8627V17.1733V16.4839V15.7945V15.1051V14.4121H15.7897H15.1002V15.1051V15.7945ZM14.4106 14.414H13.7212V13.7246V13.0352H14.4106H15.1V13.7246V14.414H14.4106ZM13.7212 9.58789V10.2773V10.9667H14.4106H15.1V10.2773V9.58789H14.4106H13.7212ZM13.7212 8.20703H14.4106V8.89644H13.7212V8.20703ZM13.0317 8.89844H13.7212V9.58787H13.0317V8.89844ZM13.7212 7.51953H13.0317V8.20703H12.3428V8.89644H13.0322V8.20896H13.7212V7.51953ZM12.3426 17.1738H13.032V17.8632H12.3426V17.1738ZM12.3428 10.9668V11.6562V12.3456V13.0351H13.0322H13.7216V12.3456V11.6562V10.9668H13.0322H12.3428ZM11.6533 16.4844H12.3428V17.1738H11.6533V16.4844ZM12.3428 15.1055H11.6533V15.7949H12.3428V15.1055ZM11.6533 13.7246H12.3428V14.414H11.6533V13.7246ZM12.3428 10.2773H11.6533V10.9668H12.3428V10.2773ZM11.6533 8.89844H12.3428V9.58787H11.6533V8.89844ZM12.3428 7.51953H11.6533V8.20896H12.3428V7.51953ZM10.9637 17.1738H11.6531V17.8632H10.9637V17.1738ZM11.6533 13.0351V12.3456V11.6562V10.9668H10.9638H10.2744V11.6562V12.3456V13.0351H10.9638H11.6533ZM10.9639 8.20703H11.6533V8.89644H10.9639V8.20703ZM10.2744 8.89844H10.9638V9.58787H10.2744V8.89844ZM10.9638 7.51953H10.2744V8.20896H10.9638V7.51953ZM10.2744 13.7246V14.414H9.58494H8.89551V13.7246V13.0352H9.58494H10.2744V13.7246ZM10.2744 9.58789H9.58494H8.89551V10.2773V10.9667H9.58494H10.2744V10.2773V9.58789ZM9.58496 8.20703H10.2744V8.89644H9.58496V8.20703Z"
      fill={fill}
    />
  </Svg>
);

export const ThunderIcon = ({fill = '#FFD600', ...props}: SvgProps) => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      d="M12.5954 3.33984V4.66929H11.2659V6.00748H9.9365V7.33693H8.5983V8.66637H7.26886V10.0046H5.93066V12.6722H7.26886V14.0017H11.2659V15.3311H9.9365V17.9987H8.5983V20.6664H11.2659V19.3282H12.5954V17.9987H13.9336V16.6693H15.263V15.3311H16.5925V14.0017H17.9307V11.334H16.5925V10.0046H13.9336V8.66637H15.263V7.33693H16.5925V6.00748H17.9307V3.33984H12.5954Z"
      fill={fill}
    />
  </Svg>
);
