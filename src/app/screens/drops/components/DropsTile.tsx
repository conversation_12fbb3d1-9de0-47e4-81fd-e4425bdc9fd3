import ArrowRight from '@assets/arrowRight.svg';
import React from 'react';
import {Pressable, View} from 'react-native';

import {getDropExpiryTime} from '../utils';
import {DropsTagRow} from './DropsTag';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {GrayOverlay} from '@/app/ui/GrayOverlay';
import {UixCornerView} from '@/app/ui/UixCornerView';
import {UixImage} from '@/app/ui/uixImage/UixImage';
import {UixText} from '@/app/ui/UixText';
import {Drops_Drop_Details} from '@/types/__generated__/graphql';

export default function DropsTile({drop}: {drop: Drops_Drop_Details}) {
  const onPress = () => {
    NavigationService.navigate(ScreenNames.DropDetail, {id: drop.id});
  };

  const expiry = getDropExpiryTime(drop.expires_at);
  const expired = getDropExpiryTime(drop.expires_at) === 'Expired';

  return (
    <Pressable
      style={{
        marginBottom: 16,
        borderRadius: 8,
        padding: 16,
        borderTopLeftRadius: 30,
        backgroundColor: expired ? Colors.White1000 : Colors.Teal1000,
      }}
      onPress={onPress}>
      <View style={{position: 'absolute', left: 0, top: 0, zIndex: 10}}>
        <UixCornerView
          color={expired ? Colors.White950 : Colors.Teal950}
          cut={0.4}>
          <UixText
            variant="dotsBold"
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              fontSize: 20,
              lineHeight: 20,
              color: expired ? Colors.White200 : Colors.Teal,
            }}
            capitalize>
            {`${expiry}`}
          </UixText>
        </UixCornerView>
      </View>
      <View
        style={{
          borderRadius: 8,
          overflow: 'hidden',
          width: '100%',
          height: 144,
          marginBottom: 16,
        }}>
        <UixImage
          resizeMode="cover"
          source={{uri: drop.media_url || ''}}
          style={{
            width: '100%',
            height: '100%',
          }}
        />
        {expired && <GrayOverlay />}
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <UixText
          style={{
            fontSize: 20,
            color: Colors.White,
            fontWeight: 'bold',
            flexShrink: 1,
          }}>
          {drop?.title}
        </UixText>
        <ArrowRight fill={'white'} />
      </View>
      <UixText
        variant="titleMedium"
        style={{
          marginVertical: 12,
          lineHeight: 20,
          color: expired ? Colors.White800 : Colors.Teal100,
        }}
        numberOfLines={3}>
        {drop.drop_summary}
      </UixText>
      <DropsTagRow drop={drop} expired={expired} />
    </Pressable>
  );
}
