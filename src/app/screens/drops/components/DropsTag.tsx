import {View} from 'react-native';
import {SvgProps} from 'react-native-svg';

import {HourIcon, ThunderIcon} from './DropsIcons';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import {Drops_Drop_Details} from '@/types/__generated__/graphql';

const DropsTag = ({
  Icon,
  label,
  backgroundColor,
  expand,
  expired = false,
}: {
  Icon?: React.FC<SvgProps>;
  label: string;
  backgroundColor: 'Yellow' | 'Teal';
  expand?: boolean;
  expired?: boolean;
}) => {
  return (
    <View
      style={[
        {
          backgroundColor: expired
            ? Colors.White950
            : backgroundColor === 'Teal'
              ? Colors.Teal950
              : Colors.Yellow950,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 6,
          paddingHorizontal: 8,
          paddingVertical: 8,
          flexDirection: 'row',
          gap: 4,
          paddingRight: 12,
          overflow: 'hidden',
        },
        expand && {flex: 1},
      ]}>
      {Icon && (
        <Icon
          fill={
            expired
              ? Colors.White400
              : backgroundColor === 'Teal'
                ? Colors.Teal
                : Colors.Yellow
          }
        />
      )}
      <UixText
        variant="dotsBold"
        style={{
          fontSize: 20,
          lineHeight: 18,
          color: expired
            ? Colors.White300
            : backgroundColor === 'Teal'
              ? Colors.Teal
              : Colors.Yellow,
        }}>
        {label}
      </UixText>
    </View>
  );
};

export function DropsTagRow({
  drop,
  expired = false,
}: {
  drop: Drops_Drop_Details;
  expired?: boolean;
}) {
  return (
    <View style={{flexDirection: 'row', gap: 10}}>
      <DropsTag
        label={drop.surge}
        backgroundColor="Yellow"
        Icon={ThunderIcon}
        expired={expired}
      />
      {drop.engagement_type && (
        <DropsTag
          label={drop.engagement_type}
          backgroundColor="Teal"
          Icon={HourIcon}
          expired={expired}
        />
      )}
      {drop.type && (
        <DropsTag
          label={drop.type}
          backgroundColor="Teal"
          expired={expired}
          expand
        />
      )}
    </View>
  );
}
