import React from 'react';
import {View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixCornerView} from '@/app/ui/UixCornerView';

function DropsTileShimmer() {
  return (
    <UixCornerView
      color={Colors.Teal1000}
      viewStyle={{
        marginBottom: 16,
        padding: 16,
        display: 'flex',
        flexDirection: 'column',
        gap: 16,
      }}
      cut={0.05}
      type="tl"
      borderRadius={8}>
      <View style={{flexGrow: 1, justifyContent: 'space-between', gap: 16}}>
        <UixCornerView
          background={
            <Shimmer style={{height: 144, width: '100%', borderRadius: 8}} />
          }
          cut={0.03}
          type="tl"
          borderRadius={8}>
          <View style={{height: 144, width: '100%'}}></View>
        </UixCornerView>
        <View style={{height: 20}} />
        <Shimmer style={{height: 24, width: 80, borderRadius: 6}} />
        <Shimmer style={{height: 32, borderRadius: 6}} />
      </View>
    </UixCornerView>
  );
}

export function DropsScreenShimmer() {
  return (
    <View>
      <DropsTileShimmer />
      <DropsTileShimmer />
      <DropsTileShimmer />
    </View>
  );
}
