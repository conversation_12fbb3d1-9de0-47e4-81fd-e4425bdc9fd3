import {DocumentPickerResponse} from '@react-native-documents/picker';
import {RootState} from '@store/store';
import {useEffect, useState} from 'react';
import {
  Image,
  Keyboard,
  NativeSyntheticEvent,
  StyleSheet,
  TextInputChangeEventData,
  View,
} from 'react-native';
import {useSelector} from 'react-redux';

import ResumeUploader from '../../../ui/resumeUploader/ResumeUploader';
import ReferFormSuccess from '../../home/<USER>/ReferFormSuccess';

import useDisclosure from '@/app/hooks/useDisclosure';
import useUixMutation from '@/app/hooks/useUixMutation';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {FullScreenModal} from '@/app/ui/FullScreenModal';
import {LinkedinSelector} from '@/app/ui/linkedInSelector/LinkedInSelector';
import {RoundedButton} from '@/app/ui/RoundedButton';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {UixTextInput} from '@/app/ui/UixTextInput';
import attachResume from '@/app/utils/addResume';
import {sendDropInterestEmail} from '@/app/utils/email-integration';
import isValidEmail from '@/app/utils/isValidEmail';
import {splitFullName} from '@/app/utils/nameUtils';
import {notifyNewDropInterest} from '@/app/utils/slackNotifications';
import {createDropReferral} from '@/graphQL/referrals';
import {
  DropReferMemberMutation,
  DropReferMemberMutationVariables,
  Drops_Drop_Details,
} from '@/types/__generated__/graphql';

interface DropReferFormState {
  values: {
    name: string;
    linkedin: string;
    phone_number: string;
    referred_by: string;
    resume_link: string;
    email: string;
  };
  errors: {
    name: boolean;
    linkedin: boolean;
    phone_number: boolean;
    resume_link: boolean;
    email: boolean;
  };
  emailError: string;
  isSubmitting: boolean;
  isUploading: boolean;
  resumeFile: DocumentPickerResponse | undefined;
}

export function DropReferMemberModal({
  isOpen,
  drop,
  onClose,
  showUploadResume = true,
}: {
  isOpen: boolean;
  drop: Drops_Drop_Details;
  onClose: () => void;
  showUploadResume?: boolean;
}) {
  const successScreenDisclosure = useDisclosure();

  const userDetails = useSelector((store: RootState) => store.user);
  const [mutation, {loading}] = useUixMutation<
    DropReferMemberMutation,
    DropReferMemberMutationVariables
  >(createDropReferral);

  const initialState = {
    values: {
      name: '',
      linkedin: '',
      phone_number: '',
      referred_by: userDetails.id,
      resume_link: '',
      email: '',
    },
    errors: {
      name: false,
      linkedin: false,
      phone_number: false,
      resume_link: false,
      email: false,
    },
    emailError: '',
    isSubmitting: false,
    isUploading: false,
    resumeFile: undefined,
  };

  const [formState, setFormState] = useState<DropReferFormState>(initialState);

  async function onSubmit() {
    Keyboard.dismiss();
    if (loading) {
      return;
    }

    if (formState.values.email.toLowerCase().includes('@uixlabs')) {
      setFormState(prev => ({
        ...prev,
        emailError: `You can't onboard or refer an existing community member.`,
        isSubmitting: true,
        errors: {...prev.errors, email: true},
      }));
      return;
    }

    // Reset emailError at the beginning of a new submission attempt
    setFormState(prev => ({...prev, emailError: ''}));

    const formErrors = {
      phone_number: !formState.values.phone_number,
      linkedin: !formState.values.linkedin,
      name: !formState.values.name,
      email: !isValidEmail(formState.values.email),
      resume_link: !formState.resumeFile,
    };

    if (Object.values(formErrors).some(error => error)) {
      setFormState(prev => ({
        ...prev,
        isSubmitting: true,
        errors: formErrors,
      }));
      return;
    }

    try {
      setFormState(prev => ({...prev, isUploading: true}));

      const {firstName, lastName} = splitFullName(formState.values.name);

      if (!formState.resumeFile) {
        return;
      }

      const response = await attachResume(formState.resumeFile);

      if (!response.secure_url) {
        setFormState(prev => ({
          ...prev,
          errors: {
            ...prev.errors,
            resume_link: true,
          },
        }));
        return;
      }

      // Now submit the complete form with the actual resume link
      await mutation({
        variables: {
          first_name: firstName,
          last_name: lastName,
          drop_id: drop.id,
          linkedin_url: formState.values.linkedin,
          referred_by: userDetails.id,
          phone_number: `+91${formState.values.phone_number}`,
          resume_link: response.secure_url,
          description: drop.title,
          email: formState.values.email.toLowerCase(),
        },
        onCompleted: data => {
          if (data?.insert_drops_drop_members_one?.id) {
            notifyNewDropInterest(data.insert_drops_drop_members_one.id);
            sendDropInterestEmail({
              id: data?.insert_drops_drop_members_one?.id,
            });
          }
          setFormState(initialState);
          successScreenDisclosure.controls.open();
        },
        onError: error => {
          if (error.message.includes('member_referrals_email_key')) {
            setFormState(prev => ({
              ...prev,
              emailError: 'This email address has already been registered.',
              isUploading: false,
            }));
          } else {
            setFormState(prev => ({...prev, isUploading: false}));
          }
        },
      });
    } catch (e) {
      console.error('Error in form submission:', e);
    } finally {
      setFormState(prev => ({...prev, isUploading: false}));
    }
  }

  useEffect(() => {
    setFormState(prev => ({
      ...prev,
      errors: {
        phone_number: !formState.values.phone_number && formState.isSubmitting,
        linkedin: !formState.values.linkedin && formState.isSubmitting,
        name: !formState.values.name && formState.isSubmitting,
        resume_link: !formState.resumeFile && formState.isSubmitting,
        email: !formState.values.email && formState.isSubmitting,
      },
    }));

    if (
      formState.values.name &&
      formState.values.linkedin &&
      formState.values.phone_number &&
      formState.resumeFile &&
      formState.values.email &&
      isValidEmail(formState.values.email)
    ) {
      setFormState(prev => ({...prev, isSubmitting: false}));
    }
  }, [formState.values, formState.isSubmitting, formState.resumeFile]);

  return (
    <FullScreenModal
      isOpen={isOpen}
      onClose={() => {
        setFormState(prev => ({...prev, isSubmitting: false}));
        onClose();
      }}>
      {successScreenDisclosure.opened ? (
        <ReferFormSuccess
          onClose={() => {
            setFormState(prev => ({...prev, isSubmitting: false}));
            onClose();
            NavigationService.goBack();
            NavigationService.goBack();
          }}
          icon={require('@assets/memberTitle.png')}
          baseColor={'Teal'}
          text={`THANK YOU\nFOR\nREFERRING`}
        />
      ) : (
        <View style={{paddingHorizontal: 16, gap: 12}}>
          <View style={{flexGrow: 1}} />
          <UixButton
            label="Close"
            iconImage={require('@assets/cross.png')}
            onPress={() => {
              setFormState(prev => ({...prev, isSubmitting: false}));
              onClose();
            }}
            tintColor={'Teal'}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: 20,
            }}>
            <UixText
              variant="dotsBold"
              style={{fontSize: 32, lineHeight: 32, color: Colors.Teal}}
              capitalize>{`REFER A\nFRIEND`}</UixText>
            <Image
              source={require('@assets/diamond.png')}
              style={{width: 40, height: 40}}
            />
          </View>
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 20,
              lineHeight: 16,
              marginTop: 20,
              color: Colors.Teal,
            }}
            capitalize>{`FULL NAME`}</UixText>
          <UixTextInput
            mode="outlined"
            placeholder="eg. Rahul Sharma"
            textContentType="name"
            outlineColor={formState.errors.name ? Colors.Error : Colors.Teal950}
            activeOutlineColor={
              formState.errors.name ? Colors.Error : Colors.Teal
            }
            textColor={Colors.Teal}
            value={formState.values.name}
            onChange={({
              nativeEvent: {text},
            }: NativeSyntheticEvent<TextInputChangeEventData>) => {
              setFormState(prev => ({
                ...prev,
                values: {...prev.values, name: text},
              }));
            }}
            placeholderTextColor={Colors.Teal800}
            style={{backgroundColor: Colors.Teal950}}
          />
          <LinkedinSelector
            value={formState.values.name}
            onChange={(text: string) => {
              setFormState(prev => ({
                ...prev,
                values: {...prev.values, linkedin: text},
              }));
            }}
            color="Teal"
            errored={formState.errors.linkedin}
          />

          <UixText variant="dotsBold" style={styleSheet.email}>
            EMAIL
          </UixText>

          <UixTextInput
            mode="outlined"
            placeholder="<EMAIL>"
            keyboardType="email-address"
            autoCapitalize="none"
            outlineColor={
              formState.errors.email ? Colors.Error : Colors.Teal950
            }
            activeOutlineColor={
              formState.errors.email ? Colors.Error : Colors.Teal
            }
            textColor={Colors.Teal}
            value={formState.values.email}
            onChange={({
              nativeEvent: {text},
            }: NativeSyntheticEvent<TextInputChangeEventData>) => {
              setFormState(prev => ({
                ...prev,
                values: {...prev.values, email: text},
                emailError: '',
              }));
            }}
            placeholderTextColor={Colors.Teal800}
            style={{backgroundColor: Colors.Teal950}}
          />
          {formState.emailError ? (
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 14,
                color: Colors.Error,
                marginTop: 4,
              }}>
              {formState.emailError}
            </UixText>
          ) : null}

          <UixText variant="dotsBold" style={styleSheet.email} capitalize>
            {'Phone Number'}
          </UixText>
          <UixTextInput
            mode="outlined"
            placeholder="XXX-XXX-XXXX"
            textContentType="telephoneNumber"
            outlineColor={
              formState.errors.phone_number ? Colors.Error : Colors.Teal950
            }
            activeOutlineColor={
              formState.errors.phone_number ? Colors.Error : Colors.Teal
            }
            keyboardType="numeric"
            inputMode="numeric"
            maxLength={10}
            textColor={Colors.Teal}
            value={formState.values.phone_number}
            onChange={({
              nativeEvent: {text},
            }: NativeSyntheticEvent<TextInputChangeEventData>) => {
              setFormState(prev => ({
                ...prev,
                values: {...prev.values, phone_number: text},
              }));
            }}
            placeholderTextColor={Colors.Teal800}
            style={{backgroundColor: Colors.Teal950}}
          />

          {showUploadResume && (
            <View style={{display: 'flex', rowGap: 8}}>
              <UixText variant="dotsBold" style={styleSheet.resume} capitalize>
                UPLOAD RESUME
              </UixText>

              <ResumeUploader
                backgroundColor={Colors.Teal950}
                color={formState.resumeFile ? Colors.Teal : Colors.Teal800}
                onFileChange={file => {
                  setFormState(prev => ({
                    ...prev,
                    resumeFile: file,
                  }));
                }}
                errored={formState.errors.resume_link}
              />
            </View>
          )}
          {!successScreenDisclosure.opened && (
            <RoundedButton
              label="Submit"
              loading={loading || formState.isUploading}
              disabled={formState.isSubmitting}
              onPress={onSubmit}
              style={{paddingTop: 16, marginTop: 'auto'}}
              center
            />
          )}
        </View>
      )}
    </FullScreenModal>
  );
}

const styleSheet = StyleSheet.create({
  email: {
    fontSize: 20,
    lineHeight: 16,
    marginTop: 20,
    color: Colors.Teal,
  },

  emailError: {
    color: Colors.Error,
    marginTop: 4,
  },

  resume: {
    fontSize: 20,
    lineHeight: 16,
    marginTop: 20,
    color: Colors.Teal,
  },
});
