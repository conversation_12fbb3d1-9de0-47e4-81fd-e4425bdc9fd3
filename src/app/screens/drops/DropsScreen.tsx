import {useQuery} from '@apollo/client';
import {RootState} from '@store/store';
import dayjs from 'dayjs';
import React, {useCallback, useMemo, useState} from 'react';
import {ListRenderItemInfo, View} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import {useSelector} from 'react-redux';

import {DropsHeader} from './components/DropsHeader';
import {DropsScreenShimmer} from './components/DropsScreenShimmer';
import DropsTile from './components/DropsTile';
import {NodropsPlaceholder} from './components/NoDropsPlaceholder';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {UixButton} from '@/app/ui/UixButton';
import DropsList from '@/graphQL/drop';
import {Drops_Drop_Details} from '@/types/__generated__/graphql';

const PAGE_SIZE = 5;

export default function DropsScreen() {
  const userDetails = useSelector((store: RootState) => store.user);
  const {data, loading, fetchMore} = useQuery<{
    drops_drop_details: Array<Drops_Drop_Details>;
  }>(DropsList, {
    variables: {
      limit: PAGE_SIZE,
      offset: 0,
    },
  });

  const filters = ['DEV', 'DESIGN', 'PRODUCT', 'AI'];
  const [selectedFilter, setSelectedFilter] = useState('ALL');
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const renderItem = useCallback(
    (d: ListRenderItemInfo<Drops_Drop_Details>) => {
      const {item} = d;
      return <DropsTile drop={item} />;
    },
    [],
  );

  const drops = useMemo(() => {
    return (
      data?.drops_drop_details
        ?.filter(drop =>
          filters.includes(selectedFilter)
            ? drop.type === selectedFilter
            : drop.drop_members.some(
                member => member.member_id === userDetails.id,
              ) || selectedFilter === 'ALL',
        )
        ?.sort(
          (a, b) =>
            dayjs(b.created_at).valueOf() - dayjs(a.created_at).valueOf(),
        ) ?? []
    );
  }, [data, selectedFilter, userDetails.id]);

  const allFilters = useMemo(
    () =>
      (data?.drops_drop_details.some(drop =>
        drop.drop_members.some(({member_id}) => member_id === userDetails.id),
      )
        ? ['ALL', 'INTERESTED']
        : ['ALL']
      ).concat(filters),
    [data, userDetails.id],
  );

  const handleLoadMore = useCallback(() => {
    if (!loading && data?.drops_drop_details) {
      const currentLength = data.drops_drop_details.length;

      // Only fetch more if we have exactly PAGE_SIZE items in the current page
      if (currentLength > 0 && currentLength % PAGE_SIZE === 0) {
        setIsLoadingMore(true);
        fetchMore({
          variables: {
            offset: currentLength,
            limit: PAGE_SIZE,
          },
          updateQuery: (prev, {fetchMoreResult}) => {
            if (!fetchMoreResult) return prev;

            // Filter out any potential duplicates based on id
            const existingIds = new Set(
              prev.drops_drop_details.map(drop => drop.id),
            );
            const newDrops = fetchMoreResult.drops_drop_details.filter(
              drop => !existingIds.has(drop.id),
            );

            return {
              ...prev,
              drops_drop_details: [...prev.drops_drop_details, ...newDrops],
            };
          },
        });
      } else {
        setIsLoadingMore(false);
      }
    }
  }, [data, loading, fetchMore, isLoadingMore]);

  return (
    <ScreenContainer name={ScreenNames.Drops} style={{padding: 16}}>
      <UixButton
        label="Back"
        iconImage={require('@assets/backArrow.png')}
        onPress={() => {
          NavigationService.goBack();
        }}
        tintColor={'Teal'}
        viewStyle={{marginBottom: 24}}
      />
      <FlatList
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        keyExtractor={item => item.id.toString()}
        style={{flex: 1}}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={1}
        ListHeaderComponent={
          <View>
            <DropsHeader
              loading={loading}
              selectedFilter={selectedFilter}
              filters={allFilters}
              dropsCount={data?.drops_drop_details?.length ?? 0}
              setSelectedFilter={setSelectedFilter}
            />
            {loading && <DropsScreenShimmer />}
          </View>
        }
        ListEmptyComponent={
          <NodropsPlaceholder
            selectedFilter={selectedFilter}
            loading={loading}
          />
        }
        ListFooterComponent={isLoadingMore ? <DropsScreenShimmer /> : null}
        data={drops}
        removeClippedSubviews={false}
      />
    </ScreenContainer>
  );
}
