import {useRoute} from '@react-navigation/native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootState} from '@store/store';
import React, {useMemo, useState} from 'react';
import {View} from 'react-native';
import {useSelector} from 'react-redux';

import {DropReferMemberModal} from './components/DropsReferMemberModal';
import {getDropExpiryTime} from './utils';

import {invalidateQueries} from '@/app/apollo/cacheUtils';
import useUixMutation from '@/app/hooks/useUixMutation';
import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {RootStackParamsList} from '@/app/navigation/types';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {DropsTagRow} from '@/app/screens/drops/components/DropsTag';
import {Colors} from '@/app/theme/colors';
import {ButtonType, RoundedButton} from '@/app/ui/RoundedButton';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixButton} from '@/app/ui/UixButton';
import {UixImage} from '@/app/ui/uixImage/UixImage';
import {UixText} from '@/app/ui/UixText';
import {sendDropInterestEmail} from '@/app/utils/email-integration';
import {notifyNewDropInterest} from '@/app/utils/slackNotifications';
import {createDropInterest, DropById, DropInterestsQuery} from '@/graphQL/drop';
import {
  AddDropInterestMutation,
  AddDropInterestMutationVariables,
  DropByIdQuery,
  DropInterestQueryQuery,
  Drops_Drop_Details,
} from '@/types/__generated__/graphql';

type NavigationProps = NativeStackScreenProps<
  RootStackParamsList,
  ScreenNames.DropDetail
>;

export default function DropDetailsScreen() {
  const route = useRoute<NavigationProps['route']>();
  const userDetails = useSelector((store: RootState) => store.user);

  const {data, loading} = useUixQuery<DropByIdQuery>(DropById, {
    variables: {
      dropId: route.params.id,
    },
  });

  const interestQuery = useUixQuery<DropInterestQueryQuery>(
    DropInterestsQuery,
    {
      variables: {
        drop_id: route.params.id,
        member_id: userDetails.id,
      },
    },
  );

  const drop = useMemo(() => {
    if (loading || !data) return undefined;
    return data.drops_drop_details[0];
  }, [data, loading]);

  const [createInterest] = useUixMutation<
    AddDropInterestMutation,
    AddDropInterestMutationVariables
  >(createDropInterest);

  const [isMutating, setIsMutating] = useState(false);
  const [referFormOpen, setReferFormOpen] = useState(false);

  const handleInterest = async () => {
    try {
      setIsMutating(true);
      await createInterest({
        variables: {
          drop_id: drop!.id,
          member_id: userDetails.id,
        },
        onCompleted: data => {
          if (data?.insert_drops_drop_members_one?.id) {
            notifyNewDropInterest(data.insert_drops_drop_members_one.id);

            sendDropInterestEmail({
              id: data.insert_drops_drop_members_one.id,
            });
          }

          invalidateQueries.drops();
        },
      });
      await interestQuery.refetch();
    } catch (e) {
      console.error('Error creating interest:', e);
    } finally {
      setIsMutating(false);
    }
  };

  const isExpired = drop && getDropExpiryTime(drop.expires_at) === 'Expired';

  return (
    <ScreenContainer
      name={ScreenNames.DropDetail}
      disableBackground
      enableKeyboardScrollView
      style={{
        paddingHorizontal: 16,
        backgroundColor: Colors.DarkBackground,
      }}>
      <View>
        <View style={{flexGrow: 1, gap: 16}}>
          <UixButton
            label="Close"
            iconImage={require('@assets/cross.png')}
            onPress={() => NavigationService.goBack()}
            tintColor="Teal"
          />
          {drop ? (
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 32,
                lineHeight: 32,
                marginVertical: 8,
                color: Colors.Teal,
              }}
              capitalize>
              {drop.title}
            </UixText>
          ) : (
            <Shimmer style={{height: 40, borderRadius: 4}} />
          )}
          {drop ? (
            <UixImage
              source={{uri: drop.media_url || ''}}
              style={{
                width: '100%',
                height: 144,
                borderRadius: 8,
              }}
            />
          ) : (
            <Shimmer style={{width: '100%', height: 144, borderRadius: 8}} />
          )}
          {drop ? (
            <DropsTagRow drop={drop as Drops_Drop_Details} />
          ) : (
            <View style={{flexDirection: 'row', gap: 16}}>
              <Shimmer style={{flex: 1, height: 40, borderRadius: 8}} />
              <Shimmer style={{flex: 1, height: 40, borderRadius: 8}} />
              <Shimmer style={{flex: 1.5, height: 40, borderRadius: 8}} />
            </View>
          )}
          {drop ? (
            <UixText
              variant="titleMedium"
              style={{
                lineHeight: 20,
                color: Colors.Teal100,
              }}>
              {drop.drop_summary}
            </UixText>
          ) : (
            <View style={{gap: 8}}>
              <Shimmer style={{width: '100%', borderRadius: 4}} />
              <Shimmer style={{width: '100%', borderRadius: 4}} />
              <Shimmer style={{width: '100%', borderRadius: 4}} />
              <Shimmer style={{width: '100%', borderRadius: 4}} />
              <Shimmer style={{width: '100%', borderRadius: 4}} />
              <Shimmer style={{borderRadius: 4}} />
            </View>
          )}
        </View>
        {!isExpired && (
          <View style={{paddingVertical: 16}}>
            {interestQuery.loading || !drop ? (
              <Shimmer
                style={{
                  width: '100%',
                  height: 56,
                  borderRadius: 8,
                }}
              />
            ) : interestQuery.data?.drops_drop_members.length === 0 ? (
              <View style={{gap: 16}}>
                <RoundedButton
                  label="REFER A FRIEND"
                  type={ButtonType.SECONDARY}
                  center
                  style={{height: 50}}
                  innerStyle={{height: 50}}
                  onPress={async () => setReferFormOpen(true)}
                />
                <RoundedButton
                  label="I'm Interested"
                  center
                  loading={isMutating}
                  disabled={isMutating}
                  style={{height: 50}}
                  innerStyle={{height: 50}}
                  onPress={handleInterest}
                />
              </View>
            ) : (
              <UixText
                variant="dotsBold"
                style={{
                  backgroundColor: Colors.Green1000,
                  fontSize: 20,
                  lineHeight: 18,
                  padding: 16,
                  textAlign: 'center',
                  color: Colors.Yellow,
                  borderRadius: 9,
                }}>
                Marked Interested
              </UixText>
            )}
          </View>
        )}
        {drop && (
          <DropReferMemberModal
            isOpen={referFormOpen}
            drop={drop as Drops_Drop_Details}
            onClose={() => setReferFormOpen(false)}
          />
        )}
      </View>
    </ScreenContainer>
  );
}
