import dayjs from 'dayjs';

export function getDropExpiryTime(expiryTime: string): string | undefined {
  const expiry = dayjs(expiryTime);
  const minDiff = expiry.diff(dayjs(), 'minutes');

  if (expiry.isValid()) {
    if (minDiff > 0) {
      const daysDiff = Math.floor(minDiff / (60 * 24));
      if (daysDiff >= 1) {
        return `Expires in ${daysDiff} day${daysDiff > 1 ? 's' : ''}`;
      }
      const hourDiff = Math.floor((minDiff % (60 * 24)) / 60);
      const mins = minDiff % 60;
      const parts = [];
      if (hourDiff > 0) {
        parts.push(`${hourDiff}hr`);
      }
      if (mins > 0) {
        parts.push(`${mins}min`);
      }
      if (parts.length > 0) {
        return `Expires in ${parts.join(' ')}`;
      }
    }
    return 'Expired';
  }
}
