import {SectionInterface} from './types';

import {UixText} from '@/app/ui/UixText';

export function MembersSectionHeader({
  section,
  loading,
}: {
  section: SectionInterface;
  loading: boolean;
}) {
  const {color, label, data} = section;

  return (
    <UixText
      variant="dotsBold"
      style={{
        fontSize: 20,
        lineHeight: 16,
        padding: 8,
        marginTop: 8,
        color,
      }}>
      {`${label}${loading ? '' : ' . ' + data?.length}`}
    </UixText>
  );
}
