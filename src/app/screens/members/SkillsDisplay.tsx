import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {View} from 'react-native';

import {UixText} from '@/app/ui/UixText';
import {Members_Member_Skills} from '@/types/__generated__/graphql';

const MAX_HEIGHT = 60;

const SkillsDisplay = ({
  skills,
  color,
}: {
  skills: Members_Member_Skills[];
  color: string;
}) => {
  if (skills.length === 0) return null;

  const [isClipped, setIsClipped] = useState(false);
  const numSkillsRef = useRef(1);
  const [numSkills, setNumSkills] = useState(numSkillsRef.current);

  const isFullyDisplayed = useMemo(
    () => numSkills === skills.length,
    [numSkills, skills.length],
  );

  const onContainerLayout = useCallback(
    (event: {nativeEvent: {layout: {height: number}}}) => {
      const {height} = event.nativeEvent.layout;
      if (
        height <= MAX_HEIGHT &&
        !isClipped &&
        skills.length > numSkillsRef.current
      ) {
        numSkillsRef.current += 1;
        setNumSkills(numSkillsRef.current);
      } else if (height > MAX_HEIGHT && !isClipped) {
        setIsClipped(true);
      }
    },
    [isClipped],
  );

  useEffect(() => {
    if (isClipped) {
      numSkillsRef.current = Math.max(numSkillsRef.current - 1, 1);
      setNumSkills(numSkillsRef.current);
    }
  }, [isClipped, color]);

  const getColor = useCallback(
    () => (isClipped || isFullyDisplayed ? color : 'transparent'),
    [isClipped, isFullyDisplayed, color],
  );

  if (skills.length === 0) return null;

  return (
    <View style={{maxHeight: MAX_HEIGHT, overflow: 'hidden'}}>
      <View
        key={numSkills}
        onLayout={onContainerLayout}
        style={{
          flexWrap: 'wrap',
          flexDirection: 'row',
          marginTop: 2,
          gap: 5,
        }}>
        {skills.map(skill => (
          <View
            key={skill.skill_detail.title}
            style={{
              borderWidth: 1,
              borderColor: getColor(),
              paddingVertical: 6,
              paddingHorizontal: 8,
              borderRadius: 24,
            }}>
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 14,
                lineHeight: 12,
                color: getColor(),
              }}
              capitalize>
              {skill.skill_detail.title.trim()}
            </UixText>
          </View>
        ))}
        {skills.length - numSkills > 0 && (
          <View
            style={{
              borderWidth: 1,
              borderColor: getColor(),
              alignItems: 'center',
              borderRadius: 24,
              flexDirection: 'row',
              gap: 2,
              paddingLeft: 8,
            }}>
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 16,
                lineHeight: 14,
                color: getColor(),
              }}>
              {'+'}
            </UixText>
            <UixText
              variant="dotsBold"
              style={{
                fontSize: 14,
                lineHeight: 12,
                color: getColor(),
                paddingVertical: 6,
                paddingRight: 8,
              }}>
              {`${skills.length - numSkills}`}
            </UixText>
          </View>
        )}
      </View>
    </View>
  );
};

// Set display name for better debugging
SkillsDisplay.displayName = 'SkillsDisplay';

export default memo(SkillsDisplay);
