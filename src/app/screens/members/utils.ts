import {SectionInterface} from './types';

import {Colors} from '@/app/theme/colors';
import {Members_Member_Details} from '@/types/__generated__/graphql';

export const searchMember = (
  data: Members_Member_Details,
  searchText: string,
) => {
  return `${data.alias?.toLowerCase()}`.includes(searchText?.toLowerCase());
};

const sortMembers = (a: Members_Member_Details, b: Members_Member_Details) => {
  const aliasA = a.alias || '';
  const aliasB = b.alias || '';
  const aPriority = (a.member_status ?? 2) % 2 === 1 ? 0 : 1;
  const bPriority = (b.member_status ?? 2) % 2 === 1 ? 0 : 1;
  return aPriority !== bPriority
    ? aPriority - bPriority
    : aliasA.localeCompare(aliasB);
};

export const getSortedMembers = (data: Members_Member_Details[]) => {
  return (data ?? [])
    .filter(member => member.alias !== 'None')
    .sort(sortMembers);
};

export const getMembersByType = (
  loading: boolean,
  filterData: Members_Member_Details[],
) => {
  if (loading) {
    return [
      {
        id: 'newbies',
        label: 'NEWBIES',
        layout: 'horizontal',
        color: Colors.Pink,
        data: [{}, {}, {}],
      },
      {
        id: 'leaders',
        label: 'LEADERS',
        layout: 'horizontal',
        color: Colors.Teal,
        data: [{}, {}, {}],
      },
      {
        id: 'allMembers',
        label: 'ALL MEMBERS',
        layout: 'vertical',
        color: Colors.Blue,
        data: [{}, {}, {}],
      },
    ] as SectionInterface[];
  }

  const now = Date.now();
  const weekAgo = now - 1000 * 60 * 60 * 24 * 7;

  const sections: SectionInterface[] = [
    {
      id: 'newbies',
      label: 'NEWBIES',
      color: Colors.Pink,
      layout: 'horizontal',
      data: filterData.filter(member => {
        const joiningDate = new Date(
          `${member?.joining_date}T00:00:00Z`,
        ).getTime();
        return joiningDate > weekAgo;
      }),
    },
    {
      id: 'leaders',
      label: 'LEADERS',
      color: Colors.Teal,
      layout: 'horizontal',
      data: filterData
        .filter(member =>
          ['Founder', 'Partner', 'Associate Partner', 'CTO'].includes(
            member.type?.name ?? '',
          ),
        )
        .sort(sortMembers),
    },
    {
      id: 'allMembers',
      label: 'ALL MEMBERS',
      color: Colors.Blue,
      layout: 'vertical',
      data: filterData.filter(
        member =>
          !['Founder', 'Co-Founder', 'Partner', 'Core Member'].includes(
            member.type?.name ?? '',
          ),
      ),
    },
  ];

  return sections.filter(section => section.data.length > 0);
};
