import NewMember from '@assets/newMember.svg';
import React from 'react';
import {View} from 'react-native';

import {Colors} from '@/app/theme/colors';
import SearchBar from '@/app/ui/SearchBar';
import {UixText} from '@/app/ui/UixText';

interface Props {
  searchText: string;
  memberCount: number;
  searchDisabled: boolean;
  onSearch: (text: string) => void;
}

export function MemberListHeader(props: Props) {
  const {onSearch, memberCount, searchText, searchDisabled} = props;

  return (
    <View style={{}}>
      <UixText
        variant="dotsBold"
        style={{
          fontSize: 32,
          lineHeight: 22,
          color: Colors.Yellow,
          marginBottom: 16,
          marginHorizontal: 8,
          elevation: 12,
          shadowColor: Colors.Yellow,
          shadowOpacity: 0.8,
          shadowRadius: 8,
          shadowOffset: {width: 0, height: 0},
        }}>
        {`${memberCount > 0 ? `${memberCount} ` : ''}${memberCount === 1 ? 'MEMBER' : 'MEMBERS'}`}
      </UixText>
      <SearchBar
        onSearchText={onSearch}
        searchText={searchText}
        disabled={searchDisabled}
      />
      <View style={{height: 8}} />
      {searchText && memberCount == 0 && (
        <View style={{padding: 8, paddingVertical: 32}}>
          <NewMember height={40} width={40} />
          <View style={{height: 12}} />
          <UixText style={{color: Colors.White300, fontSize: 20}}>
            {'Sorry,\nNo one with the username'}
          </UixText>
          <View style={{flexDirection: 'row'}}>
            <UixText style={{color: Colors.Teal, fontSize: 20}}>
              {`“${searchText}” `}
            </UixText>
            <UixText style={{color: Colors.White300, fontSize: 20}}>
              exists in our system.
            </UixText>
          </View>
        </View>
      )}
    </View>
  );
}
