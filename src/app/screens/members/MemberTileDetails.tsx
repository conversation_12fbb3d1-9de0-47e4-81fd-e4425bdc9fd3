import {useMemo} from 'react';
import {View} from 'react-native';

import {MEMBER_TILE_SHIMMER_COLORS} from './constants';
import SkillsDisplay from './SkillsDisplay';

import {Colors} from '@/app/theme/colors';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixText} from '@/app/ui/UixText';
import {Members_Member_Details} from '@/types/__generated__/graphql';

export const MemberTileDetails = ({
  name,
  member,
  color,
  disabled,
  isVertical,
}: {
  name: string;
  member: Members_Member_Details;
  color: string;
  disabled: boolean;
  isVertical: boolean;
}) => {
  const sortedSkills = useMemo(() => {
    return [...(member?.member_skills ?? [])].sort(
      (a, b) => a.skill_detail.title.length - b.skill_detail.title.length,
    );
  }, [member?.member_skills]);

  const formattedDate = useMemo(() => {
    if (!member.career_starting_year) return null;
    return (
      new Date().getFullYear() - member.career_starting_year + '+ yrs of exp'
    );
  }, [member.career_starting_year]);

  return name ? (
    <View
      style={{
        flex: 1,
        flexDirection: 'column',
        gap: 8,
        justifyContent: isVertical ? 'space-between' : 'center',
      }}>
      <UixText
        variant="dotsBold"
        style={{fontSize: 24, lineHeight: 30, color: color}}
        numberOfLines={1}
        capitalize>
        {name}
      </UixText>
      {formattedDate && (
        <UixText
          style={{
            fontSize: 16,
            lineHeight: 16,
            color: disabled ? Colors.White600 : Colors.White300,
          }}
          numberOfLines={1}>
          {formattedDate}
        </UixText>
      )}
      <SkillsDisplay color={color} skills={sortedSkills} />
    </View>
  ) : (
    <View
      style={{
        flex: 1,
        flexDirection: 'column',
        gap: 8,
        justifyContent: 'center',
      }}>
      <Shimmer
        style={{
          height: 24,
          width: 100,
          borderRadius: 4,
          opacity: 0.4,
        }}
        shimmerColors={MEMBER_TILE_SHIMMER_COLORS}
      />
      <Shimmer
        style={{
          height: 30,
          borderRadius: 4,
          width: 180,
          opacity: 0.4,
        }}
        shimmerColors={MEMBER_TILE_SHIMMER_COLORS}
      />
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          width: '100%',
          gap: 10,
        }}>
        <Shimmer
          style={{
            height: 24,
            width: 70,
            borderRadius: 12,
            opacity: 0.4,
          }}
          shimmerColors={MEMBER_TILE_SHIMMER_COLORS}
        />
        <Shimmer
          style={{
            height: 24,
            width: 100,
            borderRadius: 12,
            opacity: 0.4,
          }}
          shimmerColors={MEMBER_TILE_SHIMMER_COLORS}
        />
      </View>
    </View>
  );
};
