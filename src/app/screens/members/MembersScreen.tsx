import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {AppState, ListRenderItemInfo, View} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';

import {MEMBER_TILE_HEIGHT} from './constants';
import {MemberListHeader} from './MemberListHeader';
import {MembersSectionHeader} from './MembersSectionHeader';
import MemberTile from './MemberTile';
import {SectionInterface} from './types';
import {getMembersByType, getSortedMembers, searchMember} from './utils';

import useSearch from '@/app/hooks/useSearch';
import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {useGetHeaderHeight, useGetTabBarHeight} from '@/app/theme/hooks';
import {MembersList} from '@/graphQL/members';
import {
  Members_Member_Details,
  MembersListQuery,
} from '@/types/__generated__/graphql';

// const PAGE_SIZE = 20;

export function MembersScreen() {
  const {data, loading} = useUixQuery<MembersListQuery>(MembersList);
  const [removeClipped, setRemoveClipped] = useState(false);
  const isFocused = useIsFocused();
  const navigation = useNavigation();
  const [appState, setAppState] = useState(AppState.currentState);
  // const [isLoadingMore, setIsLoadingMore] = useState(false);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      setAppState(nextAppState);
    });

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    let focusTimeout: NodeJS.Timeout;

    const unsubscribeFocus = navigation.addListener('focus', () => {
      focusTimeout = setTimeout(() => {
        if (appState === 'active') {
          setRemoveClipped(true);
        }
      }, 300);
    });

    const unsubscribeBlur = navigation.addListener('blur', () => {
      if (focusTimeout) {
        clearTimeout(focusTimeout);
      }
      setRemoveClipped(false);
    });

    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      () => {
        if (focusTimeout) {
          clearTimeout(focusTimeout);
        }
        setRemoveClipped(false);
      },
    );

    return () => {
      if (focusTimeout) {
        clearTimeout(focusTimeout);
      }
      unsubscribeFocus();
      unsubscribeBlur();
      unsubscribeBeforeRemove();
    };
  }, [navigation, appState]);

  useEffect(() => {
    if (appState !== 'active') {
      setRemoveClipped(false);
    }
  }, [appState]);

  const memberList = useMemo(
    () =>
      getSortedMembers(
        (data?.members_member_details as Members_Member_Details[]) ?? [],
      ),
    [data],
  );

  const {filteredData, onSearch, searchText} = useSearch({
    data: memberList,
    filter: searchMember,
  });

  const membersByType = useMemo(
    () => getMembersByType(loading, filteredData),
    [loading, filteredData],
  );

  const HEADER_HEIGHT = useGetHeaderHeight();
  const TAB_BAR_HEIGHT = useGetTabBarHeight();

  const MemberListSectionHeader = useCallback(
    ({section}: {section: SectionInterface}) => (
      <MembersSectionHeader section={section} loading={loading} />
    ),
    [loading],
  );

  const RenderMemberTile = useCallback(
    ({
      item,
      section,
    }: {
      item: Members_Member_Details;
      section: SectionInterface;
    }) => (
      <MemberTile
        member={item}
        color={section.color}
        scrollOrientation={section.layout}
      />
    ),
    [MemberTile],
  );

  const RenderMemberSection = useCallback(
    (section: ListRenderItemInfo<SectionInterface>) => {
      const isHorizontal = section.item.layout === 'horizontal';
      const memberCardScrollLength =
        MEMBER_TILE_HEIGHT * (isHorizontal ? 4 / 3 : 1);
      return (
        <View>
          <MemberListSectionHeader section={section.item} />
          <FlatList
            data={section.item.data}
            horizontal={isHorizontal}
            scrollEnabled={isHorizontal}
            keyExtractor={(item, index) =>
              String(item?.id ?? `fallback-${index}`)
            }
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            getItemLayout={(_, index) => ({
              length: memberCardScrollLength,
              offset: memberCardScrollLength * index,
              index,
            })}
            ItemSeparatorComponent={ItemSeparator}
            onEndReachedThreshold={1}
            initialNumToRender={6}
            maxToRenderPerBatch={6}
            windowSize={10}
            removeClippedSubviews={
              !isHorizontal &&
              removeClipped &&
              isFocused &&
              appState === 'active'
            }
            renderItem={item => (
              <RenderMemberTile item={item.item} section={section.item} />
            )}
          />
        </View>
      );
    },
    [RenderMemberTile],
  );

  const ItemSeparator = useCallback(
    () => <View style={{height: 10, width: 10}} />,
    [],
  );

  // const handleLoadMore = useCallback(() => {
  //   if (!loading && data?.members_member_details) {
  //     const currentLength = data.members_member_details.length;

  //     // Only fetch more if we have exactly PAGE_SIZE items in the current page
  //     if (currentLength > 0 && currentLength % PAGE_SIZE === 0) {
  //       setIsLoadingMore(true);
  //       fetchMore({
  //         variables: {
  //           offset: currentLength,
  //           limit: PAGE_SIZE,
  //         },
  //         updateQuery: (prev, {fetchMoreResult}) => {
  //           if (!fetchMoreResult) return prev;

  //           // Filter out any potential duplicates based on id
  //           const existingIds = new Set(
  //             prev.members_member_details.map(member => member.id),
  //           );
  //           const newMembers = fetchMoreResult.members_member_details.filter(
  //             member => !existingIds.has(member.id),
  //           );

  //           return {
  //             ...prev,
  //             members_member_details: [
  //               ...prev.members_member_details,
  //               ...newMembers,
  //             ],
  //           };
  //         },
  //       });
  //     } else {
  //       setIsLoadingMore(false);
  //     }
  //   }
  // }, [data, loading, fetchMore]);

  return (
    <ScreenContainer name={ScreenNames.Members} tabScreen style={{padding: 10}}>
      <FlatList
        nestedScrollEnabled
        data={membersByType}
        keyExtractor={(section, index) =>
          String(section?.id ?? `fallback-${index}`)
        }
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={false}
        contentContainerStyle={{
          paddingTop: TAB_BAR_HEIGHT,
          paddingBottom: HEADER_HEIGHT,
        }}
        ListHeaderComponent={
          <MemberListHeader
            onSearch={onSearch}
            searchText={searchText}
            memberCount={filteredData.length}
            searchDisabled={loading}
          />
        }
        // onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        renderItem={RenderMemberSection}
        // ListFooterComponent={() => {
        //   return <ActivityIndicator animating={isLoadingMore} />;
        // }}
      />
    </ScreenContainer>
  );
}
