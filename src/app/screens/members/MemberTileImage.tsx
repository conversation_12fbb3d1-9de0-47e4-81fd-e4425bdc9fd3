import {Animated, View} from 'react-native';
import FastImage from 'react-native-fast-image';

import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {Shimmer} from '@/app/ui/Shimmer';
import {UixCornerView} from '@/app/ui/UixCornerView';
import {UixText} from '@/app/ui/UixText';
import {useMemo, useRef} from 'react';

export const MemberTileImage = ({
  loading,
  image,
  color,
  disabled,
  designation,
  width,
  height,
  background,
}: {
  loading: boolean;
  image?: string;
  designation?: string | null;
  color?: string;
  disabled: boolean;
  width: number;
  height: number;
  background: string;
}) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const onLoad = () => {
    Animated.timing(opacity, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const imageSource = useMemo(() => {
    if (image) {
      return {
        uri: image,
        priority: FastImage.priority.low,
        cache: FastImage.cacheControl.immutable,
      };
    }
    return disabled
      ? require('@assets/avatars/defaultGrayscale.png')
      : require('@assets/avatars/default.png');
  }, [image, disabled]);

  return (
    <UixCornerView
      type="tl"
      cut={0.2}
      background={
        loading ? (
          <Shimmer
            style={{
              width: width,
              height: height,
              borderRadius: 6,
            }}
            shimmerColors={[
              updateColorWithOpacity(Colors.Black, 0.4),
              updateColorWithOpacity(Colors.White, 0.1),
            ]}
          />
        ) : (
          <View
            style={{
              borderRadius: 6,
              overflow: 'hidden',
              width: width,
              height: height,
            }}>
            <FastImage
              style={{width: '100%', height: '100%'}}
              source={imageSource}
              onLoad={onLoad}
              resizeMode={FastImage.resizeMode.cover}>
              <View style={{flex: 1, backgroundColor: background}} />
            </FastImage>
          </View>
        )
      }>
      <View
        style={{
          width: width,
          height: height,
          justifyContent: 'flex-end',
        }}>
        {designation && (
          <View
            style={{
              backgroundColor: updateColorWithOpacity(Colors.White1000, 0.7),
              padding: 4,
              paddingBottom: 6,
              borderBottomEndRadius: 6,
              borderBottomStartRadius: 6,
              borderWidth: 1,
              borderColor: disabled ? Colors.White400 : color,
            }}>
            <UixText
              variant="dotsBold"
              style={{
                textAlign: 'center',
                color: disabled ? Colors.White400 : color,
                fontSize: 12,
                lineHeight: 12,
              }}
              capitalize>
              {designation}
            </UixText>
          </View>
        )}
      </View>
    </UixCornerView>
  );
};
