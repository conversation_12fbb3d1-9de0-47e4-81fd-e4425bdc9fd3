import React, {useMemo} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';

import {createDynamicUrl} from '../utils';
import {MEMBER_TILE_HEIGHT} from './constants';
import {MemberTileDetails} from './MemberTileDetails';
import {MemberTileImage} from './MemberTileImage';

import {Tracker} from '@/app/analytics/tracker';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {Members_Member_Details} from '@/types/__generated__/graphql';

function MemberTile({
  member,
  color,
  scrollOrientation,
}: {
  member: Members_Member_Details;
  color: string;
  scrollOrientation: 'vertical' | 'horizontal';
  disabled?: boolean;
}) {
  const isVerticalCard = scrollOrientation === 'horizontal';
  const disabled = (member.member_status ?? 0) % 2 === 0;
  const memberCardWidth = useMemo(
    () => MEMBER_TILE_HEIGHT * (isVerticalCard ? 4 / 3 : 1),
    [isVerticalCard],
  );
  const memberCardHeight = useMemo(
    () => MEMBER_TILE_HEIGHT * (isVerticalCard ? 9 / 5 : 1),
    [isVerticalCard],
  );

  const background = useMemo(
    () => updateColorWithOpacity(disabled ? Colors.White400 : color, 0.2),
    [disabled, color],
  );
  const name = member.alias ?? '';

  const image = useMemo(
    () =>
      createDynamicUrl(member?.member_photo ?? '', background.replace('#', '')),
    [member?.member_photo, background],
  );

  const innerViewStyle = useMemo(
    () =>
      StyleSheet.create({
        container: {
          height: memberCardHeight,
          width: isVerticalCard ? memberCardWidth : '100%',
          flexDirection: 'row',
          gap: 16,
          alignItems: 'center',
        },
        cornerView: {
          flex: 1,
          backgroundColor: background,
          borderRadius: 8,
          overflow: 'hidden',
          borderTopLeftRadius: 32,
        },
      }),
    [isVerticalCard, memberCardHeight, memberCardWidth, background],
  );

  const onPress = () => {
    if (!name) return;
    Tracker.track('Member_Tile_Clicked', {
      member_id: member?.id,
      alias: member?.alias || '',
    });
    NavigationService.navigate(ScreenNames.MemberDetail, {
      id: member?.id,
      member: member,
    });
  };

  return (
    <Pressable
      onPress={onPress}
      style={({pressed}) => ({flex: 1, opacity: pressed ? 0.5 : 1})}>
      <View style={innerViewStyle.cornerView}>
        <View
          style={[
            innerViewStyle.container,
            {padding: isVerticalCard ? 10 : 0},
          ]}>
          <View
            style={
              isVerticalCard ? {} : [innerViewStyle.container, {padding: 10}]
            }>
            <MemberTileImage
              loading={Boolean(name == '')}
              image={image}
              disabled={disabled}
              designation={member.designation}
              background={background}
              color={color}
              width={memberCardWidth - 20}
              height={MEMBER_TILE_HEIGHT - 20}
            />
            <MemberTileDetails
              member={member}
              name={name}
              disabled={disabled}
              color={disabled ? Colors.White500 : color}
              isVertical={isVerticalCard}
            />
          </View>
        </View>
      </View>
    </Pressable>
  );
}

export default React.memo(MemberTile, (prev, next) => {
  return prev.member?.id === next.member?.id;
});
