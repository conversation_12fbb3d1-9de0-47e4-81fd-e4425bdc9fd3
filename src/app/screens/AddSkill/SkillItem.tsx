import React from 'react';
import {Image, Pressable} from 'react-native';

import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import {Skills_Skill_Details} from '@/types/__generated__/graphql';

interface SkillItemProps {
  skill: Skills_Skill_Details;
  onPress: (skill: Skills_Skill_Details) => void;
  leadingIcon?: React.ReactNode;
  rating?: number | null;
  hideIcon?: boolean;
}

export function SkillItem({
  skill,
  onPress,
  leadingIcon,
  rating,
  hideIcon = false,
}: SkillItemProps) {
  return (
    <Pressable
      onPress={() => onPress(skill)}
      style={{
        backgroundColor: Colors.Teal950,
        borderRadius: 8,
        padding: 12,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
      }}>
      {!hideIcon &&
        (leadingIcon || (
          <Image
            source={require('@assets/plusTeal.png')}
            style={{width: 18, height: 18}}
          />
        ))}
      {rating && (
        <UixText
          style={{
            fontSize: 18,
            lineHeight: 24,
            fontWeight: 'bold',
            color: Colors.Teal,
          }}>
          {`${rating}`}
        </UixText>
      )}
      <UixText
        style={{
          fontSize: 18,
          lineHeight: 24,
          color: Colors.Teal,
        }}>
        {skill.title}
      </UixText>
    </Pressable>
  );
}
