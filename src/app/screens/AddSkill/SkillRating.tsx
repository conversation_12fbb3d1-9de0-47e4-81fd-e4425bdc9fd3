import {useMutation} from '@apollo/client';
import {RootState} from '@store/store';
import {produce} from 'immer';
import React, {useState} from 'react';
import {Image, Pressable, View} from 'react-native';
import {useSelector} from 'react-redux';

import {client} from '@/app/apollo/client';
import {Colors} from '@/app/theme/colors';
import {ButtonType, RoundedButton} from '@/app/ui/RoundedButton';
import {UixText} from '@/app/ui/UixText';
import {MemberDetailQuery} from '@/graphQL/member';
import {
  AddMemberSkill,
  DeleteMemberSkill,
  UpdateMemberSkill,
} from '@/graphQL/skill';
import {
  AddMemberSkillMutation,
  AddMemberSkillMutationVariables,
  DeleteMemberSkillMutation,
  DeleteMemberSkillMutationVariables,
  MemberDetailsQuery,
  Skills_Skill_Details,
  UpdateMemberSkillMutation,
  UpdateMemberSkillMutationVariables,
} from '@/types/__generated__/graphql';

interface Props {
  isEditing?: boolean;
  rating?: number | null;
  skill: Skills_Skill_Details;
  onApiSuccess?: (type: 'add' | 'update' | 'delete', id?: string) => void;
}

export function SkillRating(props: Props) {
  const {skill, isEditing = false, onApiSuccess} = props;
  const [rating, setRating] = useState(props.rating ?? 0);
  const userId = useSelector((store: RootState) => store.user.id);

  const updateCache = (
    member_skill_id: string,
    type: 'add' | 'update' | 'delete',
  ) => {
    client.cache.updateQuery(
      {
        query: MemberDetailQuery,
        variables: {
          id: userId,
        },
      },
      (data: MemberDetailsQuery | null) => {
        let memberSkills = [
          ...(data?.members_member_details_by_pk?.member_skills ?? []),
        ];
        const newMemberSkill = {
          id: member_skill_id,
          rating: rating,
          skill_detail: skill,
        };
        switch (type) {
          case 'add':
            memberSkills.push(newMemberSkill);
            break;
          case 'update':
            memberSkills = memberSkills?.map(ms =>
              ms.id === member_skill_id ? newMemberSkill : ms,
            );
            break;
          case 'delete':
            memberSkills = memberSkills?.filter(
              ms => ms.id !== member_skill_id,
            );
            break;
        }
        const nextState = produce(data, draft => {
          if (draft?.members_member_details_by_pk?.member_skills) {
            draft.members_member_details_by_pk.member_skills = memberSkills;
          }
        });
        return nextState;
      },
    );
  };

  const [updateSkillRating, {loading: isUpdating}] = useMutation<
    UpdateMemberSkillMutation,
    UpdateMemberSkillMutationVariables
  >(UpdateMemberSkill);

  const [addMemberSkill, {loading: isAdding}] = useMutation<
    AddMemberSkillMutation,
    AddMemberSkillMutationVariables
  >(AddMemberSkill);

  const [deleteSkill, {loading: isDeleting}] = useMutation<
    DeleteMemberSkillMutation,
    DeleteMemberSkillMutationVariables
  >(DeleteMemberSkill);

  const onUpdateRating = async () => {
    try {
      const data = await updateSkillRating({
        variables: {
          id: userId,
          skillId: skill.id,
          rating: rating,
        },
      });

      const msId = data?.data?.update_members_member_skills?.returning?.[0]?.id;
      if (msId) {
        updateCache(msId, 'update');
        onApiSuccess?.('update', skill.id);
      }

      onApiSuccess?.('update', skill.id);
    } catch (error) {
      console.log('error is', error);
    }
  };

  const onAddSkillPress = async () => {
    try {
      const data = await addMemberSkill({
        variables: {
          id: userId,
          skillId: skill.id,
          rating: rating,
        },
      });

      const msId = data?.data?.insert_members_member_skills_one?.id;
      if (msId) {
        updateCache(msId, 'add');
        onApiSuccess?.('add', skill.id);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const onDeleteSkill = async () => {
    try {
      const data = await deleteSkill({
        variables: {
          id: userId,
          skillId: skill.id,
        },
      });

      const msId = data?.data?.delete_members_member_skills?.returning?.[0]?.id;
      if (msId) {
        updateCache(msId, 'delete');
        onApiSuccess?.('delete', skill.id);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const isLoading = isUpdating || isDeleting || isAdding;

  return (
    <View>
      <UixText style={{fontSize: 18, lineHeight: 18, paddingTop: 16}}>
        {skill.title}
      </UixText>
      <View style={{flexDirection: 'row', gap: 24, padding: 16}}>
        {[1, 2, 3, 4, 5].map(star => (
          <Pressable
            key={star}
            onPress={() => setRating(star)}
            style={{flex: 1}}>
            <Image
              source={
                star <= rating
                  ? require('@assets/skillStar.png')
                  : require('@assets/skillStarDisabled.png')
              }
              style={{
                width: '100%',
                height: undefined,
                aspectRatio: 1,
              }}
            />
          </Pressable>
        ))}
      </View>
      {!isEditing ? (
        <View style={{flexDirection: 'row', columnGap: 16}}>
          <RoundedButton
            center
            disabled={isLoading || rating === 0 || !rating}
            label="Save"
            color={Colors.Yellow}
            style={{flex: 1}}
            loading={isUpdating}
            onPress={onUpdateRating}
          />
          <RoundedButton
            center
            type={ButtonType.SECONDARY}
            disabled={isLoading}
            onPress={onDeleteSkill}
            label="Delete"
            loading={isDeleting}
            color={Colors.Yellow}
            style={{flex: 1}}
          />
        </View>
      ) : (
        <RoundedButton
          center
          disabled={isLoading || rating === 0 || !rating}
          loading={isAdding}
          onPress={onAddSkillPress}
          label="Confirm"
        />
      )}
    </View>
  );
}
