import {useEffect} from 'react';
import {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

export default function useAnimated(selected: boolean) {
  const animated = useSharedValue(Number(selected));

  useEffect(() => {
    animated.value = withTiming(Number(selected));
  }, [selected]);

  const crossAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {rotate: `${interpolate(animated.value, [0, 1], [0, 45])}deg`},
      ],
    };
  });

  return {
    crossAnimatedStyle,
  };
}
