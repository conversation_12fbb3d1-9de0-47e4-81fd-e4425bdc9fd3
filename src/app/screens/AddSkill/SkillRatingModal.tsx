import {Image, Pressable, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {SkillRating} from '@/app/screens/AddSkill/SkillRating';
import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import {
  Members_Member_Skills,
  Skills_Skill_Details,
} from '@/types/__generated__/graphql';

interface SkillRatingModalProps {
  isVisible: boolean;
  onClose: () => void;
  selectedSkill: Skills_Skill_Details | null;
  isEditing: boolean;
  memberSkills: Members_Member_Skills[];
  onApiSuccess: () => void;
}

export function SkillRatingModal({
  isVisible,
  onClose,
  selectedSkill,
  isEditing,
  memberSkills,
  onApiSuccess,
}: SkillRatingModalProps) {
  const insets = useSafeAreaInsets();

  return (
    <Modal
      isVisible={isVisible && selectedSkill !== null}
      onBackdropPress={onClose}
      style={{
        margin: 0,
        justifyContent: 'flex-end',
      }}>
      <View
        style={{
          backgroundColor: Colors.DarkBackground,
          padding: 16,
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          paddingBottom: insets.bottom + 16,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <UixText
            variant="dotsBold"
            style={{fontSize: 28, lineHeight: 22, color: Colors.White}}
            capitalize>
            Rate Skill
          </UixText>
          <Pressable onPress={onClose}>
            <Image
              source={require('@assets/cross.png')}
              style={{width: 30, height: 30}}
            />
          </Pressable>
        </View>
        {selectedSkill && (
          <SkillRating
            isEditing={isEditing}
            rating={Math.min(
              memberSkills.find(ms => ms.skill_detail.id === selectedSkill.id)
                ?.rating ?? 0,
              5,
            )}
            skill={selectedSkill}
            onApiSuccess={
              isEditing
                ? () => {
                    onClose();
                    onApiSuccess();
                  }
                : () => onClose()
            }
          />
        )}
      </View>
    </Modal>
  );
}
