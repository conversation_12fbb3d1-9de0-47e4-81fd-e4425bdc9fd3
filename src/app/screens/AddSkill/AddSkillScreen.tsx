import CloseIcon from '@assets/cross.svg';
import {debounce} from 'lodash';
import React, {useCallback, useRef, useState} from 'react';
import {Keyboard, TextInput as RNTextInput, View} from 'react-native';
import {TextInput} from 'react-native-paper';

import {ScreenContainer} from '../components/ScreenContainer';
import {SuggestionList} from './SuggestionList';

import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {UixText} from '@/app/ui/UixText';
import {UixTextInput} from '@/app/ui/UixTextInput';

export default function AddSkillsScreen() {
  const [searchTerm, setSearchTerm] = useState('');
  const searchInputRef = useRef<RNTextInput>(null);

  const updateSearchText = useCallback(
    debounce((text: string) => {
      setSearchTerm(text);
    }, 300),
    [],
  );

  const [editMode, setEditMode] = useState(false);

  const onApiSuccess = () => {
    setSearchTerm('');
    searchInputRef.current?.clear();
    Keyboard.dismiss();
    setEditMode(false);
  };

  return (
    <ScreenContainer
      name={ScreenNames.AddSkill}
      disableBackground
      style={{backgroundColor: Colors.DarkBackground, padding: 16}}
      enableKeyboardScrollView>
      <View>
        <UixButton
          label="Close"
          iconImage={require('@assets/cross.png')}
          onPress={() => NavigationService.goBack()}
          tintColor={'Teal'}
        />
      </View>
      <UixText
        variant="dotsBold"
        capitalize
        style={{
          fontSize: 32,
          lineHeight: 26,
          marginTop: 16,
          color: Colors.Teal,
        }}>
        ADD Skills
      </UixText>
      <UixText
        variant="titleMedium"
        style={{color: Colors.Teal700, marginBottom: 16}}>
        Search for skills
      </UixText>
      <UixTextInput
        mode="outlined"
        onPress={() => setEditMode(true)}
        ref={searchInputRef}
        onFocus={() => setEditMode(true)}
        textContentType="name"
        outlineColor={Colors.Teal900}
        activeOutlineColor={Colors.Teal800}
        textColor={Colors.Teal}
        onChangeText={updateSearchText}
        placeholderTextColor={Colors.Teal800}
        style={{backgroundColor: Colors.Teal950}}
        right={
          searchTerm.length > 0 ? (
            <TextInput.Icon icon={() => <CloseIcon />} onPress={onApiSuccess} />
          ) : null
        }
      />
      <View style={{height: 16}} />
      <SuggestionList
        searchTerm={searchTerm}
        editMode={editMode}
        onApiSuccess={onApiSuccess}
      />
    </ScreenContainer>
  );
}
