import {useMutation} from '@apollo/client';
import {RootState} from '@store/store';
import {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, Image, Keyboard, View} from 'react-native';
import {useSelector} from 'react-redux';

import {CreateSkill, GetSkillsQuery} from '../../../graphQL/skills';
import {SkillItem} from './SkillItem';
import {SkillRatingModal} from './SkillRatingModal';

import useUixQuery from '@/app/hooks/useUixQuery';
import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';
import useMemberContext from '@/contexts/MemberContext';
import {MemberDetailQuery} from '@/graphQL/member';
import {
  GetSkillsQueryQuery,
  MemberDetailsQuery,
  Members_Member_Skills,
  Skills_Skill_Details,
} from '@/types/__generated__/graphql';

export function SuggestionList({
  searchTerm = '',
  editMode = false,
  userSkills,
  onApiSuccess = () => {},
}: {
  searchTerm?: string;
  userSkills?: Members_Member_Skills[];
  editMode?: boolean;
  onApiSuccess?: () => void;
}) {
  const [createSkillMutation] = useMutation(CreateSkill);
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [selectedSkill, setSelectedSkill] =
    useState<Skills_Skill_Details | null>(null);
  const id = useSelector((state: RootState) => state.user.id);
  const {isCurrentUser} = useMemberContext();

  const {data, loading} = useUixQuery<GetSkillsQueryQuery>(GetSkillsQuery, {
    variables: {
      search: searchTerm ? `%${searchTerm}%` : '%%',
    },
  });

  const {data: memberDetails} = useUixQuery<MemberDetailsQuery>(
    MemberDetailQuery,
    {
      variables: {id},
      fetchPolicy: 'cache-first',
    },
  );

  const memberSkills = useMemo(() => {
    return (
      userSkills ||
      memberDetails?.members_member_details_by_pk?.member_skills ||
      []
    );
  }, [memberDetails, userSkills]);

  const filteredList = useMemo(() => {
    return (
      data?.skills_skill_details?.filter(
        skill => !memberSkills.some(ms => ms.skill_detail.id === skill.id),
      ) || []
    );
  }, [data, memberSkills]);

  const onSkillItemPress = useCallback((skill: Skills_Skill_Details) => {
    Keyboard.dismiss();
    setBottomSheetVisible(true);
    setSelectedSkill(skill);
  }, []);

  const handleSkillPress = async (
    skillOrTitle: Skills_Skill_Details | string,
  ) => {
    if (typeof skillOrTitle === 'string') {
      try {
        const result = await createSkillMutation({
          variables: {
            title: skillOrTitle,
            searchCode: skillOrTitle,
          },
        });

        const newSkill = result.data?.insert_skills_skill_details_one;
        if (newSkill) {
          onSkillItemPress(newSkill);
        }
      } catch (error) {
        console.log('Error creating skill:', error);
      }
    } else {
      onSkillItemPress(skillOrTitle);
    }
  };

  const createEmptySkillFromSearchTerm = (
    searchTerm: string,
  ): Skills_Skill_Details => ({
    id: '',
    title: searchTerm,
    type: 'custom',
    search_code: searchTerm,
    member_skills: [],
    member_skills_aggregate: {
      aggregate: {
        count: 0,
      },
      nodes: [],
    },
  });

  return (
    <View style={{flexGrow: 1}}>
      {editMode ? (
        loading ? (
          <ActivityIndicator style={{margin: 156}} />
        ) : memberSkills.some(
            skill =>
              skill.skill_detail.title.toLowerCase() ===
              searchTerm.toLowerCase(),
          ) ? (
          <View style={{alignItems: 'center', marginTop: 40}}>
            <UixText
              variant="titleMedium"
              style={{color: Colors.Teal700, textAlign: 'center'}}>
              {`"${searchTerm}" already part of your skills`}
            </UixText>
          </View>
        ) : searchTerm && filteredList.length === 0 ? (
          <View style={{alignItems: 'center', flexDirection: 'row', gap: 16}}>
            <UixText
              style={{
                fontSize: 18,
                lineHeight: 24,
                color: Colors.Teal,
              }}>
              Create Skill
            </UixText>
            <SkillItem
              skill={createEmptySkillFromSearchTerm(searchTerm)}
              onPress={() => handleSkillPress(searchTerm)}
              hideIcon
            />
          </View>
        ) : (
          <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 12}}>
            {filteredList.map(skill => (
              <SkillItem
                key={skill.id}
                skill={skill as Skills_Skill_Details}
                onPress={onSkillItemPress}
              />
            ))}
          </View>
        )
      ) : (
        <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 12}}>
          {memberSkills.map(skill => (
            <SkillItem
              key={skill.skill_detail.id}
              skill={skill.skill_detail as Skills_Skill_Details}
              leadingIcon={
                <Image
                  source={require('@assets/skillStar.png')}
                  style={{width: 22, height: 22}}
                />
              }
              rating={skill.rating}
              onPress={
                isCurrentUser || !userSkills ? onSkillItemPress : () => {}
              }
            />
          ))}
        </View>
      )}
      <SkillRatingModal
        isVisible={bottomSheetVisible}
        onClose={() => setBottomSheetVisible(false)}
        selectedSkill={selectedSkill}
        isEditing={editMode}
        memberSkills={memberSkills as Members_Member_Skills[]}
        onApiSuccess={onApiSuccess}
      />
    </View>
  );
}
