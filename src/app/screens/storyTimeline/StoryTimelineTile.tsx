import React from 'react';
import {Pressable, View} from 'react-native';

import {StoryMedia} from './components/StoryMedia';

import {Tracker} from '@/app/analytics/tracker';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {Colors} from '@/app/theme/colors';
import {updateColorWithOpacity} from '@/app/theme/utils';
import {UixText} from '@/app/ui/UixText';
import {Stories_Story_Details} from '@/types/__generated__/graphql';

export interface StoryTimelineTileProps {
  storyDetail: Stories_Story_Details;
  showDateHeader: boolean;
  formattedDate: string;
}

export function StoryTimelineTile({
  showDateHeader,
  formattedDate,
  storyDetail,
}: StoryTimelineTileProps) {
  const onPress = () => {
    Tracker.track('Stories_Widget_Clicked', {story_id: storyDetail.id});
    NavigationService.navigate(ScreenNames.Stories, {
      id: storyDetail.id,
    });
  };

  return (
    <View>
      {showDateHeader && (
        <View
          style={{
            paddingVertical: 24,
            paddingLeft: 33,
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
          }}>
          <View
            style={{
              width: 16,
              height: 16,
              borderRadius: 8,
              borderWidth: 4,
              borderColor: Colors.StoriesBackground,
              backgroundColor: Colors.Teal800,
            }}
          />
          <UixText
            variant="dotsBold"
            style={{
              fontSize: 30,
              lineHeight: 26,
              color: Colors.Teal800,
            }}
            capitalize>
            {formattedDate}
          </UixText>
        </View>
      )}
      <Pressable
        style={{
          backgroundColor: updateColorWithOpacity(Colors.Black, 0.7),
        }}
        onPress={onPress}>
        <View
          style={{
            position: 'absolute',
            left: 40,
            top: 0,
            width: 2,
            bottom: 0,
            backgroundColor: Colors.Teal950,
          }}
        />
        <View
          style={{
            flexDirection: 'row',
            marginLeft: 56,
            marginVertical: 36,
            gap: 12,
          }}>
          <View style={{gap: 8, flex: 1}}>
            <UixText
              variant="dotsBold"
              style={{fontSize: 24, lineHeight: 22, color: Colors.Teal}}
              capitalize>{`${storyDetail.story_group?.title}`}</UixText>
            <UixText
              variant="titleMedium"
              style={{
                marginRight: 32,
                color: Colors.Teal800,
              }}>
              {storyDetail.title}
            </UixText>
          </View>
          <StoryMedia
            source={storyDetail.cover_media!}
            type={storyDetail.media_type!}
            resizeMode="contain"
            muted
            repeat
            redirectedFrom="storyTimeline"
          />
        </View>
      </Pressable>
    </View>
  );
}
