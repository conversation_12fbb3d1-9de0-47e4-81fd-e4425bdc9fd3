import React from 'react';
import {View, ViewStyle} from 'react-native';

import {UixImage} from '@/app/ui/uixImage/UixImage';
import getThumbnailFromCloudinary from '@/app/utils/getThumbnailFromCloudinary';

interface Props {
  type: 'IMAGE' | 'VIDEO';
  source: string;
  style?: ViewStyle;
  resizeMode: 'contain' | 'cover';
  repeat?: boolean;
  muted?: boolean;
  redirectedFrom?: 'storiesGroup' | 'storyTimeline';
}

const MEDIA_WIDTH = 80;
const MEDIA_HEIGHT = 130;

export function StoryMedia(props: Props) {
  const {type, source, style, ...rest} = props;

  const containerStyle = {
    width: MEDIA_WIDTH,
    height: props.redirectedFrom === 'storyTimeline' ? MEDIA_HEIGHT : 0,
    marginRight: 10,
    overflow: 'hidden' as const,
  };

  switch (type) {
    case 'IMAGE':
      return (
        <View style={containerStyle}>
          <UixImage
            source={{uri: source}}
            resizeMode="cover"
            style={[{width: '100%', height: '100%'}, style as never]}
          />
        </View>
      );

    case 'VIDEO': {
      const thumbnailUrl = getThumbnailFromCloudinary(source);
      if (!thumbnailUrl) return <View style={containerStyle} />;

      return (
        <View style={containerStyle}>
          <UixImage
            source={{uri: thumbnailUrl}}
            {...rest}
            style={[{width: '100%', height: '100%'}, style as never]}
          />
        </View>
      );
    }

    default:
      return <View style={containerStyle} />;
  }
}
