import {RootState} from '@store/store';
import dayjs from 'dayjs';
import React, {useCallback, useMemo} from 'react';
import {View} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import {useSelector} from 'react-redux';

import {StoryTimelineTile, StoryTimelineTileProps} from './StoryTimelineTile';

import useUixQuery from '@/app/hooks/useUixQuery';
import {ScreenNames} from '@/app/navigation/constants';
import {NavigationService} from '@/app/navigation/NavigationService';
import {ScreenContainer} from '@/app/screens/components/ScreenContainer';
import {Colors} from '@/app/theme/colors';
import {UixButton} from '@/app/ui/UixButton';
import {HomeStoriesQuery} from '@/graphQL/home';
import {
  HomeStoriesQueryQuery,
  Stories_Story_Details,
} from '@/types/__generated__/graphql';

export function StoryTimelineScreen() {
  const userId = useSelector((store: RootState) => store.user.id);

  const {data} = useUixQuery<HomeStoriesQueryQuery>(HomeStoriesQuery, {
    variables: {
      member_id: userId,
    },
  });

  const stories: StoryTimelineTileProps[] = useMemo(() => {
    if (!data) return [];

    const allStories = data.stories_story_groups?.flatMap(
      i => (i?.story_details as Stories_Story_Details[]) ?? [],
    );

    const sortedStories = (allStories || []).sort(
      (a, b) => dayjs(b.created_at).valueOf() - dayjs(a.created_at).valueOf(),
    );

    return sortedStories.map((story, index) => ({
      storyDetail: story,
      formattedDate: dayjs(story?.created_at).format('DD MMM'),
      showDateHeader:
        index === 0 ||
        dayjs(story.created_at).format('DD MMM') !==
          dayjs(sortedStories[index - 1]?.created_at).format('DD MMM'),
    }));
  }, [data]);

  const renderItem = useCallback(
    ({item}: {item: StoryTimelineTileProps; index: number}) => {
      return <StoryTimelineTile {...item} />;
    },
    [],
  );

  return (
    <ScreenContainer
      name={ScreenNames.StoryTimeline}
      background="new"
      style={{
        backgroundColor: Colors.StoriesBackground,
      }}>
      <View
        style={{
          position: 'absolute',
          left: 40,
          top: 0,
          width: 2,
          bottom: 0,
          backgroundColor: Colors.Teal950,
        }}
      />
      <View
        style={{
          marginVertical: 16,
          paddingVertical: 4,
          backgroundColor: Colors.StoriesBackground,
          paddingHorizontal: 16,
          zIndex: 10,
        }}>
        <UixButton
          label="Back"
          tintColor="Teal"
          iconImage={require('@assets/backArrow.png')}
          onPress={() => {
            NavigationService.goBack();
          }}
        />
      </View>
      <FlatList
        renderItem={renderItem}
        style={{flex: 1}}
        data={stories}
        removeClippedSubviews={false}
      />
    </ScreenContainer>
  );
}
