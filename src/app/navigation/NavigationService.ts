import {
  CommonActions,
  createNavigationContainerRef,
  StackActions,
} from '@react-navigation/native';

import {ScreenNames} from './constants';
import {RootStackParamsList} from './types';

class _NavigationService {
  navigationRef = createNavigationContainerRef<RootStackParamsList>();

  navigate<T extends keyof RootStackParamsList>(
    name: T,
    params?: RootStackParamsList[T],
  ) {
    if (this.navigationRef.isReady()) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      this.navigationRef.navigate(name, params as any);
    }
  }

  replace(name: ScreenNames, params?: object) {
    if (this.navigationRef.isReady()) {
      this.navigationRef.dispatch(StackActions.replace(name, params));
    }
  }

  goBack() {
    if (this.navigationRef.isReady() && this.navigationRef.canGoBack()) {
      this.navigationRef.goBack();
    }
  }

  backToLogin() {
    if (this.navigationRef.isReady()) {
      this.navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{name: ScreenNames.Login}],
        }),
      );
    }
  }
}

export const NavigationService = new _NavigationService();
