import {IUserSlice} from '@store/slices/UserSlice';
import React from 'react';
import {Image, Pressable, View} from 'react-native';

import {ScreenNames} from '../constants';
import {NavigationService} from '../NavigationService';

import HomeStoriesWidget from '@/app/screens/home/<USER>/HomeStoriesWidget';
import {Colors} from '@/app/theme/colors';
import {useGetHeaderHeight} from '@/app/theme/hooks';
import {AbsoluteBlurView} from '@/app/ui/AbsoluteBlurView';
import {UixImage} from '@/app/ui/uixImage/UixImage';

export function Header({userDetails}: {userDetails: IUserSlice}) {
  const HEADER_HEIGHT = useGetHeaderHeight();

  const isMemberActive = (userDetails?.member_status ?? 0) % 2 === 1;

  return (
    <View style={{height: HEADER_HEIGHT, justifyContent: 'flex-end'}}>
      <AbsoluteBlurView />
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          padding: 16,
          alignItems: 'center',
          position: 'relative',
          backgroundColor: 'transparent',
        }}>
        <Image
          source={require('@assets/app_logo.png')}
          style={{width: 60, height: 24, resizeMode: 'cover'}}
        />
        <View style={{flex: 1}} />
        <HomeStoriesWidget />
        <Pressable
          onPress={() =>
            NavigationService.navigate(ScreenNames.MemberDetail, {
              id: userDetails?.id,
            })
          }>
          <UixImage
            key={userDetails.member_photo}
            size={44}
            resizeMode="cover"
            source={
              userDetails.member_photo
                ? {uri: userDetails.member_photo}
                : isMemberActive
                  ? require('@assets/avatars/default.png')
                  : require('@assets/avatars/defaultGrayscale.png')
            }
            style={{
              width: 44,
              height: 44,
              borderRadius: 25,
              borderWidth: 1,
              borderColor: Colors.Teal800,
              marginLeft: 10,
            }}
          />
        </Pressable>
      </View>
    </View>
  );
}
