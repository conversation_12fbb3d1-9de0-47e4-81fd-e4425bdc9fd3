import ContactIcon from '@assets/tabs/contacts.png';
import HomeIcon from '@assets/tabs/home.png';
import MaskedView from '@react-native-masked-view/masked-view';
import {BottomTabBarProps} from '@react-navigation/bottom-tabs';
import {View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Svg, {Circle, ClipPath, Defs, Rect} from 'react-native-svg';

import {ScreenNames} from '../../constants';
import {RootStackParamsList} from '../../types';
import {CenterTab} from './CenterTab';
import {Tab} from './Tab';

import {CENTER_TAB_RADIUS, TAB_BAR_HEIGHT} from '@/app/theme/constants';
import {AbsoluteBlurView} from '@/app/ui/AbsoluteBlurView';
import {isAndroid} from '@/app/utils/utils';

export const TabBar = ({state}: BottomTabBarProps) => {
  const {bottom} = useSafeAreaInsets();
  return (
    <View
      style={{
        paddingBottom: bottom,
        backgroundColor: 'transparent',
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
      }}>
      <MaskedView
        style={{
          flex: 1,
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 0,
          top: 0,
        }}
        maskElement={
          <View style={{flex: 1}}>
            <Svg width="100%" height="100%">
              <Defs>
                <ClipPath id="cutout">
                  <Rect width="100%" height="100%" fill="white" />
                  <Circle
                    cx="50%"
                    cy={16}
                    r={CENTER_TAB_RADIUS + 8}
                    fill="black"
                  />
                </ClipPath>
              </Defs>
              <Rect
                width="100%"
                height="100%"
                fill="white"
                clipPath="url(#cutout)"
              />
            </Svg>
          </View>
        }>
        <AbsoluteBlurView />
      </MaskedView>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          padding: isAndroid ? 8 : 16,
          paddingBottom: 0,
          height: TAB_BAR_HEIGHT,
        }}>
        <Tab
          icon={HomeIcon}
          name={ScreenNames.Home as keyof RootStackParamsList}
          label="Home"
          isSelected={state.index === 0}
        />
        <View
          style={{
            flex: 1,
            position: 'relative',
            paddingTop: 8,
            height: TAB_BAR_HEIGHT,
          }}>
          <CenterTab />
        </View>
        <Tab
          icon={ContactIcon}
          name={ScreenNames.Members as keyof RootStackParamsList}
          label="Members"
          isSelected={state.index === 1}
        />
      </View>
    </View>
  );
};
