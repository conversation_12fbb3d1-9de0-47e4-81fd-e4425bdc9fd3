import React from 'react';
import {ImageSourcePropType, Pressable} from 'react-native';
import Animated from 'react-native-reanimated';

import useAnimatedTab from './hooks';

import {NavigationService} from '@/app/navigation/NavigationService';
import {RootStackParamsList} from '@/app/navigation/types';
import {Colors} from '@/app/theme/colors';
import {UixText} from '@/app/ui/UixText';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export const Tab = ({
  icon,
  name,
  label,

  isSelected,
}: {
  name: keyof RootStackParamsList;
  label: string;
  isSelected: boolean;
  icon: ImageSourcePropType;
}) => {
  const {animatedContainerStyle, iconStyle, labelStyle} =
    useAnimatedTab(isSelected);

  return (
    <AnimatedPressable
      style={[
        {flex: 1, alignItems: 'center', justifyContent: 'center'},
        animatedContainerStyle,
      ]}
      onPress={() => {
        NavigationService.navigate(name);
      }}>
      <Animated.Image source={icon} style={iconStyle} />
      <Animated.View style={labelStyle}>
        <UixText
          variant="dotsBold"
          style={{
            fontSize: 16,
            lineHeight: 16,
            color: Colors.Teal,
          }}>
          {label}
        </UixText>
      </Animated.View>
    </AnimatedPressable>
  );
};
