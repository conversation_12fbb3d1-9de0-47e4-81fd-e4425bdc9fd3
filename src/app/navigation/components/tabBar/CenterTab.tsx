import {Pressable, View} from 'react-native';
import Video from 'react-native-video';

import {ScreenNames} from '../../constants';
import {NavigationService} from '../../NavigationService';

import {Colors} from '@/app/theme/colors';
import {CENTER_TAB_RADIUS} from '@/app/theme/constants';
import {isAndroid} from '@/app/utils/utils';

export const CenterTab = () => {
  return (
    <Pressable onPress={() => NavigationService.navigate(ScreenNames.Drops)}>
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <View
          style={{
            height: CENTER_TAB_RADIUS * 2,
            width: CENTER_TAB_RADIUS * 2,
            backgroundColor: Colors.Black,
            overflow: 'hidden',
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: CENTER_TAB_RADIUS,
            borderWidth: 2,
            borderColor: Colors.Teal,
          }}>
          <Video
            source={isAndroid ? {uri: 'raw/cube'} : require('@assets/cube.mp4')}
            controls={false}
            muted
            disableFocus={true}
            onError={error => console.error('Video Error:', error)}
            resizeMode="cover"
            repeat={true}
            style={{
              width: 80,
              height: 80,
            }}
          />
        </View>
      </View>
    </Pressable>
  );
};
