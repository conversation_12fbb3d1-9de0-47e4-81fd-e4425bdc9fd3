import {useEffect} from 'react';
import {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

export default function useAnimatedTab(isSelected: boolean) {
  const animated = useSharedValue(isSelected ? 1 : 0);

  useEffect(() => {
    animated.value = withTiming(isSelected ? 1 : 0, {
      duration: 300,
    });
  }, [isSelected, animated]);

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animated.value, [0, 1], [0.4, 1]),
    };
  });

  const labelStyle = useAnimatedStyle(() => {
    return {
      marginTop: 4,
      height: interpolate(animated.value, [0, 1], [0, 20]),
    };
  });

  const iconStyle = useAnimatedStyle(() => {
    return {
      width: interpolate(animated.value, [0, 1], [32, 24]),
      height: interpolate(animated.value, [0, 1], [32, 24]),
    };
  });

  return {animatedContainerStyle, labelStyle, iconStyle};
}
