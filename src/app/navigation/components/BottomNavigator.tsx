import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {RootState} from '@store/store';
import {useSelector} from 'react-redux';

import {ScreenNames} from '../constants';
import {Header} from './Header';
import {TabBar} from './tabBar/TabBar';

import HomeScreen from '@/app/screens/home/<USER>';
import {MembersScreen} from '@/app/screens/members/MembersScreen';

const BottomTab = createBottomTabNavigator();

export const BottomNavigator = () => {
  const userDetails = useSelector((store: RootState) => store.user);
  return (
    <BottomTab.Navigator
      screenOptions={{
        headerTransparent: true,
        headerShown: true,
        header: () => <Header userDetails={userDetails} />,
      }}
      tabBar={props => <TabBar {...props} />}>
      <BottomTab.Screen name={ScreenNames.Home} component={HomeScreen} />
      <BottomTab.Screen name={ScreenNames.Members} component={MembersScreen} />
    </BottomTab.Navigator>
  );
};
