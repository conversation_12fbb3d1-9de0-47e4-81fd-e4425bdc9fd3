import {
  getStateFromPath as defaultGetStateFromPath,
  LinkingOptions,
} from '@react-navigation/native';
import {Linking} from 'react-native';

import {RootStackParamsList} from './types';

const linking: LinkingOptions<RootStackParamsList> = {
  prefixes: ['uix://', 'https://uix.app'],

  getInitialURL: () => Linking.getInitialURL(),

  config: {
    screens: {
      Drops: 'drops',
      DropDetail: 'drop/:id',
      Stories: {path: 'stories/:id?', parse: {id: String}},
      Network: 'network',
      PlayBook: `playbook`,
      MemberDetail: 'member/:id',
      App: {
        initialRouteName: 'Home',
        screens: {
          Members: 'members',
          Home: {
            path: 'home',
            screens: {
              ReferMember: 'refer-member',
              ReferProject: 'refer-project',
            },
          },
        },
      },
    },
  },

  subscribe(listener) {
    const onReceiveURL = ({url}: {url: string}) => listener(url);
    const subscription = Linking.addEventListener('url', onReceiveURL);
    return () => subscription.remove();
  },

  getStateFromPath(path, options) {
    const state = defaultGetStateFromPath(path, options);

    if (!state) return undefined;

    const route = state.routes[0];

    const stackOnAppScreens = [
      'DropDetail',
      'MemberDetail',
      'Stories',
      'PlayBook',
      'Drops',
      'Network',
    ];

    if (stackOnAppScreens.includes(route.name)) {
      return {
        ...state,
        routes: [{name: 'App'}, route],
        index: 1,
      };
    }

    return state;
  },
};

export default linking;
