import {NavigatorScreenParams} from '@react-navigation/native';

import {ScreenNames} from './constants';

import {Members_Member_Details} from '@/types/__generated__/graphql';

type BottomParamsList = {
  [ScreenNames.Home]: {
    screen?: 'refer-member' | 'refer-project';
  };
  [ScreenNames.Members]: undefined;
};

export type RootStackParamsList = {
  [ScreenNames.Home]: {
    screen?: 'refer-member' | 'refer-project';
  };
  [ScreenNames.Members]: undefined;

  [ScreenNames.App]: NavigatorScreenParams<BottomParamsList>;
  [ScreenNames.AccessRequest]: {userId: string};
  [ScreenNames.Login]: undefined;
  [ScreenNames.OtpVerification]: {phone: string};
  [ScreenNames.Splash]: undefined;

  [ScreenNames.EnterMobile]: undefined;
  [ScreenNames.Network]: undefined;
  [ScreenNames.Drops]: undefined;
  [ScreenNames.PlayBook]: undefined;
  [ScreenNames.Stories]: {id?: string};
  [ScreenNames.StoryTimeline]: undefined;
  [ScreenNames.DropDetail]: {id: string};

  [ScreenNames.MemberDetail]: {
    id: string;
    member?: Members_Member_Details;
  };
  [ScreenNames.EditSkills]: {skillId: string} | undefined;
  [ScreenNames.AddSkill]: undefined;
  [ScreenNames.Engagement]: undefined;
  [ScreenNames.LogEntries]: {
    totalLoggedTime?: number;
  };
};
