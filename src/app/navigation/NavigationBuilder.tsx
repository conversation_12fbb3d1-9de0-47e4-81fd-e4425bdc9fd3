import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {NavigationContainer} from '@react-navigation/native';
import React from 'react';
import BootSplash from 'react-native-bootsplash';
import {PaperProvider} from 'react-native-paper';

import {AppStack} from '../screens/components/AppStack';
import theme from '../theme/theme';
import linking from './linking';
import {NavigationService} from './NavigationService';

import {DevToolButton} from '@/app/screens/components/DevToolButton';

export const Navigation = () => {
  return (
    <PaperProvider
      settings={{
        rippleEffectEnabled: false,
      }}
      theme={theme}>
      <BottomSheetModalProvider>
        <NavigationContainer
          onReady={() => {
            BootSplash.hide({fade: true});
          }}
          ref={NavigationService.navigationRef}
          linking={linking}>
          <AppStack />
        </NavigationContainer>
        <DevToolButton />
      </BottomSheetModalProvider>
    </PaperProvider>
  );
};
