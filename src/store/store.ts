import AsyncStorage from '@react-native-async-storage/async-storage';
import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {persistReducer, persistStore} from 'redux-persist';

import CommonSlice from './slices/CommonSlice';
import UserSlice from './slices/UserSlice';

import LogEntriesSlice from '@/app/screens/home/<USER>/LogEntries/log-entries-slice/LogEntriesSlice';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  debug: __DEV__,
  blacklist: ['intermittent'],
};

const rootReducer = combineReducers({
  common: CommonSlice,
  user: UserSlice,
  logEntries: LogEntriesSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const persister = persistStore(store);
export default store;
