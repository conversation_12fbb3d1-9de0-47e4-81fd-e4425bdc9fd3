import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {UserLogoutAction} from '@store/CommonActions';

import {MemberDetails} from '@/types/__generated__/graphql';

export interface IUserSlice extends MemberDetails {
  id: string;
  firstName?: string;
  lastName?: string;
  mobile?: string;
  email?: string;
  dob?: string;
}

const DefaultState: IUserSlice = {
  id: '',
  login_user_infos: [],
  member_badges: [],
  member_projects: [],
  member_skills: [],
  user_permissions: [],
  onboarded: false,
};

const UserSlice = createSlice({
  name: 'user',
  initialState: DefaultState,
  reducers: {
    updateUserDetails: (state, data: PayloadAction<IUserSlice>) => {
      return {
        ...state,
        ...data.payload,
      };
    },
  },

  extraReducers(builder) {
    builder.addCase(UserLogoutAction.type, () => {
      return DefaultState;
    });
  },
});

export default UserSlice.reducer;
export const UserActions = UserSlice.actions;
