import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {UserLogoutAction} from '@store/CommonActions';

export interface ICommonSlice {
  isLogin: boolean;
  isFirstLaunch: boolean;
  onboardingCompleted: boolean;
}

const DefaultState: ICommonSlice = {
  isLogin: false,
  isFirstLaunch: true,
  onboardingCompleted: false,
};

const CommonSlice = createSlice({
  name: 'common',
  initialState: DefaultState,
  reducers: {
    updateFirstLaunch: (state, data: PayloadAction<boolean>) => {
      state.isFirstLaunch = data.payload;
    },

    updateOnboardingCompleted: (state, data: PayloadAction<boolean>) => {
      state.onboardingCompleted = data.payload;
    },

    updateIsLogin: (state, data: PayloadAction<boolean>) => {
      state.isLogin = data.payload;
    },
  },

  extraReducers(builder) {
    builder.addCase(UserLogoutAction.type, () => {
      return DefaultState;
    });
  },
});

export default CommonSlice.reducer;

export const CommonActions = CommonSlice.actions;
