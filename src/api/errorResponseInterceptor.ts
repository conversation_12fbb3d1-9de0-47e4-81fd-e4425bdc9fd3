import {AxiosError} from 'axios';

enum StatusCode {
  BadRequest = 400,
  Unauthorized = 401,
  Forbidden = 403,
  TooManyRequests = 429,
  InternalServerError = 500,
}

// TODO: Handle global app errors
// We can handle generic app errors depending on the status code
export function errorResponseInterceptor(response: AxiosError) {
  const {status} = response;

  switch (status) {
    case StatusCode.InternalServerError: {
      // Handle InternalServerError
      break;
    }
    case StatusCode.BadRequest: {
      // Handle Bad request
      break;
    }
    case StatusCode.Forbidden: {
      // Handle Forbidden
      break;
    }
    case StatusCode.Unauthorized: {
      // Handle Unauthorized
      // removeAuthToken();
      // globalNavigateTo("LogoutScreen");
      break;
    }
    case StatusCode.TooManyRequests: {
      // Handle TooManyRequests
      break;
    }
  }

  throw response;
}
