import Utils from '@/app/utils/utils';
import {AxiosResponse} from 'axios';

interface ResponseFormat {
  data: unknown;
  statusCode: number;
  status: {
    description: string;
    message: 'SUCCESS' | 'FAILURE';
  };
}

function hasTokenExpired(data: any) {
  return data?.errors?.[0]?.message === 'Could not verify JWT: JWTExpired';
}

export async function responseInterceptor(
  response: AxiosResponse,
): Promise<AxiosResponse> {
  const data: ResponseFormat = response.data as ResponseFormat;

  const statusCode = response.status;

  if (hasTokenExpired(data)) {
    Utils.onLogout();
    return response;
  }

  if (statusCode === 200) {
    return response;
  }

  throw new Error(data.status?.description ?? 'Something went wrong');
}
