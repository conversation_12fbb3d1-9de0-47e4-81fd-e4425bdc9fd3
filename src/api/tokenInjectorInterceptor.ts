import EncryptedStore from '@/app/utils/encryptedStore';
import {InternalAxiosRequestConfig} from 'axios';

export const injectToken = async (
  config: InternalAxiosRequestConfig,
): Promise<InternalAxiosRequestConfig> => {
  try {
    const token = await EncryptedStore.getValue('token', '');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  } catch (error: any) {
    throw new Error(error);
  }
};
