import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';

import {errorResponseInterceptor} from './errorResponseInterceptor';
import {responseInterceptor} from './responseInterceptor';
import {injectToken} from './tokenInjectorInterceptor';

class Http {
  baseUrl = 'REST_API_URL';

  private instance: AxiosInstance | null = null;

  private get http(): AxiosInstance {
    return this.instance != null ? this.instance : this.initHttp();
  }

  initHttp() {
    const http = axios.create({
      baseURL: this.baseUrl,
      withCredentials: false,
    });

    http.interceptors.request.use(injectToken);

    http.interceptors.response.use(
      responseInterceptor,
      errorResponseInterceptor,
    );

    this.instance = http;
    return http;
  }

  refreshInstance(): void {
    this.initHttp();
  }

  request<T = any, R = AxiosResponse<T>>(
    config: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.request(config);
  }

  get<T = any, R = AxiosResponse<T>>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.get<T, R>(url, config);
  }

  post<R = AxiosResponse<any>>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.post<any, R>(url, data, config);
  }

  put<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.put<T, R>(url, data, config);
  }

  delete<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.delete<T, R>(url, {
      ...config,
      data,
    });
  }
}

const HttpClient = new Http();
export default HttpClient;
