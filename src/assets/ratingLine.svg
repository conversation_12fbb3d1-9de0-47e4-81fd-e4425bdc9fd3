<svg width="325" height="20" viewBox="0 0 325 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_2005_70)">
<rect width="325" height="20" rx="5" fill="#00FFD1" fill-opacity="0.1"/>
</g>
<rect x="6" y="6" width="313" height="8" rx="2" fill="#00FFD1" fill-opacity="0.1"/>
<defs>
<filter id="filter0_i_2005_70" x="0" y="0" width="325" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect1_innerShadow_2005_70"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2005_70"/>
</filter>
</defs>
</svg>
