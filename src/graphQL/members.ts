import {gql} from '@apollo/client';

export const MembersList = gql`
  query MembersList {
    members_member_details(
      where: {_and: [{alias: {_neq: ""}}, {onboarded: {_eq: true}}]}
    ) {
      career_starting_year
      alias
      id
      created_at
      joining_date
      type {
        name
      }
      designation
      member_status
      member_photo
      member_skills {
        id
        rating
        skill_detail {
          title
          type
          id
        }
      }
      member_photo
    }
    members_member_details_aggregate(
      where: {_and: [{alias: {_neq: ""}}, {onboarded: {_eq: true}}]}
    ) {
      aggregate {
        count
      }
    }
  }
`;

export const Newbies = gql`
  query NewBies($startTime: date!) {
    members_member_details(
      where: {
        _and: [{joining_date: {_gte: $startTime}}, {onboarded: {_eq: true}}]
      }
    ) {
      id
      alias
    }
  }
`;
