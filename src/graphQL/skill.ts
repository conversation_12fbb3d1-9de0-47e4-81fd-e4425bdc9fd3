import {gql} from '@apollo/client';

export const AddMemberSkill = gql`
  mutation AddMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {
    insert_members_member_skills_one(
      object: {member_id: $id, rating: $rating, skill_id: $skillId}
    ) {
      id
      skill_id
      member_id
    }
  }
`;

export const DeleteMemberSkill = gql`
  mutation DeleteMemberSkill($id: uuid!, $skillId: uuid!) {
    delete_members_member_skills(
      where: {skill_id: {_eq: $skillId}, member_id: {_eq: $id}}
    ) {
      returning {
        id
        skill_id
      }
    }
  }
`;

export const UpdateMemberSkill = gql`
  mutation UpdateMemberSkill($id: uuid!, $skillId: uuid!, $rating: Int) {
    update_members_member_skills(
      _set: {rating: $rating}
      where: {member_id: {_eq: $id}, skill_id: {_eq: $skillId}}
    ) {
      returning {
        id
        skill_id
      }
    }
  }
`;
