import {gql} from '@apollo/client';

export const HomeQuery = gql`
  query HomeQuery(
    $joiningStartTime: date!
    $member_id: uuid
    $dropDetailcurrentDate: timestamptz!
  ) {
    members_member_details(where: {_and: [{id: {_eq: $member_id}}]}) {
      id
      alias
      bio
      member_photo
      member_badges {
        badge_id
        badge_detail {
          badge_name
          badge_media
        }
      }
      member_status
      type {
        id
        name
      }
    }

    recently_joined_members: members_member_details(
      where: {
        _and: [
          {joining_date: {_gte: $joiningStartTime}}
          {onboarded: {_eq: true}}
        ]
      }
    ) {
      id
      alias
    }

    drops_drop_details_aggregate(
      where: {_and: [{expires_at: {_gt: $dropDetailcurrentDate}}]}
    ) {
      aggregate {
        count
      }
    }

    app_ui_homepage_banners {
      id
      banner_color
      banner_description
      banner_order
      banner_title
      action_type
      action_url
    }

    stories_story_groups {
      id
      title
      story_details(order_by: [{story_order: asc}]) {
        id
        cover_media
        created_at
        link
        title
        media_type
        duration
        isReadMore
        isSeen
        story_group_id
        showOverlay
        story_group {
          id
          title
        }
        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {
          aggregate {
            count
          }
        }
      }
    }
  }
`;

export const HomeStoriesQuery = gql`
  query HomeStoriesQuery($member_id: uuid!) {
    stories_story_groups {
      id
      title
      story_details(order_by: [{story_order: asc}]) {
        id
        cover_media
        created_at
        link
        title
        media_type
        duration
        isReadMore
        isSeen
        story_group_id
        showOverlay
        story_group {
          id
          title
        }
        seen_by_aggregate(where: {member_id: {_eq: $member_id}}) {
          aggregate {
            count
          }
        }
      }
    }
  }
`;

export const markSeenMutation = gql`
  mutation markSeen($member_id: uuid!, $story_id: uuid!) {
    insert_members_member_stories_one(
      object: {member_id: $member_id, story_id: $story_id}
    ) {
      id
    }
  }
`;
