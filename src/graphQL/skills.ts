import {gql} from '@apollo/client';

export const GetSkillsQuery = gql`
  query GetSkillsQuery($search: String) {
    skills_skill_details(
      limit: 100
      where: {
        _or: [{title: {_ilike: $search}}, {search_code: {_ilike: $search}}]
      }
    ) {
      id
      title
      search_code
      type
    }
  }
`;

export const CreateSkill = gql`
  mutation CreateSkill($title: String!, $searchCode: String) {
    insert_skills_skill_details_one(
      object: {title: $title, search_code: $searchCode, type: "custom"}
    ) {
      id
      title
      search_code
      type
    }
  }
`;
