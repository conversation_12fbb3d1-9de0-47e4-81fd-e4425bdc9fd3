import {gql} from '@apollo/client';

export const AssignedBadgeQuery = gql`
  query AssignedBadgeQuery($id: uuid!) {
    members_member_badges(where: {badge_id: {_eq: $id}}) {
      created_at
      member_detail {
        alias
      }
    }
  }
`;

export const GetBadges = gql`
  query GetBadgeDetails {
    badges_badge_details {
      badge_description
      badge_media
      badge_name
      id
    }
  }
`;
