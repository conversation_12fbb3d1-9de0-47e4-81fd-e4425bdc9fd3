import {gql} from '@apollo/client';

export const getLeads = gql`
  query GetLeads($limit: Int!, $offset: Int!, $id: uuid!, $status: leads_lead_status_enum) {
  members_member_details_by_pk(id:$id) {
    lead_dealmaker_hunters(limit: $limit, offset: $offset, order_by: {created_at: desc_nulls_last}, where: {lead_detail: {lead_status: {_eq: $status}}}) {
      id
     member_role
      lead_detail {
        builder_type
        created_at
        description
        email
        industry
        id
        first_name
        lead_company
        last_name
        lead_hunter
        lead_status
        lead_type
        linkedin_url
        mobile_number
        phone_number
        updated_at
        source
      }
    }
  lead_dealmaker_hunters_aggregate(where:{lead_detail:{lead_status:{_eq:$status}}}){
    aggregate{
      count
    }
  }
  }
}
`;

export const deleteLead = gql`
  mutation DeleteLead($id: uuid!) {
    delete_leads_lead_details_by_pk(id: $id) {
      id
    }
  }
`;

export const updateLead = gql`
  mutation UpdateLead($id: uuid!) {
    update_leads_lead_details_by_pk(pk_columns: {id: $id}) {
      description
    }
  }
`;

export const getLeadStatusCount = gql`
 query GetLeadStatusCount($id: uuid!, $status: leads_lead_status_enum) {
  members_member_details_by_pk(id:$id) {
   lead_dealmaker_hunters_aggregate(where:{lead_detail:{lead_status:{_eq:$status}}}){
    aggregate{
      count
    }
  }
  }
}
`;