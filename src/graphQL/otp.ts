import {gql} from '@apollo/client';

export const GenerateOTP = gql`
  mutation GenerateOTP(
    $phone: String!
    $purpose: String = "login"
    $country_code: String = "91"
  ) {
    generateOTP(
      arg1: {
        phone_number: $phone
        purpose: $purpose
        country_code: $country_code
      }
    ) {
      message
      token
      status
      data {
        phone_number
        purpose
        user_id
      }
    }
  }
`;

export const VerifyOTP = gql`
  mutation VerifyOTP(
    $phone_number: String!
    $otp: Int!
    $country_code: String = "91"
  ) {
    verifyOTP(
      arg1: {
        otp: $otp
        phone_number: $phone_number
        country_code: $country_code
      }
    ) {
      data {
        user_id
        member_details {
          alias
          bio
          department
          first_name
          id
          last_name
          level
          type {
            name
          }
          linkedin_profile
          member_engagement
          member_photo
          member_role
          member_status
          phone_number
          uix_email
          login_user_infos {
            id
            last_seen
            member_id
            mobile
          }
          member_badges {
            badge_detail {
              badge_media
              badge_name
              id
            }
          }
          member_projects {
            project_detail {
              id
              project_logo
              project_name
            }
          }
          member_skills {
            skill_detail {
              id
              title
              type
            }
          }
        }
      }
      message
    }
  }
`;
