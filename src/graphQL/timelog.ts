import {gql} from '@apollo/client';

export const addTimelogs = gql(`
  mutation AddTimeLogs($data: [members_member_timelogs_insert_input!]!) {
    insert_members_member_timelogs(objects: $data) {
      returning {
        id
      }
    }
  }
`);

export const getTimelogs = gql(`
  query GetTimelogs(
    $id: uuid!
    $limit: Int
    $offset: Int
    $status:members_member_timelog_statuses_enum
    $projectIds: [uuid!]
    $startDate: date
    $endDate: date
  ) {
    members_member_timelogs (
      where: {
        member_id: { _eq: $id }
        _and: [
          { status: { _eq: $status } }
          { project_id: { _in: $projectIds } }
          { actual_date: { _gte: $startDate } }
          { actual_date: { _lte: $endDate } }
        ]
      }
      order_by:[
        { actual_date: desc_nulls_last }
        { created_at: desc_nulls_last }
        { id: desc_nulls_last }
      ]
      limit: $limit
      offset: $offset
    ) {
        no_of_hours
        member_id
        project_id
        status
        work_description
        created_at
        actual_date
        project_detail{
          id
          project_logo
          project_name
        }
        id
    }
    
    # Get total count for pagination
    members_member_timelogs_aggregate (
      where: {
        member_id: { _eq: $id }
        _and: [
          { status: { _eq: $status } }
          { project_id: { _in: $projectIds } }
          { actual_date: { _gte: $startDate } }
          { actual_date: { _lte: $endDate } }
        ]
      }
    ) {
      aggregate {
        count
      }
    }
  }
`);

export const updateTimelogs = gql`
  mutation UpdateTimelogs(
    $id: uuid!
    $work_description: String = ""
    $no_of_hours: numeric = ""
  ) {
    update_members_member_timelogs_by_pk(
      pk_columns: {id: $id}
      _set: {work_description: $work_description, no_of_hours: $no_of_hours}
    ) {
      id
    }
  }
`;

export const deleteTimelogs = gql`
  mutation DeleteTimelogs($id: uuid!) {
    delete_members_member_timelogs_by_pk(id: $id) {
      id
    }
  }
`;
