import {gql} from '@apollo/client';

export const MemberDetailQuery = gql`
  query MemberDetails($id: uuid!) {
    GetRevenueGraphDataForApp(arg1: {member_id: $id}) {
      status
      message
      data {
        slot
        label
        hours
        revenue
      }
    }
    members_member_details_by_pk(id: $id) {
      alias
      bio
      department
      designation
      created_at
      first_name
      id
      last_name
      level
      linkedin_profile
      member_badges {
        badge_detail {
          badge_description
          badge_name
          badge_media
          id
        }
      }
      member_bank_details {
        account_number
        bank_account_name
        ifsc_code
      }
      member_engagement
      member_photo
      member_projects {
        project_detail {
          cover_image
          project_name
          project_logo
          project_description
          id
        }
      }
      member_role
      member_skills {
        id
        rating
        skill_detail {
          title
          type
          id
        }
      }
      member_retainers {
        id
        hours
        days
        retainer_type
        member_retainer_type {
          name
        }
      }
      member_status
      member_type
      pan_card_link
      personal_email
      phone_number
      referred_by
      resume_link
      salary
      uix_email
      updated_at
      joining_date
      post_details {
        post_content
        post_subject
        posted_by
        type
        id
      }
      member_projects_aggregate(where: {member_id: {_eq: $id}}) {
        aggregate {
          count
        }
      }
      member_projects {
        project_detail {
          cover_image
          project_name
          project_logo
          project_description
          id
        }
      }
      lead_dealmaker_hunters_aggregate(where: {member_id: {_eq: $id}}) {
        aggregate {
          count
        }
      }
    }
  }
`;

export const MemberProject = gql`
  query MemberProject($id: uuid!) {
    members_member_projects(where: {member_id: {_eq: $id}}) {
      project_detail {
        project_name
        id
        project_logo
      }
    }
  }
`;

export const MemberAvailability = gql`
  mutation MemberAvailabilityStatusChange($id: uuid!, $member_status: Int) {
    update_members_member_details_by_pk(
      pk_columns: {id: $id}
      _set: {member_status: $member_status}
    ) {
      id
    }
  }
`;

export const MemberEarningsQuery = gql`
  query getTotalRevenue($member_id: uuid!, $project_id: uuid) {
    get_total_revenue(args: {member_id: $member_id, project_id: $project_id}) {
      hours
      rate
    }
  }
`;

export const deleteMember = gql`
  mutation deleteMember($id: uuid!) {
    update_members_member_details_by_pk(
      pk_columns: {id: $id}
      _set: {onboarded: false}
    ) {
      id
    }
  }
`;

export const UpdateMemberRetainer = gql`
  mutation UpdateMemberRetainer(
    $member_id: uuid!
    $hours: Int!
    $days: [String!]!
    $retainer_type: Int!
  ) {
    insert_members_member_retainer_one(
      object: {
        member_id: $member_id
        hours: $hours
        days: $days
        retainer_type: $retainer_type
      }
      on_conflict: {
        constraint: member_retainer_member_id_key
        update_columns: [hours, days, retainer_type]
      }
    ) {
      id
      hours
      days
      member_retainer_type {
        name
      }
    }
  }
`;

export const GetMemberRetainer = gql`
  query GetMemberRetainer($member_id: uuid!) {
    members_member_retainer(where: {member_id: {_eq: $member_id}}) {
      id
      hours
      days
      retainer_type
      member_retainer_type {
        name
      }
    }
  }
`;
