import {gql} from '@apollo/client';

const DropsList = gql`
  query DropsList($limit: Int!, $offset: Int!) {
    drops_drop_details(
      order_by: {expires_at: desc}
      limit: $limit
      offset: $offset
    ) {
      id
      type
      drop_summary
      expires_at
      title
      media_url
      created_by
      created_at
      surge
      engagement_type
      drop_members {
        member_id
      }
    }
  }
`;

export const DropById = gql`
  query DropById($dropId: uuid!) {
    drops_drop_details(where: {id: {_eq: $dropId}}) {
      id
      type
      drop_summary
      expires_at
      title
      media_url
      created_by
      created_at
      surge
      engagement_type
      drop_members {
        member_id
      }
    }
  }
`;

export default DropsList;

export const createDropInterest = gql`
  mutation AddDropInterest($drop_id: uuid!, $member_id: uuid!) {
    insert_drops_drop_members_one(
      object: {drop_id: $drop_id, interest_type: 1, member_id: $member_id}
    ) {
      id
    }
  }
`;

export const DropInterestsQuery = gql`
  query DropInterestQuery($drop_id: uuid!, $member_id: uuid!) {
    drops_drop_members(
      where: {drop_id: {_eq: $drop_id}, member_id: {_eq: $member_id}}
    ) {
      drop_id
      member_id
      interest_type
    }
  }
`;
