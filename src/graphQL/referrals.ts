import {gql} from '@apollo/client';

export const createMemberReferral = gql`
  mutation CreateMemberReferral(
    $first_name: String
    $last_name: String
    $linkedin_url: String
    $description: String
    $referred_by: uuid
    $phone_number: String
    $email: String
    $resume_link: String
    $status: members_member_referral_statuses_enum = RECEIVED
  ) {
    insert_members_member_referrals_one(
      object: {
        first_name: $first_name
        last_name: $last_name
        linkedin_url: $linkedin_url
        description: $description
        referred_by: $referred_by
        phone_number: $phone_number
        status: $status
        email: $email
        resume_link: $resume_link
        source: APP
      }
    ) {
      id
    }
  }
`;

export const createProjectReferral = gql`
  mutation CreateProjectReferral(
    $first_name: String
    $last_name: String
    $description: String
    $linkedin_url: String
    $phone_number: String
    $member_id: uuid!
  ) {
    insert_leads_lead_details_one(
      object: {
        first_name: $first_name
        last_name: $last_name
        description: $description
        linkedin_url: $linkedin_url
        phone_number: $phone_number
        lead_dealmaker_hunters: {
          data: {member_id: $member_id, member_role: <PERSON>}
        }
      }
    ) {
      id
    }
  }
`;

export const createDropReferral = gql`
  mutation DropReferMember(
    $drop_id: uuid
    $first_name: String
    $last_name: String
    $description: String
    $department: members_member_department_enum
    $phone_number: String
    $linkedin_url: String
    $referred_by: uuid
    $resume_link: String = ""
    $email: String
  ) {
    insert_drops_drop_members_one(
      object: {
        drop_id: $drop_id
        interest_type: 1
        referral: {
          data: {
            first_name: $first_name
            last_name: $last_name
            department: $department
            description: $description
            linkedin_url: $linkedin_url
            phone_number: $phone_number
            referred_by: $referred_by
            resume_link: $resume_link
            status: RECEIVED
            email: $email
          }
        }
      }
    ) {
      id
    }
  }
`;
