import * as Sentry from '@sentry/react-native';
import {Platform} from 'react-native';
import SpInAppUpdates, {
  IAUInstallStatus,
  IAUUpdateKind,
  StartUpdateOptions,
} from 'sp-react-native-in-app-updates';

export const checkForUpdate = async () => {
  // Skip update check in development mode
  if (__DEV__) {
    console.log('In-app update check skipped in development mode');
    return;
  }

  const inAppUpdates = new SpInAppUpdates(
    false, // isDebug
  );

  try {
    const result = await inAppUpdates.checkNeedsUpdate();

    if (result.shouldUpdate) {
      let updateOptions: StartUpdateOptions = {};

      if (Platform.OS === 'android') {
        updateOptions = {
          updateType: IAUUpdateKind.IMMEDIATE,
        };
      } else if (Platform.OS === 'ios') {
        updateOptions = {
          title: 'Update available',
          message:
            'There is a new version of the app available on the App Store, do you want to update it?',
          buttonUpgradeText: 'Update',
          buttonCancelText: 'Cancel',
          forceUpgrade: true,
        };
      }

      inAppUpdates.addStatusUpdateListener(status => {
        if (status.status === IAUInstallStatus.DOWNLOADED) {
          inAppUpdates.installUpdate();
        }
      });

      inAppUpdates.startUpdate(updateOptions);
    }
  } catch (error) {
    console.log('Error checking for updates:', error);
    Sentry.captureException(error);
  }
};
