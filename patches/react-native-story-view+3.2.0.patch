diff --git a/node_modules/react-native-story-view/lib/components/StoryView/ProgressiveImage.js b/node_modules/react-native-story-view/lib/components/StoryView/ProgressiveImage.js
index a13425a..0ef7310 100644
--- a/node_modules/react-native-story-view/lib/components/StoryView/ProgressiveImage.js
+++ b/node_modules/react-native-story-view/lib/components/StoryView/ProgressiveImage.js
@@ -1,6 +1,8 @@
 import React from 'react';
 import { Animated, View } from 'react-native';
 import styles from './styles';
+import FastImage from 'react-native-fast-image';
+const AnimatedFastImage = Animated.createAnimatedComponent(FastImage);
 const ProgressiveImage = (props) => {
     const thumbnailAnimated = new Animated.Value(0.2);
     const { thumbnailSource, imgSource, viewStyle, ...reset } = props;
@@ -18,8 +20,8 @@ const ProgressiveImage = (props) => {
         }).start();
     };
     return (React.createElement(View, { style: styles.progressiveImageContainer },
-        React.createElement(Animated.Image, { ...reset, source: thumbnailSource, style: [styles.imageOverlay, viewStyle, { opacity: thumbnailAnimated }], onLoad: handleThumbnailLoad }),
-        React.createElement(Animated.Image, { ...reset, source: imgSource, style: [{ opacity: imageAnimated }, viewStyle], onLoad: onImageLoad, onLoadEnd: () => props.onImageLoaded && props.onImageLoaded() })));
+        React.createElement(AnimatedFastImage, { ...reset, source: thumbnailSource, style: [styles.imageOverlay, viewStyle, { opacity: thumbnailAnimated }], onLoad: handleThumbnailLoad }),
+        React.createElement(AnimatedFastImage, { ...reset, source: imgSource, style: [{ opacity: imageAnimated }, viewStyle], onLoad: onImageLoad, onLoadEnd: () => props.onImageLoaded && props.onImageLoaded() })));
 };
 export default ProgressiveImage;
 //# sourceMappingURL=ProgressiveImage.js.map
\ No newline at end of file
diff --git a/node_modules/react-native-story-view/lib/components/StoryView/StoryView.js b/node_modules/react-native-story-view/lib/components/StoryView/StoryView.js
index 6a3d705..b74faf2 100644
--- a/node_modules/react-native-story-view/lib/components/StoryView/StoryView.js
+++ b/node_modules/react-native-story-view/lib/components/StoryView/StoryView.js
@@ -6,9 +6,10 @@ import { Colors, Metrics } from '../../theme';
 import ProgressiveImage from './ProgressiveImage';
 import styles from './styles';
 import { StroyTypes } from './types';
-const BUFFER_TIME = 1000 * 60;
+const BUFFER_TIME = 1000;
 const StoryView = (props) => {
     var _a, _b, _c, _d;
+    const [videoError, setVideoError] = useState(false);
     const [loading, setLoading] = useState(true);
     const [buffering, setBuffering] = useState(true);
     const source = (_a = props === null || props === void 0 ? void 0 : props.stories) === null || _a === void 0 ? void 0 : _a[props === null || props === void 0 ? void 0 : props.progressIndex];
@@ -40,10 +41,11 @@ const StoryView = (props) => {
     const { height, width } = useWindowDimensions();
     return (React.createElement(View, { style: [styles.divStory, { height, width }], ref: props === null || props === void 0 ? void 0 : props.viewRef }, (source === null || source === void 0 ? void 0 : source.type) === StroyTypes.Image ? (React.createElement(ProgressiveImage, { viewStyle: (_b = props === null || props === void 0 ? void 0 : props.imageStyle) !== null && _b !== void 0 ? _b : styles.imgStyle, imgSource: { uri: (_c = source.url) !== null && _c !== void 0 ? _c : '' }, thumbnailSource: { uri: (_d = source.url) !== null && _d !== void 0 ? _d : '' }, onImageLoaded: props.onImageLoaded })) : (isCurrentIndex && (React.createElement(React.Fragment, null,
         React.createElement(Video, { ref: videoRef, resizeMode: "contain", paused: props.pause || loading, source: {
-                uri: convertToProxyURL({
+                uri: videoError ? source.url : convertToProxyURL({
                     url: source === null || source === void 0 ? void 0 : source.url,
                 }),
             }, onEnd: props === null || props === void 0 ? void 0 : props.onVideoEnd, onError: (_error) => {
+                setVideoError(true);
                 setLoading(false);
             }, onProgress: data => {
                 var _a;
@@ -52,6 +54,7 @@ const StoryView = (props) => {
                 }
             }, bufferConfig: {
                 minBufferMs: BUFFER_TIME,
+                maxBufferMs: BUFFER_TIME,
                 bufferForPlaybackMs: BUFFER_TIME,
                 bufferForPlaybackAfterRebufferMs: BUFFER_TIME,
             }, onBuffer: onBuffer, onLoadStart: onLoadStart, onLoad: (item) => {
