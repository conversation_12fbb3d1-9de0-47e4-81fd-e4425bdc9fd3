import 'react-native-gesture-handler';
import 'react-native-reanimated';

import {ApolloProvider} from '@apollo/client';
import CodePush from '@code-push-next/react-native-code-push';
import * as Sentry from '@sentry/react-native';
import store, {persister} from '@store/store';
import {checkForUpdate} from '@utils/InAppUpdate';
import {type AppSyncResponse, sync} from 'appsonair-react-native-appsync';
import React, {useEffect, useState} from 'react';
import {StatusBar, Text} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {KeyboardProvider} from 'react-native-keyboard-controller';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {ToastProvider} from 'react-native-toast-notifications';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/es/integration/react';

import {Tracker} from '@/app/analytics/tracker';
import {client} from '@/app/apollo/client';
import Moengage from '@/app/moengage';
import {Navigation} from '@/app/navigation/NavigationBuilder';
import {isIOS} from '@/app/utils/utils';

if (__DEV__) {
  require('./ReactotronConfig');
}

Sentry.init({
  dsn: 'https://<EMAIL>/4509349590269952',
  beforeSend: event => {
    if (__DEV__) {
      return null;
    }
    return event;
  },
});

Tracker.init();

function App() {
  const [, setData] = useState<AppSyncResponse | null>(null);
  useEffect(() => {
    Moengage.init();
    checkForUpdate();
    sync().then(res => {
      setData(res);
    });

    CodePush.sync({
      installMode: CodePush.InstallMode.IMMEDIATE,
    });
  }, []);

  return (
    <GestureHandlerRootView>
      <ApolloProvider client={client}>
        <Provider store={store}>
          <SafeAreaProvider>
            <KeyboardProvider enabled={isIOS}>
              <PersistGate persistor={persister}>
                <ToastProvider>
                  <StatusBar
                    backgroundColor={'transparent'}
                    translucent
                    barStyle={'light-content'}
                  />
                  <Navigation />
                </ToastProvider>
              </PersistGate>
            </KeyboardProvider>
          </SafeAreaProvider>
        </Provider>
      </ApolloProvider>
    </GestureHandlerRootView>
  );
}

export default Sentry.wrap(App);

/* eslint-disable @typescript-eslint/no-explicit-any */
(Text as any).defaultProps = (Text as any).defaultProps || {};
(Text as any).defaultProps.allowFontScaling = false;
/* eslint-enable @typescript-eslint/no-explicit-any */
