import {CodegenConfig} from '@graphql-codegen/cli';

import Secrets from './secrets.json';
import {GRAPH_API_URL} from './src/app/constants';

const config: CodegenConfig = {
  schema: [
    {
      [GRAPH_API_URL]: {
        headers: {
          'x-hasura-admin-secret': Secrets.HASURA_TOKEN,
        },
      },
    },
  ],
  documents: ['src/graphQL/*.{ts,tsx}'],
  generates: {
    './src/types/__generated__/': {
      preset: 'client',
      plugins: [],
      config: {
        documentMode: 'string',
      },
      presetConfig: {
        gqlTagName: 'gql',
      },
    },
  },
  ignoreNoDocuments: true,
};

export default config;
