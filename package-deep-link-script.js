const {exec} = require('child_process');
const os = require('os');

const page = process.argv[2] || ''; // Default to opening the app
const url = `uix://${page}`;

if (os.platform() === 'darwin') {
  // macOS: Assume iOS Simulator
  const command = `xcrun simctl openurl booted '${url}'`;
  exec(command, (err, stdout, stderr) => {
    if (err) {
      return;
    }
    console.log(stdout);
  });
} else {
  // Assume Android
  const command = `adb shell am start -W -a android.intent.action.VIEW -d '${url}' com.uix`;
  exec(command, (err, stdout, stderr) => {
    if (err) {
      return;
    }
    console.log(stdout);
  });
}
